// Debug script to check localStorage content
console.log('=== localStorage Debug ===');

// Check if we're in browser environment
if (typeof localStorage !== 'undefined') {
    const profileData = localStorage.getItem('profile');
  
    if (profileData) {
        console.log('Raw localStorage profile data:');
        console.log(profileData);
    
        try {
            const parsed = JSON.parse(profileData);
            console.log('\nParsed profile data:');
            console.log(JSON.stringify(parsed, null, 2));
      
            console.log('\nSchedules specifically:');
            console.log('schedules array:', parsed.schedules);
            console.log('schedules length:', parsed.schedules ? parsed.schedules.length : 'undefined');
      
            if (parsed.schedules && parsed.schedules.length > 0) {
                console.log('First schedule:', parsed.schedules[0]);
            }
        } catch (error) {
            console.error('Failed to parse profile data:', error);
        }
    } else {
        console.log('No profile data found in localStorage');
    }
} else {
    console.log('localStorage not available (not in browser environment)');
}