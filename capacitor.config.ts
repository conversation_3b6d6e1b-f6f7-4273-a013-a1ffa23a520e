import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
    appId: 'com.upliftingactions.YourBestDays',
    appName: 'YourBestDays',
    webDir: 'build',
    backgroundColor: '#000000',
    server: {
        androidScheme: 'https'
    },
    plugins: {
        App: {
            // launchUrl: 'com.upliftingactions.yourbestdays://auth/callback',
            // enable handling of your scheme
            urlScheme: 'yourbestdays',
            handleCustomScheme: true,
        },
        SplashScreen: {
            launchShowDuration: 0,
            launchAutoHide: false,
        },
    },
    ios: {
        contentInset: 'automatic'
    },
    android: {
        allowMixedContent: true,
        captureInput: true,
        webContentsDebuggingEnabled: true
    }
};

export default config;


// TODO
/*
Android (AndroidManifest.xml)
Inside your <activity>:

<intent-filter android:autoVerify="true">
  <action android:name="android.intent.action.VIEW"/>
  <category android:name="android.intent.category.DEFAULT"/>
  <category android:name="android.intent.category.BROWSABLE"/>
  <data android:scheme="yourbestdays"/>
</intent-filter>


iOS (Info.plist)
Add:

<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>yourbestdays</string>
    </array>
  </dict>
</array>

*/