{"dynamics": ["Anger-Based", "Fear-Based", "Sadness-Based", "Behavioral / Experience Loops", "Synergy / Misalignment"], "coreDynamics": [{"id": "work-stress", "name": "Work Stress", "description": "Pressure, deadlines, or workplace challenges"}, {"id": "relationship-worry", "name": "Relationship Worry", "description": "Concerns about connections with others"}, {"id": "self-doubt", "name": "<PERSON>-<PERSON><PERSON><PERSON>", "description": "Questioning your abilities or worth"}, {"id": "upcoming-change", "name": "Upcoming Change", "description": "Uncertainty about transitions or new situations"}, {"id": "financial-concern", "name": "Financial Concern", "description": "Money-related stress or security worries"}, {"id": "health-anxiety", "name": "Health Anxiety", "description": "Physical or mental health concerns"}, {"id": "family-dynamics", "name": "Family Dynamics", "description": "Challenges with family relationships or expectations"}, {"id": "life-direction", "name": "Life Direction", "description": "Uncertainty about purpose or next steps"}, {"id": "perfectionism", "name": "Perfectionism", "description": "Pressure to meet impossibly high standards"}, {"id": "social-pressure", "name": "Social Pressure", "description": "Feeling judged or needing to fit in"}], "beliefs": ["I'm not good enough", "This always happens to me", "People must like me", "I have to be perfect", "I can't trust anyone", "Nothing ever works out", "I'm too sensitive", "I don't deserve good things", "Everyone else has it figured out", "I'm behind in life", "I'm not smart enough", "I'm too old/young for this", "I don't have enough time", "I'm not worthy of love", "I always mess things up", "People will judge me", "I'm not strong enough", "I'm a burden to others", "I can't change", "Life is unfair", "I'm not creative", "I'm not attractive enough", "I don't matter", "I'm too different", "I'm not talented", "I'm unlucky", "I'm too emotional", "I'm not disciplined", "I'm not organized", "I'm not social enough"], "themes": [{"id": "calm", "name": "Calm", "description": "Finding peace and tranquility", "color": "#6B73FF"}, {"id": "clarity", "name": "Clarity", "description": "Gaining clear understanding and focus", "color": "#9333EA"}, {"id": "energy", "name": "Energy", "description": "Boosting vitality and motivation", "color": "#F59E0B"}, {"id": "release", "name": "Release", "description": "Letting go of what no longer serves", "color": "#10B981"}, {"id": "focus", "name": "Focus", "description": "Concentrating on what matters most", "color": "#EF4444"}, {"id": "growth", "name": "Growth", "description": "Expanding and evolving yourself", "color": "#8B5CF6"}, {"id": "balance", "name": "Balance", "description": "Finding harmony in all areas", "color": "#06B6D4"}, {"id": "courage", "name": "Courage", "description": "Building strength to face challenges", "color": "#F97316"}, {"id": "compassion", "name": "Compassion", "description": "Cultivating kindness for self and others", "color": "#EC4899"}, {"id": "wisdom", "name": "Wisdom", "description": "Accessing deeper understanding", "color": "#84CC16"}], "steps": [{"id": "step-1-notice", "name": "Notice", "description": "The user notices they are experiencing discomfort—this could be anxiety, nervousness, tiredness, irritation, etc. This is the signal that there is a misalignment or something internal might be worth exploring.", "flashcards": [{"id": "notice-1", "front": "What are you feeling in your body right now?", "back": "Name it—tight chest, shallow breath, heaviness, restlessness. Don't analyze—just observe.", "favorite": false}, {"id": "notice-2", "front": "What emotion is closest to this sensation?", "back": "Choose freely: anxious, irritated, flat, tense, numb. Let it be real.", "favorite": false}, {"id": "notice-3", "front": "What thought keeps replaying with this feeling?", "back": "Write the sentence. Don't judge it.", "favorite": false}, {"id": "notice-4", "front": "Could this feeling mean you're simply tired or overstimulated?", "back": "If so, can you give yourself permission to rest? If not, what belief is preventing it?", "favorite": false}, {"id": "notice-5", "front": "If this feeling had a message, what would it say?", "back": "Let the discomfort speak, like a wise friend. \"I need...\" or \"I'm scared that...\"", "favorite": false}, {"id": "notice-6", "front": "Do you feel pressure to \"fix\" this feeling fast?", "back": "If yes, pause and ask: \"What's the rush? What do I fear will happen if I sit with this?\"", "favorite": false}, {"id": "notice-7", "front": "Is this feeling new or familiar?", "back": "If familiar: When have you felt this before? What usually follows?", "favorite": false}]}, {"id": "step-2-identify", "name": "Name / Identify", "description": "The user identifies which of the 5 core dynamics is most at play. Sometimes more than one is present. Users are encouraged to resolve one at a time for clarity. At this point, the flow becomes tailored to the selected dynamic.", "flashcards": [{"id": "identify-1", "front": "Which of the 5 dynamics feels most dominant right now?", "back": "Trust your instinct. It doesn't have to be perfect.", "favorite": false}, {"id": "identify-2", "front": "Can you see how more than one dynamic might be at play?", "back": "Acknowledge the others, but commit to working with just one for now.", "favorite": false}, {"id": "identify-3", "front": "What clues point to this dynamic being dominant?", "back": "Use body, behavior, thoughts: e.g., anger = tension, fear = withdrawal, etc.", "favorite": false}, {"id": "identify-4", "front": "How do you typically react when this dynamic shows up?", "back": "Awareness of your default pattern builds power to shift.", "favorite": false}, {"id": "identify-5", "front": "What do you wish you could do when this dynamic shows up?", "back": "Write the empowered version of your response.", "favorite": false}, {"id": "identify-6", "front": "If this part of you had a protective job, what would it be?", "back": "These dynamics often emerge from survival strategies. Honor that.", "favorite": false}, {"id": "identify-7", "front": "What age or memory is connected to this pattern?", "back": "Name one early experience where this feeling or behavior began.", "favorite": false}]}, {"id": "step-3-belief-reflection", "name": "Belief Reflection", "description": "Purpose: to interrupt certainty. Helps shift from \"this belief = truth\" → \"this belief = one lens\". Creates mental flexibility before exploring any new belief or solution. Temporarily stepping outside the belief to test it from a neutral, curious stance.", "flashcards": [{"id": "belief-reflection-1", "front": "What belief feels most true in this moment?", "back": "Write it. \"People always...,\" \"I never...,\" \"It's pointless...\"", "favorite": false}, {"id": "belief-reflection-2", "front": "Whose voice does this belief sound like?", "back": "Parent, peer, culture, social media, yourself?", "favorite": false}, {"id": "belief-reflection-3", "front": "What emotion is fueled by this belief?", "back": "Anger? Shame? Helplessness? See the link.", "favorite": false}, {"id": "belief-reflection-4", "front": "Has this belief helped you in the past?", "back": "If yes, honor that. If not, that matters too.", "favorite": false}, {"id": "belief-reflection-5", "front": "When has this belief not held up in real life?", "back": "List 1–3 examples where something different happened.", "favorite": false}, {"id": "belief-reflection-6", "front": "How might your behavior change if this belief were just one possible lens?", "back": "Imagine it, even if you don't fully believe it yet.", "favorite": false}, {"id": "belief-reflection-7", "front": "What do you focus on when this belief is active?", "back": "What are you filtering out? What becomes invisible?", "favorite": false}, {"id": "belief-reflection-8", "front": "Has this belief always been true in every situation?", "back": "Look for exceptions. Even one exception creates possibility.", "favorite": false}, {"id": "belief-reflection-9", "front": "If your closest friend believed this, how would you respond to them?", "back": "Sometimes we offer others more compassion than ourselves.", "favorite": false}]}, {"id": "step-4-belief-validation", "name": "Belief Validation", "description": "Understanding the reasons behind why you believe what you believe. This explores how beliefs are born, the motivation behind them, and belief frequency - how often you've actually experienced what validates your beliefs.", "flashcards": [{"id": "belief-validation-1", "front": "What experiences validate this belief for you?", "back": "List up to 5 specific memories or experiences you can remember.", "favorite": false}, {"id": "belief-validation-2", "front": "How many times can you actually remember this happening?", "back": "Enter the specific number. Be honest about frequency vs. assumption.", "favorite": false}, {"id": "belief-validation-3", "front": "What fear or self-preservation need might this belief serve?", "back": "Beliefs often protect us from something. What might that be?", "favorite": false}, {"id": "belief-validation-4", "front": "How much of this belief comes from what others have told you?", "back": "Vs. your own direct experience. Be curious, not judgmental.", "favorite": false}, {"id": "belief-validation-5", "front": "Do you use absolute words like 'always,' 'never,' 'everybody,' or 'nobody'?", "back": "Notice how these words limit vision and create tunnel thinking.", "favorite": false}, {"id": "belief-validation-6", "front": "What would change if you replaced 'always' with 'sometimes'?", "back": "Or 'never' with 'rarely'? How does this shift feel?", "favorite": false}, {"id": "belief-validation-7", "front": "When you adopted this belief, what were you trying to avoid?", "back": "Pain? Disappointment? Rejection? Understanding the 'why' brings clarity.", "favorite": false}, {"id": "belief-validation-8", "front": "Has holding this belief cost you anything?", "back": "Opportunities? Relationships? Peace of mind? Be honest.", "favorite": false}]}, {"id": "step-5-goals-why", "name": "Goals + Why Reflection", "description": "The user reflects on what matters to them overall (ultimate life goals, life commitments, and short-term goals) and goes deeper into why each of those things matter to them. This helps reconnect to intrinsic motivation, direction and personal agency.", "flashcards": [{"id": "goals-why-1", "front": "What are your most important life goals right now?", "back": "List 3-5 things that truly matter to you. Don't overthink—trust your first instincts.", "favorite": false}, {"id": "goals-why-2", "front": "Why does your first goal matter to you?", "back": "Go deeper than the surface. What would achieving this give you or allow you to become?", "favorite": false}, {"id": "goals-why-3", "front": "What would your life feel like if you achieved this goal?", "back": "Connect to the emotional experience, not just the external outcome.", "favorite": false}, {"id": "goals-why-4", "front": "Is your current focus/situation supporting or distracting from your goals?", "back": "Sometimes our challenges are just distractions from what truly matters.", "favorite": false}, {"id": "goals-why-5", "front": "What small action could you take today toward one of your goals?", "back": "Focus on what's within your control right now.", "favorite": false}, {"id": "goals-why-6", "front": "How do your goals connect to your deeper values?", "back": "What principles or values are these goals expressing?", "favorite": false}, {"id": "goals-why-7", "front": "Which goal energizes you most when you think about it?", "back": "Notice which one creates excitement vs. obligation.", "favorite": false}, {"id": "goals-why-8", "front": "What would you regret not pursuing if you looked back in 10 years?", "back": "This can reveal what truly matters beneath surface goals.", "favorite": false}]}, {"id": "step-6-conflict-reflection", "name": "Conflict Reflection", "description": "A non-judgmental exploration of the conflicts that exist between what the user believes, why they believe it and what they are focused on, and what they truly want or value. Surfaces inner friction without blame.", "flashcards": [{"id": "conflict-reflection-1", "front": "Where do you notice tension between what you believe and what you want?", "back": "Name the specific conflict without trying to resolve it yet.", "favorite": false}, {"id": "conflict-reflection-2", "front": "What part of you wants to move forward vs. the part that holds back?", "back": "Both parts have wisdom. What is each trying to protect or achieve?", "favorite": false}, {"id": "conflict-reflection-3", "front": "How does your current focus serve your beliefs vs. your goals?", "back": "Sometimes we focus on problems because it feels safer than pursuing dreams.", "favorite": false}, {"id": "conflict-reflection-4", "front": "What would you do if you knew you couldn't fail?", "back": "This reveals what fear-based beliefs might be limiting you.", "favorite": false}, {"id": "conflict-reflection-5", "front": "What story do you tell yourself about why you can't have what you want?", "back": "Notice the narrative. Is it protecting you or limiting you?", "favorite": false}, {"id": "conflict-reflection-6", "front": "Which feels stronger right now: your desire for your goals or your attachment to your beliefs?", "back": "There's no wrong answer. Awareness is the first step.", "favorite": false}, {"id": "conflict-reflection-7", "front": "What would you need to believe to take action toward your goals?", "back": "Imagine the mindset of someone who has what you want.", "favorite": false}, {"id": "conflict-reflection-8", "front": "How might this inner conflict actually be serving you?", "back": "Sometimes conflict prevents us from taking risks. What might it be protecting?", "favorite": false}]}, {"id": "step-7-alternative-beliefs", "name": "Alternative Beliefs Exploration", "description": "The user begins embracing alternative beliefs. Purpose: to help shape a reality that better supports their goals and values. Emphasis is on freedom, not correctness.", "flashcards": [{"id": "alternative-beliefs-1", "front": "What would you need to believe to feel more free?", "back": "Focus on liberation, not just positive thinking.", "favorite": false}, {"id": "alternative-beliefs-2", "front": "If you could choose any belief about this situation, what would serve you best?", "back": "You have more choice in your beliefs than you might think.", "favorite": false}, {"id": "alternative-beliefs-3", "front": "What does someone who has overcome this challenge believe?", "back": "Model the mindset of someone who's where you want to be.", "favorite": false}, {"id": "alternative-beliefs-4", "front": "What if this challenge is actually preparing you for something better?", "back": "How might this difficulty be building strength or wisdom you'll need?", "favorite": false}, {"id": "alternative-beliefs-5", "front": "What belief would make you feel most empowered right now?", "back": "Choose based on how it makes you feel, not whether you can 'prove' it.", "favorite": false}, {"id": "alternative-beliefs-6", "front": "How would your behavior change if you fully believed in your ability to succeed?", "back": "Imagine the actions you'd take from a place of confidence.", "favorite": false}, {"id": "alternative-beliefs-7", "front": "What if you're exactly where you need to be for your growth?", "back": "How does this perspective change your relationship to your current situation?", "favorite": false}, {"id": "alternative-beliefs-8", "front": "What belief would your future self want you to adopt right now?", "back": "Connect to the wisdom of who you're becoming.", "favorite": false}]}, {"id": "step-8-new-try", "name": "New-Try", "description": "The user chooses a small action or reframe. This is them embracing their personal agency to shape what they can. Action is not forced—it's experimental and aligned with the user's deeper truth.", "flashcards": [{"id": "new-try-1", "front": "What's one small action you could take today that aligns with your new perspective?", "back": "Start tiny. The goal is momentum, not perfection.", "favorite": false}, {"id": "new-try-2", "front": "How could you reframe your current situation in a way that empowers you?", "back": "Same facts, different lens. What story serves you better?", "favorite": false}, {"id": "new-try-3", "front": "What would you do differently if you approached this with curiosity instead of fear?", "back": "Curiosity opens possibilities that fear closes off.", "favorite": false}, {"id": "new-try-4", "front": "What's one belief you're willing to experiment with for the next week?", "back": "Treat it as an experiment, not a permanent commitment.", "favorite": false}, {"id": "new-try-5", "front": "How can you honor both your growth and your need for safety?", "back": "Growth doesn't require abandoning all caution. Find the balance.", "favorite": false}, {"id": "new-try-6", "front": "What would 'good enough' progress look like?", "back": "Sometimes perfectionism prevents any action. What's the minimum viable step?", "favorite": false}, {"id": "new-try-7", "front": "How will you celebrate taking this step, regardless of the outcome?", "back": "Acknowledge your courage to try something new.", "favorite": false}, {"id": "new-try-8", "front": "What support do you need to maintain this new perspective?", "back": "Change is easier with support. What resources can you access?", "favorite": false}]}]}