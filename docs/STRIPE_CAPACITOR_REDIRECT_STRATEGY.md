# Stripe Checkout Redirect Strategy for Capacitor Apps

## Overview

This document explains the strategy for handling Stripe checkout redirects in a Capacitor-based hybrid mobile app. The core challenge is that <PERSON>e requires valid HTTP/HTTPS URLs for redirects, but mobile apps need to redirect back to the app using custom URL schemes.

## The Problem

When implementing Stripe checkout in a Capacitor app:

1. **Web deployment**: `req.headers.get('origin')` returns a valid HTTP/HTTPS URL (e.g., `https://yourapp.com`)
2. **Capacitor/iOS**: `req.headers.get('origin')` returns `capacitor://localhost` or similar
3. **Stripe requirement**: Only accepts valid HTTP/HTTPS URLs for `success_url` and `cancel_url`
4. **iOS Safari error**: "<PERSON><PERSON> cannot open the page because the address is invalid"

## The Solution: Conditional Redirect Strategy

We use a **conditional redirect strategy** that:

- **For web deployments**: Uses the actual origin for direct redirects within the same domain
- **For Capacitor/native apps**: Uses an intermediate web page that detects the platform and redirects to the custom URL scheme

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Capacitor     │    │   Stripe         │    │  Intermediate       │
│   App           │───▶│   Checkout       │───▶│  Redirect Page      │
│                 │    │                  │    │  (upliftingactions) │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                                          │
                                                          ▼
                                               ┌─────────────────────┐
                                               │   Custom URL        │
                                               │   Scheme Redirect   │
                                               │   yourbestdays://   │
                                               └─────────────────────┘
                                                          │
                                                          ▼
                                               ┌─────────────────────┐
                                               │   App Deep Link     │
                                               │   Handler           │
                                               └─────────────────────┘
```

## Implementation Details

### 1. iOS URL Scheme Configuration

**File**: `ios/App/App/Info.plist`

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>yourbestdays</string>
        </array>
    </dict>
</array>
```

### 2. Capacitor Configuration

**File**: `capacitor.config.ts`

```typescript
const config: CapacitorConfig = {
    appId: 'com.upliftingactions.YourBestDays',
    appName: 'YourBestDays',
    webDir: 'build',
    plugins: {
        App: {
            urlScheme: 'yourbestdays',
            handleCustomScheme: true,
        },
    },
};
```

### 3. Frontend: Capacitor Detection and API Call

**File**: `src/lib/services/subscriptionService.ts`

```typescript
export async function createCheckoutSession(priceId: string) {
    try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session || !session.user) throw new Error('Not authenticated');

        // Detect if we're running in Capacitor
        const isCapacitor = (window as any).Capacitor !== undefined;

        const { data, error } = await supabase.functions.invoke('create-checkout-session', {
            body: {
                priceId,
                userId: session.user.id,
                userEmail: session.user.email,
                isCapacitor  // Pass platform detection to backend
            },
            headers: {
                Authorization: `Bearer ${session.access_token}`
            }
        });

        if (error) {
            return {
                error: 'Payment system is currently unavailable. Please try again later.',
                url: null
            };
        }

        return {
            error: null,
            url: data?.url || null
        };
    } catch (error) {
        console.error('Error creating checkout session:', error);
        return {
            error: 'Unable to start checkout process. Please ensure you are logged in and try again.',
            url: null
        };
    }
}
```

### 4. Backend: Conditional URL Generation

**File**: `supabase/functions/create-checkout-session/index.ts`

```typescript
Deno.serve(async (req) => {
    try {
        // Parse request body
        const { priceId, couponId, successUrl, cancelUrl, isCapacitor } = await req.json()

        // ... authentication and customer creation logic ...

        // Create Stripe Checkout Session with conditional URLs
        const sessionConfig: StripeTypes.Checkout.SessionCreateParams = {
            customer: customerId,
            payment_method_types: ['card'],
            mode: 'subscription',
            line_items: [
                {
                    price: priceId,
                    quantity: 1,
                },
            ],
            // Conditional redirect URLs based on platform
            success_url: successUrl || (isCapacitor 
                ? 'https://www.upliftingactions.com/payment/ybd-app/success?session_id={CHECKOUT_SESSION_ID}'
                : `${req.headers.get('origin')}/payment/ybd-app/success?session_id={CHECKOUT_SESSION_ID}`),
            cancel_url: cancelUrl || (isCapacitor 
                ? 'https://www.upliftingactions.com/payment/ybd-app/cancel'
                : `${req.headers.get('origin')}/payment/ybd-app/cancel`),
            client_reference_id: user.id,
            metadata: {
                user_id: user.id,
            },
            subscription_data: {
                metadata: {
                    user_id: user.id,
                },
            },
            allow_promotion_codes: true,
        }

        const session = await stripe.checkout.sessions.create(sessionConfig)

        return createCorsResponse({
            url: session.url,
            sessionId: session.id,
        })
    } catch (err) {
        console.error("Error: creating-checkout-session:", err)
        return createErrorResponse("Internal server error", 500)
    }
})
```

### 5. Intermediate Redirect Page (Success)

**File**: `https://www.upliftingactions.com/payment/ybd-app/success`

```svelte
<script lang="ts">
    import { onMount } from 'svelte';

    let showCloseMessage = false;
    let isNativeApp = false;

    onMount(() => {
        // Check if running in Capacitor native app
        isNativeApp = !!(window as any).Capacitor ||
                      navigator.userAgent.includes('CapacitorWebView') ||
                      (window as any).webkit?.messageHandlers;

        if (isNativeApp) {
            // Native app - redirect using custom URL scheme
            const params = new URLSearchParams(window.location.search);
            const sessionId = params.get('session_id');
            const target = `yourbestdays://payment/ybd-app/success?session_id=${sessionId}`;

            // Redirect to app
            window.location.replace(target);

            // Fallback after 3 seconds if redirect didn't work
            setTimeout(() => {
                showCloseMessage = true;
            }, 3000);
        }
    });

    function closeWindow() {
        showCloseMessage = true;
    }
</script>

<svelte:head>
    <title>Payment Successful - Your Best Days App</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center p-8 bg-gray-100">
    <div class="bg-white rounded-xl max-w-lg w-full text-center shadow-lg">
        <div class="p-8">
            {#if isNativeApp}
                <!-- Native app redirect message -->
                <h1 class="text-3xl font-bold text-gray-900 mb-4">Payment Successful!</h1>
                <p class="text-gray-600 mb-4">Redirecting back to the app…</p>

                {#if showCloseMessage}
                    <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p class="text-sm text-blue-700">
                            If the app didn't open automatically, please open Your Best Days app manually.
                        </p>
                    </div>
                {/if}
            {:else}
                <!-- Web browser message -->
                <h1 class="text-3xl font-bold text-gray-900 mb-4">Payment Successful!</h1>
                <p class="text-gray-600 mb-4">
                    Your payment for Your Best Days app subscription has been completed successfully.
                </p>
                <button on:click={closeWindow}
                        class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Close Window
                </button>

                {#if showCloseMessage}
                    <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p class="text-sm text-blue-700">
                            Please close this tab manually or return to the Your Best Days app.
                        </p>
                    </div>
                {/if}
            {/if}
        </div>
    </div>
</div>
```

### 6. App Deep Link Handler

**File**: `src/routes/+layout.svelte`

```typescript
import { onMount } from 'svelte';
import { goto } from '$app/navigation';
import { browser } from '$app/environment';
import { App as CapApp } from '@capacitor/app';

function handleDeepLink(url: string) {
    const parsed = new URL(url);

    switch (parsed.pathname) {
        case '/payment/ybd-app/success': {
            const sessionId = parsed.searchParams.get('session_id');
            goto(`/payment/ybd-app/confirm?session_id=${sessionId}`);
            break;
        }
        case '/payment/ybd-app/cancel':
            goto('/pricing');
            break;
        case '/auth/callback':
            goto(`/auth/callback${parsed.search}`);
            break;
    }
}

onMount(() => {
    if (!browser) return;

    // 1) Cold-start deep-link (app not already running)
    CapApp.getLaunchUrl().then(info => {
        if (info?.url) {
            handleDeepLink(info.url);
        }
    });

    // 2) Runtime deep-links (app already in foreground/background)
    CapApp.addListener('appUrlOpen', ({ url }) => {
        handleDeepLink(url);
    });
});
```

## Flow Diagram

### Web Flow
```
User clicks "Subscribe" → Edge Function → Stripe Checkout →
https://yourapp.com/payment/ybd-app/success → App Success Page
```

### Capacitor/Native Flow
```
User clicks "Subscribe" → Edge Function → Stripe Checkout →
https://www.upliftingactions.com/payment/ybd-app/success →
Platform Detection → yourbestdays://payment/ybd-app/success →
Deep Link Handler → App Success Page
```

## Benefits

1. **Stripe Compliance**: Always uses valid HTTPS URLs
2. **Universal Compatibility**: Works for both web and native deployments
3. **Reliable Platform Detection**: Multiple detection methods for robustness
4. **Better UX**: Appropriate messaging for each platform
5. **Easy Testing**: Can test intermediate pages independently
6. **Future-Proof**: Easy to extend for additional platforms

## Testing

### Web Testing
1. Deploy app to web hosting
2. Test payment flow - should redirect directly to your domain

### iOS Testing
1. Build and install app: `npx cap sync ios && npx cap build ios`
2. Test payment flow - should redirect through intermediate page back to app
3. Verify deep link handling works correctly

## Troubleshooting

- **"Safari cannot open page"**: Check iOS URL scheme configuration
- **Redirect not working**: Verify intermediate page platform detection
- **Deep links not handled**: Check Capacitor App plugin setup
- **Web redirects failing**: Verify origin header handling in edge function
```
