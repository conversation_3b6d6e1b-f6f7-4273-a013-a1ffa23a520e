This page should be hosted page that successful payment redirects users to after they use a stripe hosted checkout session. On that page i should include instructions like:

- Close this page and return to the app. Your subscription should be updated automatically.
- Review 


### Example native only capacitor rendering that only renders if on native device (not web)

```html
<!-- in script, import { Capacitor } from '@capacitor/core'; -->

  {#if Capacitor.isNativePlatform()}
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
          <h3 class="text-sm font-semibold text-blue-900 mb-3">📱 Mobile Payment Instructions</h3>
          <ul class="text text-blue-800 space-y-2">
              <li>- Complete your payment in the secure browser window</li>
              <li>- <strong>Close the browser</strong> when you see "Thanks for subscribing"</li>
              <li>3️⃣ The app will automatically check your subscription</li>
              <li>4️⃣ If stuck, use the button below to check manually</li>
          </ul>
          
          <div class="mt-4 pt-3 border-t border-blue-200">
              <button
                  on:click={() => goto('/payment/check')}
                  class="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
              >
                  🔍 Restore My Subscription
              </button>
          </div>
      </div>
  {/if}
```





```html
<!-- src/routes/success/+page.svelte -->
<script lang="ts">
  import { onMount } from 'svelte'
  import { page } from '$app/stores'
  import { supabase } from '$lib/supabase'
  import { SubscriptionService } from '$lib/services/subscriptionService'

  let loading = true
  let sessionId: string | null = null
  let subscriptionStatus: any = null
  let user: any = null

  onMount(async () => {
    // Get session ID from URL
    sessionId = $page.url.searchParams.get('session_id')
    
    // Get current user
    const { data: { user: currentUser } } = await supabase.auth.getUser()
    user = currentUser

    if (user) {
      // Check updated subscription status
      subscriptionStatus = await SubscriptionService.checkSubscriptionStatus(user)
    }

    loading = false
  })

  function goToApp() {
    window.location.href = '/app' // or wherever your main app is
  }

  function goToPricing() {
    window.location.href = '/pricing'
  }
</script>

<div class="success-container">
  {#if loading}
    <div class="loading">
      <div class="spinner"></div>
      <p>Confirming your subscription...</p>
    </div>
  {:else if subscriptionStatus?.hasActiveSubscription}
    <div class="success">
      <div class="check-icon">✓</div>
      <h1>Welcome to Premium!</h1>
      <p>Your subscription has been activated successfully.</p>
      
      <div class="subscription-details">
        <h3>Subscription Details</h3>
        <p><strong>Status:</strong> {subscriptionStatus.subscription.status}</p>
        <p><strong>Started:</strong> {new Date(subscriptionStatus.subscription.created_at).toLocaleDateString()}</p>
        {#if sessionId}
          <p><strong>Session ID:</strong> {sessionId}</p>
        {/if}
      </div>

      <div class="actions">
        <button on:click={goToApp} class="primary-btn">
          Start Using Premium Features
        </button>
        <button on:click={goToPricing} class="secondary-btn">
          View Pricing
        </button>
      </div>
    </div>
  {:else}
    <div class="pending">
      <h2>Processing Your Subscription</h2>
      <p>Your payment was successful, but we're still setting up your subscription.</p>
      <p>This usually takes just a few moments. You'll receive an email confirmation shortly.</p>
      
      <div class="actions">
        <button on:click={() => window.location.reload()} class="primary-btn">
          Check Again
        </button>
        <button on:click={goToPricing} class="secondary-btn">
          Back to Pricing
        </button>
      </div>
    </div>
  {/if}
</div>

```