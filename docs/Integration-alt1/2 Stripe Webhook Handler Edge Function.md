## 2. Stripe Webhook Handler

### Create Webhook Edge Function
```bash
supabase functions new stripe-webhook
```

### Webhook Handler Code (`supabase/functions/stripe-webhook/index.ts`)### Webhook Environment Variables
Add to Supabase:
```bash
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_from_stripe
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```


```js
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.9.0?target=deno'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '' // Use service role for webhook
)

Deno.serve(async (req) => {
  const signature = req.headers.get('stripe-signature')
  const body = await req.text()
  
  if (!signature) {
    return new Response('No signature', { status: 400 })
  }

  try {
    // Verify webhook signature
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      Deno.env.get('STRIPE_WEBHOOK_SECRET') ?? ''
    )

    console.log(`Received event: ${event.type}`)

    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object)
        break
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object)
        break
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object)
        break
      
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object)
        break
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object)
        break
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object)
        break
      
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(`Webhook error: ${error.message}`, {
      status: 400,
    })
  }
})

async function handleCheckoutSessionCompleted(session: any) {
  console.log('Processing checkout.session.completed:', session.id)
  
  const userId = session.client_reference_id
  const customerId = session.customer
  const subscriptionId = session.subscription

  if (!userId) {
    console.error('No client_reference_id found in session')
    return
  }

  // Update user profile with customer ID if not already set
  if (customerId) {
    await supabaseClient
      .from('profiles')
      .update({ stripe_customer_id: customerId })
      .eq('id', userId)
  }

  // If this is a subscription, the subscription events will handle the rest
  if (subscriptionId) {
    console.log('Subscription checkout completed, waiting for subscription events')
    return
  }

  // Handle one-time payments if needed
  console.log('One-time payment completed for user:', userId)
}

async function handleSubscriptionCreated(subscription: any) {
  console.log('Processing customer.subscription.created:', subscription.id)
  
  const userId = subscription.metadata?.user_id
  if (!userId) {
    console.error('No user_id found in subscription metadata')
    return
  }

  // Create subscription record
  const { error } = await supabaseClient
    .from('user_subscriptions')
    .insert({
      user_id: userId,
      subscription_id: subscription.id,
      status: subscription.status,
    })

  if (error) {
    console.error('Error creating subscription record:', error)
  } else {
    console.log('Subscription created successfully for user:', userId)
  }
}

async function handleSubscriptionUpdated(subscription: any) {
  console.log('Processing customer.subscription.updated:', subscription.id)
  
  // Update subscription status
  const { error } = await supabaseClient
    .from('user_subscriptions')
    .update({
      status: subscription.status,
      updated_at: new Date().toISOString(),
    })
    .eq('subscription_id', subscription.id)

  if (error) {
    console.error('Error updating subscription:', error)
  } else {
    console.log('Subscription updated successfully:', subscription.id)
  }
}

async function handleSubscriptionDeleted(subscription: any) {
  console.log('Processing customer.subscription.deleted:', subscription.id)
  
  // Update subscription status to cancelled
  const { error } = await supabaseClient
    .from('user_subscriptions')
    .update({
      status: 'cancelled',
      updated_at: new Date().toISOString(),
    })
    .eq('subscription_id', subscription.id)

  if (error) {
    console.error('Error updating subscription to cancelled:', error)
  } else {
    console.log('Subscription cancelled successfully:', subscription.id)
  }
}

async function handleInvoicePaymentSucceeded(invoice: any) {
  console.log('Processing invoice.payment_succeeded:', invoice.id)
  
  if (invoice.subscription) {
    // Update subscription status to active
    const { error } = await supabaseClient
      .from('user_subscriptions')
      .update({
        status: 'active',
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', invoice.subscription)

    if (error) {
      console.error('Error updating subscription to active:', error)
    } else {
      console.log('Subscription activated after payment success:', invoice.subscription)
    }
  }
}

async function handleInvoicePaymentFailed(invoice: any) {
  console.log('Processing invoice.payment_failed:', invoice.id)
  
  if (invoice.subscription) {
    // Update subscription status to past_due
    const { error } = await supabaseClient
      .from('user_subscriptions')
      .update({
        status: 'past_due',
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', invoice.subscription)

    if (error) {
      console.error('Error updating subscription to past_due:', error)
    } else {
      console.log('Subscription marked as past_due after payment failure:', invoice.subscription)
    }
  }
}
```
