# 🔐 Finalized Flow: Supabase Magic Link + Stripe Customer Portal + Subscription-Based Feature Access

This document outlines a fully hosted, passwordless authentication and subscription billing flow using:

- Supabase (magic link auth)
- Stripe Customer Portal (no custom billing UI)
- Webhooks (to track active subscriptions)
- Capacitor + SvelteKit (mobile app frontend)

---

## 📌 Prerequisite: Scope Features Based on Subscription Status

**Important:** Your app currently does not lock or unlock features based on whether a user has an active paid subscription.

You will need to implement feature gating so that:

- **Free users** only see or access a limited set of features.
- **Paid users** (those with an active Stripe subscription) get access to all features.

---

### Suggested Implementation (to be completed):

You now have two related tables:

- `public.profiles`: Stores user profile info and Stripe customer ID
- `public.user_subscriptions`: Tracks subscription ID and status per user

To implement subscription-based feature gating:

1. **In your app**:
   - After the user logs in, fetch their `user_subscriptions.status` value.
   - Define which `status` values unlock full access (e.g., `active`, `trialing`).

2. **In the database**:
   - Keep the `user_subscriptions.status` field updated via Stripe webhooks.
   - Optionally denormalize a simple flag (e.g., `is_subscribed`) in the `profiles` table for easy querying in your app.

---

## 🧾 Current Supabase Schema

### `public.profiles`

```sql
create table public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  stripe_customer_id text unique,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  early_adopter BOOLEAN DEFAULT FALSE,
  wizard_completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

### `public.user_subscriptions`

```sql
create table public.user_subscriptions (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users(id),
  subscription_id text unique,
  status text,
  created_at timestamp default now(),
  updated_at timestamp default now()
);
```

### 📌 Notes:

* `stripe_customer_id` connects your users to Stripe.
* `subscription_id` maps to Stripe’s subscription object.
* `status` should mirror the current Stripe subscription status (`active`, `canceled`, `incomplete`, etc.).
* Use the `status` field to drive feature gating logic.

---

## ✅ Flow Summary

### Step 1: User signs up or signs in (via Supabase magic link)

* User enters email into your in-app form.
* Supabase sends a magic link email.
* User clicks the link and is redirected back to the app (`redirectTo` set to your custom app URL or deep link).
* App receives a valid Supabase session.

---

### Step 2: App checks user’s subscription status

* After authentication, query Supabase for the user's latest `user_subscriptions.status`.
* If `status != 'active'`:

  * Show limited features.
  * Display a "Manage Subscription" button.

* If `status == 'active'` or `trialing`:

  * Unlock full feature set.

---

### Step 3: User taps "Manage Subscription" → Create Stripe Customer Portal session

* Your app calls a **Supabase Edge Function** (or serverless API) that:

  * Looks up or creates a Stripe customer for the user.
  * Calls `stripe.billingPortal.sessions.create()` with a `return_url`.

```ts
const session = await stripe.billingPortal.sessions.create({
  customer: stripeCustomerId,
  return_url: 'myapp://return' // deep link back to your app, only if this can be done well - bug free, otherwise its better to just redirect user to page that tells them thank you for updating subscription and they can close the web page and return to the app.
});
```

* Return `session.url` to the app.

---

### Step 4: App opens Stripe Customer Portal

* Use Capacitor’s in-app browser to open the URL:

```ts
import { Browser } from '@capacitor/browser';
await Browser.open({ url: sessionUrl });
```

* User manages their plan, upgrades, or cancels.

---

### Step 5: Stripe Webhook updates subscription status

Your backend or Supabase Edge Function listens to:

* `customer.subscription.created`
* `customer.subscription.updated`
* `customer.subscription.deleted`
* `invoice.paid`, `invoice.payment_failed`

On each event:

* Look up the user by Stripe `customer` or metadata.
* Insert or update the relevant row in `user_subscriptions`:

  * Update `status`
  * Store `subscription_id`
* (Optional) Set `profiles.is_subscribed = true|false` for easier frontend logic.

---

### Step 6: App refreshes and adjusts feature access

* On next login or better yet callback/listener in app so its automatic, the app checks the latest `user_subscriptions.status` (or denormalized `is_subscribed`).
* Show or hide premium features based on this value.

---

## 🔧 Optional Enhancements

* Use `myapp://return` or other deep links to bring the user back into the app after managing subscriptions only if it can be done confidently and bug free.
* Store subscription start/renewal/expiration timestamps if needed.
* Display current subscription status in a Settings or Account screen.

---

## 📎 Key Benefits of This Flow

| Feature                  | Why It Works Well                                         |
| ------------------------ | --------------------------------------------------------- |
| Supabase magic link auth | Fully passwordless, no passwords stored                   |
| Stripe Customer Portal   | No need to build billing UI or manage plans               |
| Stripe webhooks          | Keeps user access synced even if user never returns       |
| Clean schema separation  | `profiles` for identity, `user_subscriptions` for billing |
| Capacitor in-app browser | Smooth UX for mobile users                                |

---
