# Subscription Gating Implementation Guide

## Overview

This document outlines the recommended approach for implementing subscription-based feature gating in YourBestDays. The system ensures all features require authentication, with most features additionally requiring an active subscription.

## Current Architecture

### Database Schema
```sql
-- User profiles with Stripe integration
profiles:
  - id (UUID, references auth.users)
  - stripe_customer_id (unique)
  - email, first_name, last_name
  - early_adopter (boolean)
  - wizard_completed (boolean)

-- Subscription tracking
user_subscriptions:
  - id (UUID)
  - user_id (references auth.users)
  - subscription_id (Stripe subscription ID)
  - status (active, canceled, past_due, etc.)
  - created_at, updated_at
```

### Existing Infrastructure

1. **Authentication**: Handled via Supabase auth with session management
2. **Route Guards**: App layout (`/app/+layout.ts`) already checks subscription status
3. **Subscription Service**: Currently a dummy implementation that needs completion

## Recommended Implementation Strategy

### Option 1: Route-Based Gating (Recommended)

Instead of creating a `/free` route prefix, implement a more flexible approach:

#### 1. **Feature Flags Configuration**

Create a centralized feature configuration:

```typescript
// src/lib/config/features.ts
export const FEATURES = {
  // Core features (always free)
  AUTH: { requiresSubscription: false },
  PROFILE: { requiresSubscription: false },
  ONBOARDING: { requiresSubscription: false },
  
  // Premium features
  BELIEFS_EXPLORER: { requiresSubscription: true },
  YBD_MESSAGES: { requiresSubscription: true },
  CUSTOM_SCHEDULES: { requiresSubscription: true },
  AUDIO_MESSAGES: { requiresSubscription: true },
  
  // Limited free features
  BASIC_MESSAGES: { 
    requiresSubscription: false,
    limits: { messagesPerDay: 3 }
  }
} as const;
```

#### 2. **Update Subscription Service**

Implement the actual subscription checking:

```typescript
// src/lib/services/subscriptionService.ts
import { supabase } from '$lib/supabaseClient';

export async function checkSubscriptionStatus(userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .select('status')
      .eq('user_id', userId)
      .single();
    
    if (error || !data) return false;
    
    return ['active', 'trialing'].includes(data.status);
  } catch {
    return false;
  }
}

export async function getSubscriptionDetails(userId: string) {
  const { data } = await supabase
    .from('user_subscriptions')
    .select('*')
    .eq('user_id', userId)
    .single();
  
  return data;
}
```

#### 3. **Route-Level Protection**

Modify the app layout to handle free vs premium routes:

```typescript
// src/routes/app/+layout.ts
import { FEATURES } from '$lib/config/features';

export const load = async ({ url, locals }) => {
  // Existing auth check...
  
  // Determine if current route requires subscription
  const pathname = url.pathname;
  const requiresSubscription = !isFreeRoute(pathname);
  
  if (requiresSubscription) {
    const hasSubscription = await checkSubscriptionStatus(session.user.id);
    if (!hasSubscription) {
      throw redirect(302, '/pricing?reason=subscription_required');
    }
  }
  
  return {
    user: session.user,
    session,
    subscription: await getSubscriptionDetails(session.user.id)
  };
};

function isFreeRoute(pathname: string): boolean {
  // Define free routes
  const freeRoutes = [
    '/app/profile',
    '/app/onboarding',
    '/app/settings',
    '/pricing',
    '/app/free-trial' // If you want a dedicated free area
  ];
  
  return freeRoutes.some(route => pathname.startsWith(route));
}
```

#### 4. **Component-Level Feature Gating**

Create a reusable component for feature gating:

```svelte
<!-- src/lib/components/SubscriptionGate.svelte -->
<script lang="ts">
  import { page } from '$app/stores';
  import { Button } from '$lib/components/ui/button';
  
  export let feature: keyof typeof FEATURES;
  export let showUpgradePrompt = true;
  
  $: subscription = $page.data.subscription;
  $: hasAccess = !FEATURES[feature].requiresSubscription || subscription?.status === 'active';
</script>

{#if hasAccess}
  <slot />
{:else if showUpgradePrompt}
  <div class="text-center p-8">
    <h3 class="text-lg font-semibold mb-2">Premium Feature</h3>
    <p class="text-muted-foreground mb-4">
      Upgrade to access this feature and unlock your best days.
    </p>
    <Button href="/pricing">View Plans</Button>
  </div>
{/if}
```

Usage:
```svelte
<SubscriptionGate feature="BELIEFS_EXPLORER">
  <BeliefsExplorerComponent />
</SubscriptionGate>
```

### Option 2: Middleware-Based Approach

If you prefer more centralized control:

#### 1. **Create Subscription Middleware**

```typescript
// src/hooks.server.ts
import { checkSubscriptionStatus } from '$lib/services/subscriptionService';
import { redirect } from '@sveltejs/kit';

export const handle = async ({ event, resolve }) => {
  const session = await event.locals.getSession();
  
  if (session && event.url.pathname.startsWith('/app')) {
    const subscription = await checkSubscriptionStatus(session.user.id);
    event.locals.subscription = subscription;
    
    // Check if route requires subscription
    if (requiresSubscription(event.url.pathname) && !subscription) {
      throw redirect(302, '/pricing?reason=subscription_required');
    }
  }
  
  return resolve(event);
};
```

## Free vs Premium Features Organization

### Recommended Structure

1. **Free Features** (always accessible after login):
   - Profile management
   - Basic settings
   - Onboarding/wizard
   - Limited daily messages (e.g., 3 per day)
   - View pricing/upgrade

2. **Premium Features**:
   - Unlimited YBD messages
   - Beliefs explorer
   - Custom schedules
   - Audio messages
   - Advanced analytics
   - Priority support

### Implementation Examples

#### Free Feature with Limits
```typescript
// src/routes/app/messages/+page.server.ts
export const load = async ({ locals }) => {
  const { user, subscription } = locals;
  
  const dailyLimit = subscription?.status === 'active' ? null : 3;
  const messagesViewed = await getMessagesViewedToday(user.id);
  
  return {
    messages: await getMessages(user.id),
    dailyLimit,
    messagesViewed,
    canViewMore: !dailyLimit || messagesViewed < dailyLimit
  };
};
```

#### UI Indication of Premium Features
```svelte
<!-- In navigation or feature lists -->
{#each menuItems as item}
  <a href={item.href} class="flex items-center gap-2">
    {item.label}
    {#if item.premium && !$page.data.subscription}
      <Badge variant="secondary">PRO</Badge>
    {/if}
  </a>
{/each}
```

## Implementation Checklist

1. **Backend Setup**
   - [ ] Implement real subscription service with Supabase queries
   - [ ] Add subscription status caching (Redis/in-memory)
   - [ ] Set up Stripe webhook handlers for subscription events
   - [ ] Create subscription sync function for Stripe → Database

2. **Route Protection**
   - [ ] Update app layout with subscription checks
   - [ ] Define free vs premium routes
   - [ ] Add subscription data to page store
   - [ ] Handle edge cases (expired trials, payment failures)

3. **UI Components**
   - [ ] Create SubscriptionGate component
   - [ ] Add upgrade prompts to locked features
   - [ ] Show subscription status in user profile
   - [ ] Implement usage limits UI for free features

4. **Testing**
   - [ ] Test subscription flow end-to-end
   - [ ] Verify free features remain accessible
   - [ ] Test subscription expiration handling
   - [ ] Ensure graceful degradation for errors

## Best Practices

1. **Performance**: Cache subscription status to avoid repeated database queries
2. **UX**: Show clear upgrade paths when users hit paywalls
3. **Flexibility**: Use feature flags for easy A/B testing of free vs premium features
4. **Security**: Always verify subscription server-side, never trust client-side checks
5. **Graceful Degradation**: Handle subscription service failures without blocking all access

## Alternative Approaches Considered

### Why Not `/free` Routes?

While creating `/free` routes was considered, it has several drawbacks:
- Requires duplicating route structures
- Makes it harder to convert features from free to premium
- Creates confusion in navigation and URLs
- Limits flexibility for freemium features with usage limits

The recommended approach using feature flags and route guards provides more flexibility and better maintainability.