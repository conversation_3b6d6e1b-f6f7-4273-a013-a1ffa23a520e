# YourBestDays Notification System Architecture Plan

## Overview

This document outlines the architecture and implementation plan for a notification system that generates personalized message lists based on user preferences and schedules. The system creates a 24-hour notification queue that updates daily and regenerates when user profiles change.

## Current App Analysis

### Existing Data Structures

**Profile Store** ([`src/lib/stores/profile.ts`](../src/lib/stores/profile.ts))
- Contains user's Most Wanted Experiences (MWE) in `experiences` array
- Contains user's obstacles in `obstacles` array  
- Contains user's notification schedules in `schedules` array
- Users can select up to 5 MWE and 5 obstacles from predefined lists

**YBD Messages** ([`src/lib/data/ybd_messages.json`](../src/lib/data/ybd_messages.json))
- Rich message data with titles, content, action items, quotes
- Each message has tags that can be matched to user selections
- 962 lines of motivational content with various themes

**Schedule System** ([`src/routes/wizard/schedule/+page.svelte`](../src/routes/wizard/schedule/+page.svelte))
- Complex time ranges with specific days of the week
- Multiple time ranges per schedule with `maxPerHour` frequency limits
- Support for multiple schedules with different day/time combinations

### Key Requirements

1. **Exact Keyword Matching**: Match user's MWE and obstacles to message tags using exact keyword matching
2. **Random Selection**: Randomly select from matching messages to avoid repetition
3. **24-Hour Window**: Generate notifications for the next 24 hours only
4. **Daily Regeneration**: Update the list every 24 hours automatically
5. **Profile Change Triggers**: Regenerate immediately when user updates their profile
6. **localStorage Persistence**: Store with timestamp checking and regenerate when expired
7. **Native App Integration**: Provide interface for iOS/Android push notifications

## System Architecture

```mermaid
graph TD
    A[User Profile Changes] --> B[Notification Service]
    C[24-Hour Timer] --> B
    B --> D[Message Matcher]
    D --> E[YBD Messages JSON]
    D --> F[User MWE & Obstacles]
    B --> G[Schedule Processor]
    G --> H[User Schedules]
    G --> I[Time Slot Generator]
    I --> J[Notification Queue]
    J --> K[localStorage Cache]
    L[Native App] --> K
    
    style B fill:#e1f5fe
    style J fill:#f3e5f5
    style K fill:#e8f5e8
```

## Core Components

### 1. Notification Service (`src/lib/services/notificationService.ts`)

**Purpose**: Central orchestrator for notification generation

**Responsibilities**:
- Check cache validity (24-hour expiration + profile changes)
- Coordinate message matching and scheduling
- Manage localStorage persistence
- Provide public API for the app
- Handle automatic regeneration triggers

**Key Methods**:
```typescript
class NotificationService {
  async ensureValidQueue(): Promise<NotificationItem[]>
  async regenerateQueue(): Promise<NotificationItem[]>
  async getUpcomingNotifications(hours: number): Promise<NotificationItem[]>
  markNotificationSent(id: string): void
  clearCache(): void
}
```

### 2. Message Matcher (`src/lib/services/messageMatcher.ts`)

**Purpose**: Match user preferences to available messages

**Algorithm**:
1. Extract keywords from user's MWE and obstacles (convert to lowercase)
2. Find messages with tags matching these keywords (exact match)
3. Prioritize messages:
   - **High Priority**: Messages matching both MWE + obstacles
   - **Medium Priority**: Messages matching MWE-only
   - **Low Priority**: Messages matching obstacles-only
4. Random selection within priority groups to avoid repetition
5. Fallback to general motivational messages if no matches found

**Key Methods**:
```typescript
class MessageMatcher {
  matchMessages(userProfile: Profile): MatchedMessage[]
  private extractUserKeywords(profile: Profile): string[]
  private categorizeMessagesByPriority(messages: YBDMessage[], keywords: string[]): PrioritizedMessages
  private selectRandomMessage(messages: YBDMessage[]): YBDMessage
}
```

### 3. Schedule Processor (`src/lib/services/scheduleProcessor.ts`)

**Purpose**: Convert user schedules into specific notification times

**Logic**:
1. Process each schedule's days and time ranges
2. Generate time slots within each range respecting `maxPerHour` limits
3. Create specific timestamps for the next 24 hours only
4. Handle schedule overlaps and conflicts
5. Distribute notifications evenly within time ranges

**Key Methods**:
```typescript
class ScheduleProcessor {
  generateTimeSlots(schedules: Schedule[], startTime: Date): TimeSlot[]
  private processSchedule(schedule: Schedule, startTime: Date): TimeSlot[]
  private generateSlotsForTimeRange(range: TimeRange, days: string[], startTime: Date): TimeSlot[]
  private distributeNotificationsInRange(startHour: number, endHour: number, maxPerHour: number): Date[]
}
```

### 4. Notification Queue Store (`src/lib/stores/notificationQueue.ts`)

**Purpose**: Svelte store for managing the notification list

**Features**:
- Reactive updates when profile changes
- Integration with localStorage
- Automatic regeneration triggers
- Subscription to profile store changes

**Store Structure**:
```typescript
export const notificationQueue = writable<NotificationItem[]>([]);
export const queueStatus = writable<'loading' | 'ready' | 'error'>('loading');
export const lastGenerated = writable<Date | null>(null);
```

## Data Structures

### NotificationItem
```typescript
interface NotificationItem {
  id: string;
  scheduledTime: Date;
  message: {
    id: string;
    title: string;
    content: string;
    tags: string[];
    actionItems?: ActionItem[];
    quote?: Quote;
  };
  matchedKeywords: string[]; // Which MWE/obstacles triggered this
  priority: 'high' | 'medium' | 'low'; // Based on match quality
  sent: boolean; // Tracking for native app
  read: boolean; // User interaction tracking
}
```

### NotificationCache
```typescript
interface NotificationCache {
  generatedAt: Date;
  expiresAt: Date;
  profileHash: string; // To detect profile changes
  notifications: NotificationItem[];
  nextRegenerationTime: Date;
  version: string; // For cache format versioning
}
```

### TimeSlot
```typescript
interface TimeSlot {
  time: Date;
  scheduleId: number;
  timeRangeId: number;
  priority: 'high' | 'medium' | 'low';
}
```

### MatchedMessage
```typescript
interface MatchedMessage {
  message: YBDMessage;
  matchedKeywords: string[];
  priority: 'high' | 'medium' | 'low';
  matchType: 'both' | 'mwe' | 'obstacle' | 'fallback';
}
```

## Implementation Flow

### 1. Initialization Process
```typescript
// On app startup or profile change
const notificationService = new NotificationService();
await notificationService.ensureValidQueue();

// Subscribe to profile changes
profile.subscribe(async (newProfile) => {
  if (hasRelevantChanges(newProfile)) {
    await notificationService.regenerateQueue();
  }
});
```

### 2. Message Matching Process
```typescript
// Extract user keywords
const userKeywords = [
  ...profile.experiences.map(exp => exp.name.toLowerCase()),
  ...profile.obstacles.map(obs => obs.name.toLowerCase())
];

// Find matching messages
const matchedMessages = ybdMessages.filter(message => 
  message.tags.some(tag => 
    userKeywords.includes(tag.tag_name.toLowerCase())
  )
);

// Categorize by priority
const prioritizedMessages = {
  high: matchedMessages.filter(msg => matchesBothMweAndObstacles(msg, userKeywords)),
  medium: matchedMessages.filter(msg => matchesMweOnly(msg, userKeywords)),
  low: matchedMessages.filter(msg => matchesObstaclesOnly(msg, userKeywords))
};
```

### 3. Schedule Processing
```typescript
// Generate time slots for next 24 hours
const now = new Date();
const endTime = new Date(now.getTime() + 24 * 60 * 60 * 1000);
const timeSlots = [];

profile.schedules.forEach(schedule => {
  schedule.timeRanges.forEach(range => {
    const slots = generateTimeSlotsForRange(range, schedule.selectedDays, now, endTime);
    timeSlots.push(...slots);
  });
});

// Sort by time and remove conflicts
timeSlots.sort((a, b) => a.time.getTime() - b.time.getTime());
```

### 4. Queue Generation
```typescript
// Combine messages with time slots
const notifications = timeSlots.map(slot => {
  const availableMessages = getMessagesForPriority(slot.priority);
  const selectedMessage = selectRandomMessage(availableMessages);
  
  return {
    id: generateUniqueId(),
    scheduledTime: slot.time,
    message: selectedMessage,
    matchedKeywords: getMatchingKeywords(selectedMessage, userKeywords),
    priority: slot.priority,
    sent: false,
    read: false
  };
});
```

## Integration Points

### 1. Profile Store Integration
```typescript
// Subscribe to relevant profile changes
profile.subscribe((newProfile) => {
  const relevantChanges = [
    'experiences', 'obstacles', 'schedules'
  ].some(key => hasChanged(key, newProfile, previousProfile));
  
  if (relevantChanges) {
    notificationService.regenerateQueue();
  }
});
```

### 2. localStorage Management
```typescript
// Cache structure
const CACHE_KEY = 'ybd_notification_queue';
const CACHE_VERSION = '1.0';

// Save to localStorage
const cacheData: NotificationCache = {
  generatedAt: new Date(),
  expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
  profileHash: generateProfileHash(profile),
  notifications: generatedNotifications,
  nextRegenerationTime: calculateNextRegenTime(),
  version: CACHE_VERSION
};

localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
```

### 3. Native App Interface
```typescript
// Expose API for native app consumption
export class NotificationAPI {
  static getUpcomingNotifications(hours: number = 24): NotificationItem[] {
    // Return notifications for next N hours
  }
  
  static markNotificationSent(id: string): void {
    // Mark notification as sent to avoid duplicates
  }
  
  static getNotificationStats(): NotificationStats {
    // Return analytics data for native app
  }
}
```

## Error Handling & Edge Cases

### 1. No Matching Messages
```typescript
// Fallback strategy
if (matchedMessages.length === 0) {
  // Use general motivational messages
  const fallbackMessages = ybdMessages.filter(msg => 
    msg.tags.some(tag => ['motivation', 'inspiration'].includes(tag.tag_name))
  );
  
  // Log for analytics
  console.warn('No matching messages found for user profile', { userKeywords });
}
```

### 2. No Active Schedules
```typescript
// Handle empty schedules
if (profile.schedules.length === 0) {
  return {
    notifications: [],
    message: 'No notification schedules configured. Please set up your notification preferences.',
    action: 'redirect_to_schedule_setup'
  };
}
```

### 3. Cache Corruption
```typescript
// Graceful fallback
try {
  const cachedData = JSON.parse(localStorage.getItem(CACHE_KEY) || '{}');
  validateCacheStructure(cachedData);
} catch (error) {
  console.error('Cache corruption detected, regenerating', error);
  localStorage.removeItem(CACHE_KEY);
  await regenerateQueue();
}
```

### 4. Timezone Changes
```typescript
// Detect timezone changes
const currentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
const cachedTimezone = cachedData.timezone;

if (currentTimezone !== cachedTimezone) {
  console.log('Timezone change detected, adjusting notifications');
  await regenerateQueue();
}
```

## Performance Considerations

### 1. Lazy Loading
```typescript
// Load YBD messages only when needed
let ybdMessagesCache: YBDMessage[] | null = null;

async function getYBDMessages(): Promise<YBDMessage[]> {
  if (!ybdMessagesCache) {
    ybdMessagesCache = await import('../data/ybd_messages.json');
  }
  return ybdMessagesCache;
}
```

### 2. Efficient Matching
```typescript
// Pre-index messages by tags for faster lookup
const messagesByTag = new Map<string, YBDMessage[]>();

ybdMessages.forEach(message => {
  message.tags.forEach(tag => {
    const tagName = tag.tag_name.toLowerCase();
    if (!messagesByTag.has(tagName)) {
      messagesByTag.set(tagName, []);
    }
    messagesByTag.get(tagName)!.push(message);
  });
});
```

### 3. Background Processing
```typescript
// Use requestIdleCallback for heavy computations
function scheduleBackgroundRegeneration() {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => regenerateQueue(), { timeout: 5000 });
  } else {
    setTimeout(() => regenerateQueue(), 100);
  }
}
```

### 4. Debounced Updates
```typescript
// Debounce profile change triggers
const debouncedRegenerate = debounce(async () => {
  await notificationService.regenerateQueue();
}, 1000);

profile.subscribe(debouncedRegenerate);
```

## Testing Strategy

### 1. Unit Tests
```typescript
// Message matching tests
describe('MessageMatcher', () => {
  test('should match exact keywords', () => {
    const matcher = new MessageMatcher();
    const profile = createTestProfile(['peace', 'anxiety']);
    const matches = matcher.matchMessages(profile);
    expect(matches.some(m => m.matchedKeywords.includes('peace'))).toBe(true);
  });
  
  test('should prioritize combined matches', () => {
    // Test priority ordering
  });
  
  test('should handle no matches gracefully', () => {
    // Test fallback behavior
  });
});
```

### 2. Integration Tests
```typescript
// End-to-end notification generation
describe('NotificationService Integration', () => {
  test('should generate valid 24-hour queue', async () => {
    const service = new NotificationService();
    const queue = await service.regenerateQueue();
    
    expect(queue.length).toBeGreaterThan(0);
    expect(queue.every(n => n.scheduledTime > new Date())).toBe(true);
    expect(queue.every(n => n.scheduledTime < addHours(new Date(), 24))).toBe(true);
  });
  
  test('should regenerate on profile changes', async () => {
    // Test profile change triggers
  });
  
  test('should persist to localStorage', async () => {
    // Test cache persistence
  });
});
```

### 3. Edge Case Testing
```typescript
describe('Edge Cases', () => {
  test('should handle empty profile', async () => {
    const emptyProfile = createEmptyProfile();
    const queue = await generateNotificationQueue(emptyProfile);
    expect(queue).toEqual([]);
  });
  
  test('should handle malformed schedules', async () => {
    // Test invalid schedule data
  });
  
  test('should recover from cache corruption', async () => {
    // Test localStorage corruption scenarios
  });
});
```

## File Structure

```
src/lib/
├── services/
│   ├── notificationService.ts      # Main orchestrator
│   ├── messageMatcher.ts           # Message matching logic
│   ├── scheduleProcessor.ts        # Schedule to time slot conversion
│   └── notificationAPI.ts          # Native app interface
├── stores/
│   └── notificationQueue.ts        # Svelte store for notifications
├── types/
│   └── notifications.ts            # TypeScript interfaces
└── utils/
    ├── dateUtils.ts                # Date/time utilities
    ├── hashUtils.ts                # Profile hashing
    └── cacheUtils.ts               # localStorage utilities
```

## Future Enhancements

### 1. Smart Scheduling
- **User Behavior Learning**: Track when users are most responsive to notifications
- **Optimal Timing**: Adjust notification times based on engagement patterns
- **Frequency Optimization**: Learn user preferences for notification frequency

### 2. Advanced Matching
- **Semantic Similarity**: Use NLP to find related concepts beyond exact keyword matching
- **User Feedback Integration**: Allow users to rate message relevance
- **Machine Learning**: Improve recommendations based on user interactions

### 3. Analytics & Insights
- **Effectiveness Tracking**: Monitor which messages drive the most engagement
- **A/B Testing**: Test different message variations and timing strategies
- **User Journey Analytics**: Track how notifications impact user goals

### 4. Enhanced Personalization
- **Dynamic Content**: Personalize message content with user's name and goals
- **Context Awareness**: Consider time of day, weather, calendar events
- **Mood Tracking**: Adjust message tone based on user's current state

### 5. Social Features
- **Shared Goals**: Allow users to share and support each other's goals
- **Community Messages**: User-generated motivational content
- **Accountability Partners**: Notification sharing with trusted contacts

## Implementation Timeline

### Phase 1: Core System (Week 1-2)
- [ ] Create basic service architecture
- [ ] Implement message matching logic
- [ ] Build schedule processing
- [ ] Set up localStorage caching

### Phase 2: Integration (Week 3)
- [ ] Integrate with existing profile store
- [ ] Create notification queue store
- [ ] Add automatic regeneration triggers
- [ ] Implement error handling

### Phase 3: Testing & Polish (Week 4)
- [ ] Write comprehensive tests
- [ ] Add performance optimizations
- [ ] Create native app API
- [ ] Documentation and examples

### Phase 4: Future Enhancements (Ongoing)
- [ ] Analytics implementation
- [ ] Smart scheduling features
- [ ] Advanced personalization
- [ ] User feedback systems

## Conclusion

This notification system provides a robust foundation for delivering personalized, timely motivational content to users. The architecture balances performance, reliability, and extensibility while maintaining simplicity for the initial implementation.

The system's modular design allows for incremental improvements and feature additions without disrupting the core functionality. The comprehensive error handling and testing strategy ensure reliability in production environments.

Key success metrics:
- **User Engagement**: Increased interaction with notifications
- **Goal Achievement**: Higher completion rates for user goals
- **System Reliability**: 99%+ uptime with graceful error handling
- **Performance**: Sub-100ms notification generation times