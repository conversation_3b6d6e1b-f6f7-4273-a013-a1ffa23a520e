# User Verification Implementation

## Overview

This implementation ensures that users who have been deleted from Supabase are automatically logged out of the app. This is crucial for security, as deleted users would otherwise remain authenticated until their JWT token expires.

## Problem Statement

When a user is deleted from Supabase's `auth.users` table:
- Their authentication token (JWT) remains valid until it expires
- The app continues to treat them as authenticated
- They can still access protected resources and make API calls
- This poses a security risk if an admin intentionally removed a user

## Solution Architecture

The solution implements a multi-layered verification system that:
1. Periodically checks if the authenticated user still exists in the database
2. Performs checks when the app becomes active or network connectivity is restored
3. Only performs checks when the device is online (to avoid false positives)
4. Automatically signs out users who no longer exist in the database

## Implementation Details

### Core Files

#### 1. `/src/lib/services/userVerificationService.ts`
The main service that handles user verification logic.

**Key Features:**
- **Network-aware checking**: Only verifies when device is online
- **Throttling**: Prevents excessive API calls (minimum 30 seconds between checks)
- **Periodic verification**: Checks every 5 minutes while app is active
- **Event-based verification**: Checks when:
  - App becomes active (foreground)
  - Network connectivity is restored
  - App is resumed (mobile)
  - Browser tab/window gains focus (web)

**Key Functions:**
- `verifyUserExists()`: Checks if user exists in the profiles table
- `checkUserIfOnline()`: Performs verification only if online
- `initializeUserVerification()`: Sets up all listeners and periodic checks
- `cleanupUserVerification()`: Removes all listeners (called on logout)

**Capacitor Integration:**
- Uses `@capacitor/app` for app state detection
- Uses `@capacitor/network` for network status
- Falls back to web APIs when Capacitor is not available

#### 2. `/src/lib/stores/auth.ts`
Modified to integrate the verification service.

**Changes Made:**
- Imports the verification service
- Calls `initializeUserVerification()` when:
  - Initial session is detected on app load
  - User signs in successfully
- Calls `cleanupUserVerification()` when user signs out
- Ensures verification only runs for authenticated users

#### 3. `/src/lib/auth.ts`
Contains the `signOut()` function used by the verification service.

**Integration:**
- The verification service calls `signOut()` when a deleted user is detected
- Handles the actual Supabase sign out process
- The auth store's listener then redirects to the signin page

## How It Works

### Verification Flow

1. **User Authentication**
   ```
   User logs in → Auth store detects session → Initializes verification service
   ```

2. **Verification Check Process**
   ```
   Check triggered → Verify online status → Query profiles table → Handle result
   ```

3. **Database Query**
   ```typescript
   // Queries the profiles table for the user's ID
   const { data, error } = await supabase
     .from('profiles')
     .select('id')
     .eq('id', session.user.id)
     .single();
   ```

4. **Result Handling**
   - **User exists**: Continue normal operation
   - **User not found** (PGRST116 error): Trigger sign out
   - **Other errors**: Log but don't sign out (avoid false positives)

### Trigger Points

1. **App Initialization**
   - When app loads and user is already authenticated
   - Ensures deleted users can't continue using cached sessions

2. **App State Changes**
   - **Mobile (Capacitor)**: When app comes to foreground
   - **Web**: When browser tab becomes visible or gains focus
   - Catches users returning to the app

3. **Network Connectivity**
   - When device comes back online after being offline
   - Ensures verification runs as soon as possible

4. **Periodic Checks**
   - Every 5 minutes while app is active and online
   - Catches deletions that happen during active use

### Error Handling

- **Network errors**: Skipped when offline, retried when online
- **Database errors**: Logged but don't trigger logout (safety measure)
- **Missing profiles**: Specifically handled as user deletion
- **Capacitor unavailable**: Falls back to web APIs seamlessly

## Testing the Implementation

### Manual Testing Steps

1. **Setup**
   - Log into the app with a test user
   - Note the user's ID from the Supabase dashboard

2. **Verify Normal Operation**
   - Navigate around the app
   - Confirm user remains logged in
   - Check browser console for verification logs

3. **Test User Deletion**
   - Go to Supabase dashboard
   - Navigate to Authentication → Users
   - Delete the test user
   - Return to the app

4. **Verify Automatic Logout**
   - Within 30 seconds to 5 minutes, user should be logged out
   - Can trigger immediate check by:
     - Switching browser tabs and back
     - Turning airplane mode on/off
     - Minimizing and restoring app (mobile)

### Expected Behavior

- **Before deletion**: User can use app normally
- **After deletion**: User is automatically logged out and redirected to signin
- **Offline scenario**: No logout occurs while offline, but triggers when online
- **Console logs**: Shows verification attempts and results

## Security Considerations

1. **Token Expiry**: This solution works alongside JWT expiry, not as a replacement
2. **Rate Limiting**: Verification checks are throttled to prevent API abuse
3. **False Positives**: Errors don't trigger logout to prevent accidental lockouts
4. **Profile Dependency**: Requires users to have entries in the profiles table

## Maintenance Notes

- The 5-minute interval can be adjusted via `VERIFICATION_INTERVAL`
- Throttling can be adjusted via `MIN_TIME_BETWEEN_CHECKS`
- Additional trigger points can be added to `setupAppStateListeners()`
- The profiles table query can be replaced with any table containing user data

## Potential Improvements

1. **Exponential Backoff**: Implement increasing delays on repeated failures
2. **Admin Notification**: Log deletion events for security monitoring
3. **Graceful Messaging**: Show user a "Your account has been deactivated" message
4. **Batch Checking**: For apps with many users, implement server-side verification
5. **Cache Invalidation**: Clear local storage and cached data on deletion detection