# YourBestDays: Privacy-First Supabase + Stripe Integration Plan

## Major App Note

- This is a sveltekit SSG (static site generation) app using capacitor to deploy to ios and android. There is no server other than supabase API and stripe API. No private keys should be used in this app.
- This is a mobile view only app. Even the web version is intended to only be viewed on a mobile device like a phone. All screens should take this into consideration when being built.

## Privacy Architecture Overview

```mermaid
graph TB
    subgraph "Device Storage (Private)"
        A[Local Profile Data] --> B[iCloud Sync]
        A --> C[Anonymous Metrics Queue]
    end
    
    subgraph "Supabase (Minimal PII)"
        D[Auth + Basic Profile] --> E[Subscriptions]
        F[Anonymous Analytics] --> G[Feature Usage Stats]
    end
    
    subgraph "Stripe"
        H[Payment Data] --> I[Subscription Status]
    end
    
    C --> F
    D --> E
    E --> I
```

## Data Classification & Storage Strategy

### 🔒 Device-Only Data (Never Synced to Cloud)
- Wizard responses (beliefs, goals, obstacles, experiences)
- Personal preferences and settings
- Notification schedules and customizations
- Usage patterns and app behavior
- Theme preferences and UI customizations

### ☁️ Supabase Stored Data (Minimal PII)
- User ID (UUID)
- Email (for auth/billing and OTP delivery)
- First/Last name (billing purposes)
- Subscription status and billing info
- Early adopter flag
- Account creation date
- Daily usage metrics (messages viewed, actions confirmed per day)

### 📊 Anonymous Analytics Data
- Aggregated wizard completion rates
- Feature usage statistics (no user linkage)
- App performance metrics
- Anonymized preference trends

### 💾 iCloud Sync Data (User Controlled)
- Encrypted profile backup
- User-initiated data export/import
- Cross-device sync for personal data

## Revised Database Schema

### Supabase Tables (Minimal Data)

```sql
-- Minimal user profiles (PII only for billing/auth)
CREATE TABLE public.user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  stripe_customer_id text unique,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  early_adopter BOOLEAN DEFAULT FALSE,
  wizard_completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Daily usage tracking
CREATE TABLE public.daily_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  usage_date DATE NOT NULL,
  messages_viewed INTEGER DEFAULT 0,
  actions_confirmed INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, usage_date)
);

-- Subscription management
CREATE TABLE public.subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  stripe_subscription_id TEXT UNIQUE,
  stripe_customer_id TEXT,
  status TEXT,
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  plan_duration TEXT,
  price_paid DECIMAL(10,2),
  early_adopter_pricing BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Anonymous analytics (no user linkage)
CREATE TABLE public.anonymous_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  metric_type TEXT, -- 'wizard_completion', 'feature_usage', etc.
  metric_data JSONB,
  session_hash TEXT, -- Anonymous session identifier
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feature flags
CREATE TABLE public.feature_flags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  feature_name TEXT UNIQUE NOT NULL,
  requires_active_subscription BOOLEAN DEFAULT TRUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Implementation Phases

### Phase 1: Privacy-First Setup

#### 1.1 Dependencies
```bash
npm install @supabase/supabase-js stripe @stripe/stripe-js
npm install @capacitor/preferences @capacitor/network
npm install crypto-js # For data anonymization
```

#### 1.2 Data Storage Services

**Local Storage Service** (`src/lib/services/localStorageService.ts`)
```typescript
interface LocalStorageService {
  // Profile data (device only)
  saveProfile(profile: Profile): Promise<void>
  getProfile(): Promise<Profile | null>
  
  // iCloud sync preparation
  exportProfileForSync(): Promise<EncryptedProfile>
  importProfileFromSync(data: EncryptedProfile): Promise<void>
  
  // Anonymous metrics queue
  queueMetric(metric: AnonymousMetric): Promise<void>
  getQueuedMetrics(): Promise<AnonymousMetric[]>
  clearMetricsQueue(): Promise<void>
  
  // Daily usage tracking (extended offline support)
  incrementMessagesViewed(): Promise<void>
  incrementActionsConfirmed(): Promise<void>
  getDailyUsage(date: Date): Promise<DailyUsage>
  getPendingUsageSync(): Promise<DailyUsage[]>
  markUsageSynced(date: Date): Promise<void>
  
  // Long-term offline storage
  getUsageQueueSize(): Promise<number>
  cleanupOldUsageData(olderThanDays: number): Promise<void>
  exportPendingUsage(): Promise<DailyUsage[]>
}
```

**Usage Tracking Service** (`src/lib/services/usageTrackingService.ts`)
```typescript
interface UsageTrackingService {
  // Local tracking with cloud sync
  trackMessageViewed(): Promise<void>
  trackActionConfirmed(): Promise<void>
  
  // Sync to Supabase
  syncDailyUsage(): Promise<void>
  getDailyStats(date: Date): Promise<DailyUsage>
  getUsageHistory(startDate: Date, endDate: Date): Promise<DailyUsage[]>
  
  // Extended offline support
  queueUsageUpdate(type: 'message' | 'action'): Promise<void>
  processPendingUsage(): Promise<void>
  getPendingDaysCount(): Promise<number>
  getOldestPendingDate(): Promise<Date | null>
  
  // Batch sync for long offline periods
  syncMultipleDays(days: DailyUsage[]): Promise<void>
  
  // Storage management (preserve daily granularity)
  archiveOldData(): Promise<void> // Move old data to archive storage
  getStorageUsage(): Promise<StorageInfo>
}

interface DailyUsage {
  date: Date
  messagesViewed: number
  actionsConfirmed: number
  synced: boolean
  lastModified: Date
}

interface OfflineUsageQueue {
  pendingDays: Map<string, DailyUsage> // date string -> usage data
  maxRetentionDays: number // 180 days maximum
  archiveThreshold: number // e.g., 90 days - move to archive storage
}

interface StorageInfo {
  pendingDays: number
  archivedDays: number
  totalStorageKB: number
  oldestPendingDate: Date | null
}
```

**Extended Offline Storage Strategy (Preserving Daily Granularity)**
```typescript
class ExtendedOfflineManager {
  private readonly MAX_RETENTION_DAYS = 180; // 6 months maximum
  private readonly ARCHIVE_THRESHOLD = 90;   // 3 months to archive
  
  // Three-tier storage system with 180-day limit
  async manageStorage(): Promise<void> {
    // Tier 1: Active queue (0-30 days) - immediate sync priority
    const activePending = await this.getActivePendingData();
    
    // Tier 2: Archive storage (31-180 days) - batch sync when convenient
    const archivedData = await this.getArchivedData();
    
    // Tier 3: Delete data older than 180 days (unrecoverable)
    await this.deleteExpiredData();
  }
  
  // Hard delete after 180 days
  async deleteExpiredData(): Promise<void> {
    const expiredData = await this.getDataOlderThan(this.MAX_RETENTION_DAYS);
    
    if (expiredData.length > 0) {
      console.warn(`Deleting ${expiredData.length} days of usage data older than 180 days`);
      await this.permanentlyDeleteData(expiredData);
    }
  }
  
  // Preserve daily granularity while managing storage (up to 180 days)
  async archiveOldData(): Promise<void> {
    const oldData = await this.getDataOlderThan(this.ARCHIVE_THRESHOLD);
    const expiredData = oldData.filter(day =>
      this.daysSince(day.date) > this.MAX_RETENTION_DAYS
    );
    
    // Delete expired data first
    if (expiredData.length > 0) {
      await this.permanentlyDeleteData(expiredData);
    }
    
    // Archive remaining old data (90-180 days)
    const archivableData = oldData.filter(day =>
      this.daysSince(day.date) <= this.MAX_RETENTION_DAYS
    );
    
    if (archivableData.length > 0) {
      const archivedFormat = archivableData.map(day => ({
        date: day.date,
        messagesViewed: day.messagesViewed,
        actionsConfirmed: day.actionsConfirmed,
        dayOfWeek: day.date.getDay(),
        archived: true,
        synced: day.synced,
        daysOld: this.daysSince(day.date)
      }));
      
      await this.storeInArchive(archivedFormat);
      await this.removeFromActiveQueue(archivableData);
    }
  }
  
  private daysSince(date: Date): number {
    return Math.floor((Date.now() - date.getTime()) / (1000 * 60 * 60 * 24));
  }
  
  // Smart sync prioritization (with 180-day expiration awareness)
  async prioritizedSync(): Promise<void> {
    // Priority 1: Data approaching 180-day limit (170+ days old)
    await this.syncCriticalExpiringData();
    
    // Priority 2: Recent data (last 7 days)
    await this.syncRecentData();
    
    // Priority 3: Older pending data (8-90 days)
    await this.syncOlderPendingData();
    
    // Priority 4: Archived data (90-180 days, when bandwidth allows)
    await this.syncArchivedData();
  }
  
  // Emergency sync for data about to expire
  async syncCriticalExpiringData(): Promise<void> {
    const criticalData = await this.getDataOlderThan(170); // 10-day warning
    
    if (criticalData.length > 0) {
      console.warn(`Attempting emergency sync of ${criticalData.length} days about to expire`);
      await this.forceSyncWithRetry(criticalData);
    }
  }
```

**iCloud Sync Service** (`src/lib/services/iCloudSyncService.ts`)
```typescript
interface iCloudSyncService {
  // User-controlled backup/restore
  backupToiCloud(): Promise<void>
  restoreFromiCloud(): Promise<Profile | null>
  
  // Cross-device sync
  syncAcrossDevices(): Promise<void>
  
  // Data encryption
  encryptProfile(profile: Profile): Promise<string>
  decryptProfile(encrypted: string): Promise<Profile>
}
```

### Phase 2: Anonymous Analytics System

#### 2.1 Metrics Collection Service
```typescript
interface MetricsService {
  // Anonymous data collection
  trackWizardCompletion(anonymizedData: WizardMetrics): Promise<void>
  trackFeatureUsage(feature: string, context?: object): Promise<void>
  trackAppPerformance(metrics: PerformanceMetrics): Promise<void>
  
  // Data anonymization
  anonymizeUserData(data: any): AnonymousMetric
  generateSessionHash(): string
  
  // Batch sending
  sendQueuedMetrics(): Promise<void>
}
```

#### 2.2 Data Anonymization Strategy
```typescript
// Example anonymization for wizard data
function anonymizeWizardData(profile: Profile): AnonymousWizardMetrics {
  return {
    ageRange: profile.personal.ageRange, // Keep demographic data
    goalCount: profile.goals.length,
    experienceCategories: profile.experiences.map(e => hashCategory(e.name)),
    obstacleCategories: profile.obstacles.map(o => hashCategory(o.name)),
    completionTime: Date.now() - profile.wizardStartTime,
    // NO personal names, specific goals, or identifying info
  }
}
```

### Phase 3: Enhanced Authentication (Privacy-Focused)

#### 3.1 Minimal Auth Service
```typescript
interface PrivacyAuthService {
  // Email-based OTP Authentication (no phone collection)
  signInWithOTP(email: string): Promise<void>
  verifyOTP(email: string, token: string): Promise<MinimalUser>
  
  // Minimal user data
  updateBillingInfo(info: BillingInfo): Promise<void>
  getBillingInfo(): Promise<BillingInfo>
  
  // Usage data sync
  syncUsageData(): Promise<void>
  
  // No profile data sync - only auth state
  getAuthState(): Promise<AuthState>
}
```

#### 3.2 Separated Data Stores
```typescript
// Auth store (minimal data)
export const authStore = writable<{
  isAuthenticated: boolean
  userId: string | null
  subscriptionStatus: SubscriptionStatus
}>()

// Profile store (device-only, enhanced)
export const localProfile = writable<Profile>()

// Usage tracking store
export const usageStore = writable<{
  todayStats: DailyUsage
  pendingSync: DailyUsage[]
  lastSyncDate: Date | null
}>()

// Sync status store
export const syncStatus = writable<{
  lastBackup: Date | null
  lastRestore: Date | null
  pendingMetrics: number
  pendingUsageUpdates: number
}>()
```

### Phase 4: Subscription System (Privacy-Compliant)

#### 4.1 Subscription Service (No Personal Data)
```typescript
interface PrivacySubscriptionService {
  // Subscription management (uses Stripe customer ID only)
  createSubscription(planId: string): Promise<string>
  getSubscriptionStatus(): Promise<SubscriptionStatus>
  
  // Feature access (local determination)
  hasFeatureAccess(feature: string): boolean
  getDisabledFeatures(): string[]
  
  // Early adopter (flag only, no personal tracking)
  isEarlyAdopter(): boolean
}
```

### Phase 5: iCloud Integration (iOS/macOS)

#### 5.1 Capacitor iCloud Plugin Setup
```bash
npm install @capacitor-community/icloud-kvstore
```

#### 5.2 Cross-Device Sync Implementation
```typescript
// iCloud key-value store for profile sync
const ICLOUD_KEYS = {
  PROFILE_BACKUP: 'ybd_profile_backup',
  SYNC_TIMESTAMP: 'ybd_last_sync'
}

async function syncToiCloud(profile: Profile): Promise<void> {
  const encrypted = await encryptProfile(profile)
  await ICloudKVStore.set({
    key: ICLOUD_KEYS.PROFILE_BACKUP,
    value: encrypted
  })
}
```

### Phase 6: UI Components (Privacy-Aware)

#### 6.1 Privacy Controls
- `PrivacySettings.svelte` - Control data sharing preferences
- `DataExport.svelte` - Export personal data
- `SyncControls.svelte` - Manage iCloud sync
- `MetricsOptOut.svelte` - Anonymous analytics preferences

#### 6.2 Clear Data Boundaries
```svelte
<!-- Example: Clear indication of data storage -->
<div class="privacy-indicator">
  <Icon name="shield" />
  <span>This data stays on your device</span>
</div>

<div class="sync-indicator">
  <Icon name="cloud" />
  <span>Synced via iCloud (encrypted)</span>
</div>
```

### Phase 7: Data Architecture Implementation

#### 7.1 Storage Decision Matrix

| Data Type | Local Storage | iCloud Sync | Supabase | Anonymous Analytics |
|-----------|---------------|-------------|----------|-------------------|
| Wizard responses | ✅ | ✅ (encrypted) | ❌ | ✅ (anonymized) |
| Personal goals | ✅ | ✅ (encrypted) | ❌ | ✅ (count only) |
| App preferences | ✅ | ✅ (encrypted) | ❌ | ❌ |
| Auth info | ❌ | ❌ | ✅ (email only) | ❌ |
| Subscription status | ❌ | ❌ | ✅ | ❌ |
| Daily usage stats | ✅ (cache) | ❌ | ✅ (aggregated) | ✅ (anonymized) |
| Usage patterns | ✅ | ❌ | ❌ | ✅ (anonymized) |

#### 7.2 Implementation Guidelines

**Adding New Local-Only Data:**
1. Add to `Profile` interface in `src/lib/stores/profile.ts`
2. Update `localStorageService.ts` save/load methods
3. Include in iCloud sync encryption
4. Add to data export functionality

**Adding New Analytics Data:**
1. Define in `src/lib/types/analytics.ts`
2. Implement anonymization in `metricsService.ts`
3. Add to queue system
4. Update privacy policy documentation

**Adding New Supabase Data:**
1. Must be essential for auth/billing/usage tracking only
2. Update database schema
3. Add RLS policies
4. Document in privacy policy

**Adding Usage Tracking:**
1. Add tracking calls in relevant components
2. Update `usageTrackingService.ts`
3. Ensure offline queueing works
4. Test sync functionality

### Phase 8: Privacy Compliance Features

#### 8.1 Data Transparency
```typescript
// Data audit service
interface DataAuditService {
  getLocalDataSummary(): LocalDataSummary
  getCloudDataSummary(): CloudDataSummary
  getAnalyticsDataSummary(): AnalyticsDataSummary
  exportAllUserData(): Promise<UserDataExport>
  deleteAllUserData(): Promise<void>
}
```

#### 8.2 User Controls
- Complete data export (GDPR compliance)
- Selective data deletion
- Analytics opt-out
- iCloud sync enable/disable
- Account deletion with data purge

### Phase 9: Testing Strategy

#### 9.1 Privacy Testing
- Verify no PII in analytics
- Test data encryption/decryption
- Validate iCloud sync security
- Confirm local data isolation

#### 9.2 Compliance Testing
- GDPR data export/deletion
- CCPA compliance verification
- App store privacy label accuracy

### Phase 10: Documentation & Deployment

#### 10.1 Privacy Documentation
- Clear privacy policy
- Data handling documentation
- User-facing privacy controls guide
- Developer data classification guide

#### 10.2 App Store Compliance
- Privacy nutrition labels
- Data collection disclosure
- Third-party SDK audit

## Key Benefits of This Approach

1. **User Privacy**: Personal data never leaves device unless user explicitly syncs
2. **Compliance**: GDPR/CCPA ready with minimal PII collection
3. **Analytics**: Valuable insights without privacy invasion
4. **User Control**: Complete transparency and control over data
5. **Security**: Encrypted backups and minimal attack surface
6. **Performance**: Fast local access to personal data

## Implementation Priority

1. **Phase 1**: Privacy-first storage architecture
2. **Phase 2**: Anonymous analytics system
3. **Phase 3**: Minimal authentication
4. **Phase 5**: iCloud sync (critical for user experience)
5. **Phase 4**: Subscription system
6. **Phase 6-7**: UI and data architecture
7. **Phase 8**: Privacy compliance features
8. **Phase 9-10**: Testing and deployment

This approach ensures user privacy while maintaining all desired functionality and providing valuable business insights through anonymous analytics.