# Ultra-Simplified Theme System Documentation

## Overview

This is a maximally simplified theme system for Svelte + Tailwind CSS v4 projects. Each theme has exactly **one primary color** with 9 systematic variants (-100 to -900), using OKLCH color space for consistent, perceptual color definitions.

## Key Features

- ✅ **Ultra-minimal**: Each theme = 9 color variants only
- ✅ **Consistent hues**: No color shifting between light/dark modes
- ✅ **Semantic colors**: Consistent intensity across modes
- ✅ **OKLCH color space**: Perceptually uniform colors
- ✅ **Tailwind v4 ready**: Native CSS custom properties
- ✅ **Print-accurate**: CMYK color specifications supported

## Quick Setup

### 1. Install Dependencies

```bash
npm install tailwindcss @tailwindcss/vite
```

### 2. Vite Configuration

```javascript
// vite.config.js
import { sveltekit } from '@sveltejs/kit/vite';
import tailwindcss from '@tailwindcss/vite';

export default {
  plugins: [sveltekit(), tailwindcss()]
};
```

### 3. CSS Setup

Create `src/app.css`:

```css
@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
}

/* Base semantic colors - same for all themes */
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.1 0.05 240);
  --border: oklch(0.9 0.02 240);
  --input: oklch(0.95 0.01 240);
  --ring: var(--primary-500);
  --destructive: oklch(0.577 0.245 27.325);
  
  /* Semantic colors - consistent across light/dark modes */
  --muted: var(--primary-100);
  --muted-foreground: oklch(0.5 0.05 240);
  --primary: var(--primary-500);
  --primary-foreground: oklch(0.95 0.01 240);
  --secondary: var(--primary-200);
  --secondary-foreground: oklch(0.2 0.05 240);
  --accent: var(--primary-300);
  --accent-foreground: oklch(0.1 0.05 240);
}

.dark {
  --background: oklch(0.1 0.05 240);
  --foreground: oklch(0.95 0.01 240);
  --border: oklch(0.2 0.1 240);
  --input: oklch(0.15 0.08 240);
  --destructive: oklch(0.704 0.191 22.216);
  
  /* Keep semantic colors consistent - only adjust foreground colors for contrast */
  --muted-foreground: oklch(0.7 0.05 240);
  --primary-foreground: oklch(0.1 0.05 240);
  --secondary-foreground: oklch(0.9 0.02 240);
  --accent-foreground: oklch(0.95 0.01 240);
}

/* Blue Theme (Default) */
:root,
[data-theme="blue"] {
  /* Primary color scale - Blue (hue 240°) */
  --primary-100: oklch(0.95 0.05 240);
  --primary-200: oklch(0.9 0.08 240);
  --primary-300: oklch(0.8 0.12 240);
  --primary-400: oklch(0.7 0.16 240);
  --primary-500: oklch(0.6 0.2 240);
  --primary-600: oklch(0.5 0.2 240);
  --primary-700: oklch(0.4 0.18 240);
  --primary-800: oklch(0.3 0.15 240);
  --primary-900: oklch(0.2 0.1 240);
}

/* Hunter Green Theme */
[data-theme="hunter-green"] {
  /* Primary color scale - Hunter Green (hue 120°) */
  --primary-100: oklch(0.95 0.05 120);
  --primary-200: oklch(0.9 0.08 120);
  --primary-300: oklch(0.8 0.12 120);
  --primary-400: oklch(0.7 0.16 120);
  --primary-500: oklch(0.6 0.2 120);
  --primary-600: oklch(0.5 0.2 120);
  --primary-700: oklch(0.4 0.18 120);
  --primary-800: oklch(0.3 0.15 120);
  --primary-900: oklch(0.2 0.1 120);
}

/* Forest Green Theme */
[data-theme="forest-green"] {
  /* Primary color scale - Forest Green (hue 140°) */
  --primary-100: oklch(0.95 0.05 140);
  --primary-200: oklch(0.9 0.08 140);
  --primary-300: oklch(0.8 0.12 140);
  --primary-400: oklch(0.7 0.16 140);
  --primary-500: oklch(0.6 0.2 140);
  --primary-600: oklch(0.5 0.2 140);
  --primary-700: oklch(0.4 0.18 140);
  --primary-800: oklch(0.3 0.15 140);
  --primary-900: oklch(0.2 0.1 140);
}

/* Red Theme */
[data-theme="red"] {
  /* Primary color scale - Red (CMYK 0,100,100,0 base) */
  --primary-100: oklch(0.95 0.05 29);
  --primary-200: oklch(0.9 0.1 29);
  --primary-300: oklch(0.8 0.15 29);
  --primary-400: oklch(0.7 0.2 29);
  --primary-500: oklch(0.628 0.257 29);  /* CMYK (0,100,100,0) */
  --primary-600: oklch(0.55 0.25 29);
  --primary-700: oklch(0.45 0.22 29);
  --primary-800: oklch(0.35 0.18 29);
  --primary-900: oklch(0.25 0.12 29);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  
  /* Map to Tailwind color system */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-destructive: var(--destructive);
  
  /* Semantic colors */
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  
  /* Primary colors */
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

### 4. Theme Store

Create `src/lib/stores/theme.ts`:

```typescript
import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type Theme = 'blue' | 'hunter-green' | 'forest-green' | 'red';

export const themes: { value: Theme; label: string }[] = [
  { value: 'blue', label: 'Blue' },
  { value: 'hunter-green', label: 'Hunter Green' },
  { value: 'forest-green', label: 'Forest Green' },
  { value: 'red', label: 'Red' }
];

function createThemeStore() {
  const { subscribe, set } = writable<Theme>('blue');

  return {
    subscribe,
    set: (theme: Theme) => {
      if (browser) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
      }
      set(theme);
    },
    init: () => {
      if (browser) {
        const stored = localStorage.getItem('theme') as Theme;
        const theme = stored && themes.some(t => t.value === stored) ? stored : 'blue';
        document.documentElement.setAttribute('data-theme', theme);
        set(theme);
      }
    }
  };
}

export const theme = createThemeStore();
```

### 5. Dark Mode Store

Create `src/lib/stores/darkMode.ts`:

```typescript
import { writable } from 'svelte/store';
import { browser } from '$app/environment';

function createDarkModeStore() {
  const { subscribe, set } = writable(false);

  return {
    subscribe,
    toggle: () => {
      if (browser) {
        const isDark = document.documentElement.classList.contains('dark');
        if (isDark) {
          document.documentElement.classList.remove('dark');
          localStorage.setItem('darkMode', 'false');
          set(false);
        } else {
          document.documentElement.classList.add('dark');
          localStorage.setItem('darkMode', 'true');
          set(true);
        }
      }
    },
    init: () => {
      if (browser) {
        const stored = localStorage.getItem('darkMode');
        const isDark = stored === 'true' || 
          (stored === null && window.matchMedia('(prefers-color-scheme: dark)').matches);
        
        if (isDark) {
          document.documentElement.classList.add('dark');
          set(true);
        } else {
          document.documentElement.classList.remove('dark');
          set(false);
        }
      }
    }
  };
}

export const darkMode = createDarkModeStore();
```

### 6. Theme Switcher Component

Create `src/lib/components/ThemeSwitcher.svelte`:

```svelte
<script lang="ts">
  import { theme, themes } from '$lib/stores/theme';
  import { darkMode } from '$lib/stores/darkMode';
  import { onMount } from 'svelte';

  onMount(() => {
    theme.init();
    darkMode.init();
  });
</script>

<div class="flex items-center gap-2">
  <!-- Dark Mode Toggle -->
  <button
    on:click={darkMode.toggle}
    class="p-2 rounded-lg border border-border bg-background hover:bg-muted transition-colors"
    aria-label="Toggle dark mode"
  >
    {#if $darkMode}
      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
      </svg>
    {:else}
      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
      </svg>
    {/if}
  </button>

  <!-- Theme Color Indicator -->
  <div class="w-4 h-4 rounded-full bg-primary-500 border border-border"></div>

  <!-- Theme Selector -->
  <select
    bind:value={$theme}
    on:change={(e) => theme.set(e.currentTarget.value)}
    class="px-3 py-1 rounded-lg border border-border bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
  >
    {#each themes as themeOption}
      <option value={themeOption.value}>{themeOption.label}</option>
    {/each}
  </select>
</div>
```

### 7. App Layout

Update `src/app.html`:

```html
<!DOCTYPE html>
<html lang="en" %sveltekit.theme%>
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%sveltekit.assets%/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    %sveltekit.head%
  </head>
  <body data-sveltekit-preload-data="hover" class="bg-background text-foreground">
    <div style="display: contents">%sveltekit.body%</div>
  </body>
</html>
```

## Usage Examples

### Direct Color Classes

```html
<!-- Primary color variants -->
<div class="bg-primary-100">Very light</div>
<div class="bg-primary-500">Base color</div>
<div class="bg-primary-900">Very dark</div>

<!-- With dark mode variants -->
<div class="bg-primary-500 dark:bg-primary-400">Responsive</div>
```

### Semantic Color Classes

```html
<!-- Semantic colors (consistent across light/dark) -->
<button class="bg-primary text-primary-foreground">Primary Button</button>
<div class="bg-secondary text-secondary-foreground">Secondary Background</div>
<div class="bg-accent text-accent-foreground">Accent Element</div>

<!-- Layout colors -->
<div class="bg-background text-foreground">Main content</div>
<div class="bg-muted text-muted-foreground">Muted content</div>
<input class="bg-input border-border">
```

## Color System Architecture

### OKLCH Color Space

All colors use OKLCH format: `oklch(lightness chroma hue)`

- **Lightness**: 0-1 (0 = black, 1 = white)
- **Chroma**: 0-0.4 (0 = gray, higher = more saturated)
- **Hue**: 0-360 degrees (color wheel position)

### Color Scale Pattern

Each theme follows this systematic pattern:

```css
--primary-100: oklch(0.95 0.05 HUE);  /* Very light */
--primary-200: oklch(0.9 0.08 HUE);   /* Light */
--primary-300: oklch(0.8 0.12 HUE);   /* Light-medium */
--primary-400: oklch(0.7 0.16 HUE);   /* Medium-light */
--primary-500: oklch(0.6 0.2 HUE);    /* Base color */
--primary-600: oklch(0.5 0.2 HUE);    /* Medium-dark */
--primary-700: oklch(0.4 0.18 HUE);   /* Dark-medium */
--primary-800: oklch(0.3 0.15 HUE);   /* Dark */
--primary-900: oklch(0.2 0.1 HUE);    /* Very dark */
```

### Semantic Color Mapping

Semantic colors provide consistent meaning across themes:

```css
--primary: var(--primary-500);      /* Main brand color */
--secondary: var(--primary-200);    /* Light variant */
--accent: var(--primary-300);       /* Highlight color */
--muted: var(--primary-100);        /* Subtle background */
```

## Adding New Themes

### 1. Define Color Scale

Add a new theme by defining its color scale:

```css
/* Purple Theme */
[data-theme="purple"] {
  --primary-100: oklch(0.95 0.05 280);
  --primary-200: oklch(0.9 0.08 280);
  --primary-300: oklch(0.8 0.12 280);
  --primary-400: oklch(0.7 0.16 280);
  --primary-500: oklch(0.6 0.2 280);
  --primary-600: oklch(0.5 0.2 280);
  --primary-700: oklch(0.4 0.18 280);
  --primary-800: oklch(0.3 0.15 280);
  --primary-900: oklch(0.2 0.1 280);
}
```

### 2. Update Theme Store

```typescript
export type Theme = 'blue' | 'hunter-green' | 'forest-green' | 'red' | 'purple';

export const themes: { value: Theme; label: string }[] = [
  { value: 'blue', label: 'Blue' },
  { value: 'hunter-green', label: 'Hunter Green' },
  { value: 'forest-green', label: 'Forest Green' },
  { value: 'red', label: 'Red' },
  { value: 'purple', label: 'Purple' }
];
```

## CMYK Color Support

For print-accurate colors, use CMYK specifications:

### Converting CMYK to OKLCH

1. **Convert CMYK to RGB** using color conversion tools
2. **Convert RGB to OKLCH** using tools like [OKLCH Color Picker](https://oklch.com)
3. **Use the OKLCH values** in your theme

Example: CMYK (0,100,100,0) → RGB (255,0,0) → OKLCH (0.628 0.257 29)

## Best Practices

### 1. Consistent Hue Usage

- Use the same hue value across all variants in a theme
- Only adjust lightness and chroma for variants
- Maintain hue consistency between light/dark modes

### 2. Semantic Color Strategy

- Use semantic colors (`bg-primary`) for consistent meaning
- Use direct variants (`bg-primary-500`) for specific intensities
- Keep semantic mappings consistent across themes

### 3. Accessibility

- Ensure sufficient contrast ratios
- Test with screen readers
- Provide alternative indicators beyond color

### 4. Performance

- Minimize CSS custom properties
- Use Tailwind's built-in optimizations
- Leverage CSS custom property inheritance

## Troubleshooting

### Theme Not Switching

1. Check `data-theme` attribute is set on `<html>`
2. Verify theme store initialization in `onMount`
3. Ensure CSS selectors match theme values

### Colors Not Updating

1. Verify CSS custom property definitions
2. Check Tailwind configuration
3. Ensure proper CSS cascade order

### Dark Mode Issues

1. Check `.dark` class on `<html>`
2. Verify dark mode store initialization
3. Ensure semantic color overrides are correct

## Migration from Complex Systems

### From Multi-Color Themes

1. **Identify primary brand color** for each theme
2. **Remove secondary/tertiary colors** - derive from primary
3. **Update component classes** to use single color scale
4. **Test thoroughly** across light/dark modes

### From HSL/RGB Systems

1. **Convert colors to OKLCH** for perceptual uniformity
2. **Establish systematic lightness scale**
3. **Maintain consistent chroma progression**
4. **Update CSS custom property names**

This ultra-simplified system provides maximum flexibility with minimal complexity, making it perfect for rapid development and easy maintenance across projects.