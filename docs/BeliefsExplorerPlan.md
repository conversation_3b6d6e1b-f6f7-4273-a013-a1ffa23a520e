# Beliefs Explorer Implementation Plan

This document outlines the detailed plan for implementing the "Beliefs Explorer" feature within the Svelte application, based on the user's requirements.

## Task Overview

The user needs a "beliefs explorer" for users to explore their beliefs and reference goals from their profile. This explorer will guide users through an 8-step "Beliefs Explorer," presented as a series of steps with prev/next navigation. Each step will involve "flashcards" with prompts and reflections. Initially, three cards will be presented, followed by a prompt to explore more from the section or move to the next step.

## Summary of Findings

*   **Data Structure:** `sample_anger_flow.json` shows an array of objects with `ybddatatype` and `favorite` properties. `ybd.ts` defines interfaces for these data types. This structure can be adapted for the beliefs explorer flashcards.
*   **Profile:** `profile.ts` manages user data, but `goals` are not explicitly defined. This will need to be addressed by extending the `Profile` interface.
*   **UI Components:** `relief/+page.svelte` is an empty canvas, suitable for hosting the new feature. `WizardNavigation.svelte` and `StepIndicator.svelte` are available for multi-step navigation. `CardBase.svelte` (from `src/lib/components/`) could be useful for displaying flashcards.

## Detailed Plan

### Phase 1: Data Modeling and Storage

1.  **Define Beliefs Explorer Data Structure:**
    *   Create a new TypeScript interface (e.g., `BeliefExplorerStep`, `Flashcard`) in `src/lib/types/beliefs.ts`.
    *   Each `BeliefExplorerStep` will contain:
        *   `id`: Unique identifier for the step.
        *   `name`: Name of the step (e.g., "Notice", "Name / Identify").
        *   `description`: Detailed explanation of the step.
        *   `flashcards`: An array of `Flashcard` objects.
    *   Each `Flashcard` will contain:
        *   `id`: Unique identifier for the flashcard.
        *   `front`: The prompt/question.
        *   `back`: The reflection/guide.
        *   `favorite`: Boolean to indicate if the user has starred it.
        *   `dynamic_type` (optional): To link to specific dynamics if a flashcard is only relevant to certain ones (e.g., Anger-Based).
2.  **Create JSON Data File:**
    *   Create a new JSON file (e.g., `src/lib/data/beliefs_explorer_data.json`) that contains the 8 steps and their associated flashcards, following the defined data structure. This will be the "database" for the explorer content.
3.  **Extend User Profile for Goals:**
    *   Modify `src/lib/types/ybd.ts` (or `src/lib/types/profile.ts` if a separate profile type is created) to add a `goals` property to the `Profile` interface. This `goals` property could be an array of strings or a more complex object if goals have additional properties (e.g., `id`, `name`, `description`).
    *   Update `src/lib/stores/profile.ts` to initialize and manage this new `goals` property.

### Phase 2: Core UI Implementation

1.  **Create Beliefs Explorer Store:**
    *   Create a new Svelte store (e.g., `src/lib/stores/beliefsExplorer.ts`) to manage the state of the beliefs explorer:
        *   `currentStepIndex`: The index of the current step (0-7).
        *   `selectedDynamic`: The dynamic chosen in Step 2 (e.g., "Anger-Based").
        *   `starredFlashcards`: An array of IDs of flashcards the user has starred.
        *   `currentFlashcards`: The set of flashcards currently being displayed for the step.
        *   `flashcardDisplayCount`: To track how many cards have been shown in the current set (initially 3).
2.  **Develop Beliefs Explorer Page:**
    *   Modify `src/routes/(wtabnav)/relief/+page.svelte` to be the main entry point for the beliefs explorer.
    *   This page will:
        *   Load the `beliefs_explorer_data.json`.
        *   Display the current step's name and description.
        *   Render the flashcards for the current step.
        *   Integrate `WizardNavigation.svelte` for "Previous" and "Next" step navigation.
        *   Integrate `StepIndicator.svelte` to show progress through the 8 steps.
3.  **Implement Flashcard Component:**
    *   Create a new Svelte component (e.g., `src/lib/components/BeliefFlashcard.svelte`) to display individual flashcards.
    *   This component will:
        *   Show the `front` (prompt) by default.
        *   Allow users to flip the card to reveal the `back` (reflection).
        *   Include a "star" icon/button to mark the card as a favorite, updating the `starredFlashcards` in the store.
        *   Potentially use `CardBase.svelte` as a wrapper.
4.  **Implement Flashcard Display Logic:**
    *   In `relief/+page.svelte` (or a sub-component), implement the logic to:
        *   Initially display 3 flashcards from the current step's `flashcards` array.
        *   After 3 cards, display a prompt: "Explore more from this section?" or "Move to the next step."
        *   If "Explore more" is chosen, display the next 3 cards (or remaining cards).
        *   If "Move to next step" is chosen, update `currentStepIndex` and reset flashcard display.

### Phase 3: Step-Specific Logic and Enhancements

1.  **Step 2: Dynamic Tailoring:**
    *   When the user selects a dynamic in Step 2, update the `selectedDynamic` in the `beliefsExplorer` store.
    *   Modify the flashcard display logic to filter flashcards based on `dynamic_type` if a dynamic is selected.
2.  **Step 5: Goals + Why Reflection:**
    *   Implement a UI element on this step that fetches and displays the user's goals from the `profile` store.
    *   Provide input fields or prompts for the user to reflect on "Why does each of those things matter to you?" (This data might need to be stored temporarily or persistently, depending on requirements).
3.  **Persistence (Optional but Recommended):**
    *   Consider how to persist the user's progress (current step, selected dynamic, starred flashcards) across sessions. This could involve local storage or a backend integration (if available).

## Visual Flow Diagrams

### Overall Flow

```mermaid
graph TD
    A[Start Beliefs Explorer] --> B{Load Data & State};
    B --> C[Display Step 1: Notice];
    C --> D[Display 3 Flashcards];
    D --> E{User Action};
    E --> F[Flip Card / Star Card];
    E --> G{Prompt: More Cards or Next Step?};
    G --> H[Display Next 3 Cards];
    G --> I[Move to Next Step];
    I --> J{Is it Step 2?};
    J -- Yes --> K[Select Dynamic];
    K --> L[Filter Flashcards by Dynamic];
    J -- No --> M[Display Current Step's Flashcards];
    M --> D;
    L --> D;
    I --> N{Is it Step 5?};
    N -- Yes --> O[Display User Goals];
    O --> P[Prompt for Why Reflection];
    N -- No --> M;
    I --> Q{Is it Last Step?};
    Q -- Yes --> R[Completion / New-Try];
    Q -- No --> M;
```

### Flashcard Interaction

```mermaid
graph TD
    A[Display 3 Flashcards] --> B{User Interacts with Card};
    B --> C[Flip Card];
    B --> D[Star Card];
    C --> E[Show Back of Card];
    D --> F[Update Starred Flashcards in Store];
    E --> G[Card Interaction Complete];
    F --> G;
    G --> H{All 3 Cards Explored?};
    H -- Yes --> I[Display Prompt: More or Next?];
    H -- No --> A;
    I --> J[Display Next 3 Cards];
    I --> K[Move to Next Step];
    J --> A;
    K --> L[Reset Flashcard Display];
    K --> M[Advance Step Index];
    M --> N[Load New Step's Flashcards];
    N --> A;