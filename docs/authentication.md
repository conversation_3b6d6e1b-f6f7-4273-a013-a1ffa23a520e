# Authentication System

This application uses Supabase OTP (One-Time Password) authentication for secure, passwordless login.

## How it Works

1. **Email-based Authentication**: Users enter their email address to sign up or sign in
2. **OTP Code Generation**: Supabase sends a 6-digit verification code to the user's email
3. **Code Verification**: Users enter the 6-digit code to complete authentication
4. **Session Management**: Once verified, users are authenticated and redirected to the app

**Note**: This implementation uses OTP codes only - no magic links are sent in emails.

## File Structure

### Authentication Pages
- `src/routes/auth/signup/+page.svelte` - Sign up with email and OTP verification
- `src/routes/auth/signin/+page.svelte` - Sign in with email and OTP verification  
- `src/routes/auth/callback/+page.svelte` - Handles magic link redirects from email
- `src/routes/auth/+layout.svelte` - Shared layout for auth pages

### Authentication Utilities
- `src/lib/supabaseClient.ts` - Supabase client configuration
- `src/lib/auth.ts` - Authentication helper functions and utilities

### Protected Routes
- `src/routes/app/+page.svelte` - Main app page with authentication protection

## Key Features

### OTP Authentication Flow

**For Signup (new users):**
1. User enters email address
2. System calls `sendSignupOtp(email)` to send verification code
3. User receives email with 6-digit verification code
4. User enters the code and system calls `verifyOtp(email, token)`

**For Signin (existing users only):**
1. User enters email address
2. System calls `sendSigninOtp(email)` to send verification code
3. User receives email with 6-digit verification code
4. User enters the code and system calls `verifyOtp(email, token)`

### Authentication Functions

```typescript
// Send OTP code for signup (allows new user creation)
await sendSignupOtp(email);

// Send OTP code for signin (existing users only)
await sendSigninOtp(email);

// Verify OTP code (same for both signup and signin)
await verifyOtp(email, token);

// Get current session
const { user, session } = await getAuthSession();

// Require authentication (redirects if not authenticated)
const auth = await requireAuth();

// Sign out
await signOut();
```

### Protected Routes
Routes that require authentication should use the `requireAuth()` function or check authentication status in `onMount()`:

```typescript
import { getAuthSession } from '$lib/auth';

onMount(async () => {
    const { user, session } = await getAuthSession();
    
    if (!user || !session) {
        await goto('/auth/signin');
        return;
    }
    
    // User is authenticated, continue with page logic
});
```

## Configuration

### Supabase Setup
1. Configure email templates in Supabase dashboard
2. Set up email redirect URLs:
   - Development: `http://localhost:5173/auth/callback`
   - Production: `https://yourdomain.com/auth/callback`

### Environment Variables
```env
PUBLIC_SUPABASE_URL=your_supabase_url
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## User Experience

### Sign Up Flow
1. User enters email on `/auth/signup`
2. Receives email with 6-digit verification code
3. User enters the code to complete signup
4. Redirected to `/pricing` after successful verification

### Sign In Flow
1. User enters email on `/auth/signin`
2. Receives email with 6-digit verification code
3. User enters the code to complete signin
4. Redirected to `/app` after successful verification

## Security Features

- **Passwordless**: No passwords to store or compromise
- **Time-limited codes**: OTP codes expire after a set time
- **Email verification**: Ensures user owns the email address
- **Session management**: Secure session handling via Supabase
- **Code-only authentication**: No magic links to intercept or compromise

## Development Notes

- OTP codes are 6 digits and numeric only
- Email validation is performed client-side before sending OTP
- Loading states and error handling are implemented throughout
- Clean, accessible UI with proper form validation
- Responsive design works on mobile and desktop
- No magic links sent - users must enter the 6-digit code manually
- Auth callback page redirects to signin (not used in OTP-only flow)