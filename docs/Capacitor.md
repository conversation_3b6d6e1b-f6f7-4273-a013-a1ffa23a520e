# Capacitor notes

Use with svelte https://capacitorjs.com/solution/svelte

## Workflow

### Install

https://capacitorjs.com/solution/svelte

### Workflow
https://capacitorjs.com/docs/basics/workflow

1. Build web code
   1. `npm run build`
2. Sync with native
   1. `npx cap sync`
3. Test with debug native
   1. `npx cap run ios` Will prompt to select device
      1. If want to run with a default device, do
         1. `npx cap run ios --list`
         2. `npx cap run ios --target <target Id>`
         3. Example from list `npx cap run ios --target 193E674A-9E3A-4857-87C7-54CA08117B63`
   2. `npx cap run android`
   3. You can also run your app on iOS via Xcode or run your app on Android via Android Studio as well. Both options are valid for development.
      1. Opening the native project can give you full control over the native runtime of your application. You can create plugins, add custom native code, or compile your application for releasing.
      2. `npx cap build ios --configuration=Release`
      3. To open the iOS Capacitor .xcworkspace project in Xcode
         1. `npx cap open ios`
      4. Similarly, to open the Android Capacitor project in Android Studio
         1. `npx cap open android`
4. Compiling your native binary: After sync, you are encouraged to open your target platform's IDE: Xcode for iOS or Android Studio for Android, for compiling your native app.
   1. Alternatively, to compile your app in a terminal or in CI environments, you can use the cap build command to build the native project, outputting a signed AAB, APK or IPA file ready for distribution to a device or end users.
      1. `npx cap build android`
      2. We also suggest using tools such as Fastlane or a cloud build tool like Appflow to automate these processes for you. While every application is different, we have an example of a general release process for Capacitor projects. Go and read our publishing guides for iOS and Android for more info on how to deploy to the Apple App Store or the Google Play Store.
5. Updating capacitor
   1. Updating your Capacitor runtime is as straightforward as running npm install.
      1. `npm i @capacitor/core @capacitor/ios @capacitor/android`
      2. `npm i -D @capacitor/cli`
   2. https://capacitorjs.com/docs/basics/workflow#updating-capacitor
   3. When updating Capacitor, you want to make sure your Core, Android, and iOS libraries are all the same version. Capacitor Core, Android, and iOS releases are all uploaded simultaneously, meaning that if you install all of the libraries at the same time, you'll be fine!

## Telemetry of capacitor

https://capacitorjs.com/docs/cli/telemetry

Disable with `npx cap telemetry off`

https://capacitorjs.com/docs/cli/commands/run

## IOS Specific Notes

### Troubleshooting

https://capacitorjs.com/docs/ios/troubleshooting


### Native does not have 404

The issue is in your svelte.config.js at line 10: fallback: '200.html'.

This configuration tells the static adapter to create a 200.html file that serves as a
fallback for all routes that don't have a corresponding file. In a native/mobile Capacitor
app, when you navigate to /payment/check (which doesn't exist), instead of showing a 404
error, it falls back to 200.html.

This 200.html file contains your SvelteKit app, which then tries to resolve the route
/payment/check. Since that route doesn't exist, SvelteKit's client-side router defaults to
rendering the root route (/), which is src/routes/+page.svelte.

This is why on mobile/native you see the homepage instead of a 404 error when clicking
"Restore Subscription".

### Need to update App name

Xcode’s Organizer uses your **scheme/project name** (and the `PRODUCT_NAME` setting) as the Archive “Name,” not the `appName` from your Capacitor config. To get “YourBestDays” showing instead of “App” (the default App name) you need to:

## 1. Set the product name (Don't change it after starting project - will fuckup cli and xcode flow so that you can only build or run from xcode)

Alternatively can experiment with https://capacitorjs.com/docs/cli/commands/run later for `--scheme <schemeName>: set the scheme of the iOS project`

When the Organizer window pops up, you should now see:

* **Name** column → **App** is the result of default folder name created in your svelte capacitor project... defaults to `App`
* That name does not appear to your end users so it does not matter. Your end users see values based on your `appName` and `appId` in capacitor config
* The generated `.app` inside the archive named `App.app`

That aligns your Xcode archive naming with your Capacitor `appName`.


### Need to update Version and Build

1. In the Project navigator (⌘-1), under **TARGETS**, click your app target (e.g. **YourBestDays**).
2. In the editor area, switch to the **General** tab.
3. Under **Identity**, you’ll see:

   * **Version** → the “marketing” version (CFBundleShortVersionString).
   * **Build**   → the build number (CFBundleVersion).
4. Click into **Version**, type your new semantic version (e.g. `1.3.0`).
5. Click into **Build**, type your new build number (e.g. `43`).
6. Press ⌘-S to save.
7. (Optional) **Product → Clean Build Folder** (⇧⌘K), then **Product → Build** (⌘-B) or **Archive** (⇧⌘B) to verify the new Version → Build shows up.


### Deploy to testflight

1. **Select a device destination**

   * In Xcode’s toolbar, choose **Generic iOS Device** (or a connected device) as the run destination.

2. **Archive your build**

   * From the menu bar, choose **Product → Archive** (⌥⌘B).
   * Wait for the build to finish; the **Organizer** window will open showing your new archive.

3. **Validate the archive (optional but recommended)**

   * In Organizer, select your archive and click **Validate App**.
   * Follow the prompts to confirm there are no immediate issues.

4. **Distribute to App Store Connect**

   * With the archive still selected, click **Distribute App**.
   * Choose **App Store Connect** → **Upload** → **Next**.

5. **Configure upload options**

   * Ensure **Include symbols**, **Bitcode**, and **Thinning** settings match your needs.
   * Sign in with your Apple ID and select your provisioning profile/team.
   * Click **Upload**; Xcode will package and send your build to App Store Connect.

6. **Monitor the upload**

   * Watch the progress bar in Organizer; when it finishes, you’ll see a confirmation.
   * Click **Done** to close the dialog.

7. **Enable the build in TestFlight**

   * In a browser, go to [App Store Connect](https://appstoreconnect.apple.com/) and sign in.
   * Under **My Apps**, select your app, then the **TestFlight** tab.
   * Find your newly uploaded build, enable internal or external testing, and invite testers.