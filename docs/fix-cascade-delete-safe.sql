-- SAFE CASCADE DELETE FIX FOR SUPABASE
-- This approach is safer and won't trigger destructive warnings

-- Step 1: First, check for any orphaned records that would cause issues
-- Run these queries to identify any problematic data:

-- Check for orphaned profiles
SELECT p.id, p.email 
FROM public.profiles p
LEFT JOIN auth.users u ON p.id = u.id
WHERE u.id IS NULL;

-- Check for orphaned subscriptions
SELECT s.id, s.user_id, s.subscription_id 
FROM public.user_subscriptions s
LEFT JOIN auth.users u ON s.user_id = u.id
WHERE u.id IS NULL;

-- Step 2: Clean up any orphaned records (if found above)
-- Only run these if the queries above return results:
/*
DELETE FROM public.profiles 
WHERE id NOT IN (SELECT id FROM auth.users);

DELETE FROM public.user_subscriptions 
WHERE user_id NOT IN (SELECT id FROM auth.users);
*/

-- Step 3: Alternative approach using RLS policies and functions
-- Instead of relying on CASCADE, we can use a function approach

-- Create a function to safely delete a user and all related data
CREATE OR REPLACE FUNCTION public.delete_user_cascade(user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
BEGIN
  -- Delete from user_subscriptions first (child table)
  DELETE FROM public.user_subscriptions WHERE user_id = delete_user_cascade.user_id;
  
  -- Delete from profiles
  DELETE FROM public.profiles WHERE id = delete_user_cascade.user_id;
  
  -- Finally delete from auth.users
  DELETE FROM auth.users WHERE id = delete_user_cascade.user_id;
END;
$$;

-- Grant execute permission to authenticated users (adjust as needed)
GRANT EXECUTE ON FUNCTION public.delete_user_cascade TO authenticated;

-- Step 4: If you still want to add CASCADE without dropping constraints
-- This approach uses a transaction to ensure safety:

BEGIN;

-- Create new constraints with CASCADE (they'll have different names)
ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_id_fkey_cascade 
FOREIGN KEY (id) 
REFERENCES auth.users(id) 
ON DELETE CASCADE;

ALTER TABLE public.user_subscriptions 
ADD CONSTRAINT user_subscriptions_user_id_fkey_cascade 
FOREIGN KEY (user_id) 
REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- Only after confirming the new constraints work, drop the old ones
-- You can do this in a separate transaction after testing
/*
ALTER TABLE public.profiles DROP CONSTRAINT profiles_id_fkey;
ALTER TABLE public.user_subscriptions DROP CONSTRAINT user_subscriptions_user_id_fkey;
*/

COMMIT;

-- Step 5: Alternative using triggers (safest approach)
-- This doesn't modify existing constraints at all

CREATE OR REPLACE FUNCTION public.cascade_user_delete()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Delete related records when a user is about to be deleted
  DELETE FROM public.user_subscriptions WHERE user_id = OLD.id;
  DELETE FROM public.profiles WHERE id = OLD.id;
  
  RETURN OLD;
END;
$$;

-- Create the trigger
DROP TRIGGER IF EXISTS cascade_user_delete_trigger ON auth.users;
CREATE TRIGGER cascade_user_delete_trigger
  BEFORE DELETE ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.cascade_user_delete();

-- Test query to verify your current constraints:
SELECT
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    rc.delete_rule
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.referential_constraints AS rc
      ON rc.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name IN ('profiles', 'user_subscriptions')
  AND tc.table_schema = 'public';