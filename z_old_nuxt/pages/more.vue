<template>
    <div class="flex flex-col space-y-4">
        <FormBtnMain text="Settings" to="/settings">
            <template #icon>
                <Icon name="uil:setting" class="text-2xl" />
            </template>
        </FormBtnMain>

        <FormBtnMain text="About" to="/about">
            <template #icon>
                <Icon name="uil:user" class="text-2xl" />
            </template>
        </FormBtnMain>

        <FormBtnMain text="Contact" to="/contact">
            <template #icon>
                <Icon name="uil:envelope" class="text-2xl" />
            </template>
        </FormBtnMain>

        <FormBtnMain text="Demos" to="/examples">
            <template #icon>
                <Icon name="garden:asterisk-fill-12" class="text-2xl" />
            </template>
        </FormBtnMain>
    </div>
</template>

<script setup lang="ts">
definePageMeta({
    layout: 'wtabnav'
})
</script>