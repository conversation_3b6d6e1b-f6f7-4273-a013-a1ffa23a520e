<template>
    <div class="container mx-auto p-4 flex flex-col min-h-screen">
        <h1 class="text-2xl font-bold mb-4">Component Playground</h1>

        <div ref="containerRef" class="flex overflow-hidden flex-grow h-full">
            <div ref="leftColumnRef" class="flex-shrink-0 bg-gray-100 p-4 rounded-lg h-full" :style="{ width: leftColWidth }">
                <h2 class="text-xl font-semibold mb-2">Story Controls</h2>
                <div class="mb-4">
                    <label for="storySelect" class="block text-sm font-medium text-gray-700">Select Story:</label>
                    <select id="storySelect" v-model="selectedStory" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option v-for="(story, key) in storyComponents" :key="key" :value="key">
                            {{ key }}
                        </option>
                    </select>
                </div>
            </div>

            <div
                ref="resizerRef"
                class="w-2 h-auto bg-gray-300 cursor-ew-resize flex-shrink-0"
                @mousedown="startResize"
            />

            <div ref="rightColumnRef" class="flex-grow bg-white p-4 rounded-lg shadow-md flex overflow-hidden h-full">
                <div class="border p-4 rounded-md h-full w-full flex flex-col overflow-auto relative">
                    <component :is="selectedStory ? storyComponents[selectedStory] : null" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, shallowRef, type Component, onUnmounted } from 'vue';

const storiesRaw = import.meta.glob<Record<string, Component>>('~/components/**/*.play.vue', { eager: true });

const storyComponents = ref<Record<string, Component>>({});

// Process the raw stories and populate storyComponents
Object.entries(storiesRaw).forEach(([path, module]) => {
    // Extract component name from path
    const match = path.match(/components\/(.+)\/([a-zA-Z0-9]+)\.play\.vue$/);
    if (match && match.length >= 3 && module?.default !== undefined) {
        const folder = match[1];
        const name = match[2];
        // Create display name with folder structure - join with dots for better readability
        const displayName = folder.split('/').join('.') + '.' + name;
        storyComponents.value[displayName] = shallowRef(module.default) as unknown as Component;
    } else {
        // Handle components directly in the components directory
        const directMatch = path.match(/components\/([a-zA-Z0-9]+)\.play\.vue$/);
        if (directMatch && directMatch.length >= 2 && module?.default !== undefined) {
            const name = directMatch[1];
            storyComponents.value[name] = shallowRef(module.default) as unknown as Component;
        }
    }
});

type StoryKeys = keyof typeof storyComponents.value;
const selectedStory = ref<StoryKeys | null>(null);

// Resizable columns logic
const containerRef = ref<HTMLElement | null>(null);
const leftColumnRef = ref<HTMLElement | null>(null);
const resizerRef = ref<HTMLElement | null>(null);

const leftColWidth = ref('33.33%'); // Initial width for left column

let isResizing = false;

const startResize = (_e: MouseEvent): void => {
    isResizing = true;
    document.addEventListener('mousemove', resize);
    document.addEventListener('mouseup', stopResize);
    document.body.style.cursor = 'ew-resize'; // Change cursor globally
    document.body.style.userSelect = 'none'; // Prevent text selection during resize
};

const resize = (e: MouseEvent): void => {
    if (!isResizing || !containerRef.value || !leftColumnRef.value) return;

    const containerRect = containerRef.value.getBoundingClientRect();
    const newLeftWidth = e.clientX - containerRect.left;
    const percentage = (newLeftWidth / containerRect.width) * 100;

    // Ensure minimum width for both columns
    const minWidthPx = 200; // Minimum width in pixels for each column

    if (newLeftWidth > minWidthPx && (containerRect.width - newLeftWidth) > minWidthPx) {
        leftColWidth.value = `${percentage}%`;
    }
};

const stopResize = (): void => {
    isResizing = false;
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopResize);
    document.body.style.cursor = ''; // Reset cursor
    document.body.style.userSelect = ''; // Reset user-select
};

onUnmounted((): void => {
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopResize);
});
</script>
