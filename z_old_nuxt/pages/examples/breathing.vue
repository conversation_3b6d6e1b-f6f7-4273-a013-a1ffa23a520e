<template>
    <div class="h-screen bg-gray-100 flex flex-col space-y-4">
        <BreathingAnimation
            :inhale-duration="6"
            :hold-duration="5"
            :exhale-duration="5"
            :relax-duration="15"
            :min-size="100"
            :max-size="300"
            :animation-start-time="animationStartTime"
            :focus-words="['Calm', 'Focus', 'Release']"
            :countdown="countdown"
            theme-text-color="text-white"
        />

        <NuxtLink 
            to="#" 
            class="flex items-center justify-center gap-2 p-4 rounded-lg bg-black shadow-md transition-colors mx-4"
        >
            <span class="text-yellow-400 text-lg">Next</span>
            <div class="w-6 h-6 text-yellow-400">
                <Icon name="uil:angle-right" class="text-2xl text-yellow-400" />
            </div>
        </NuxtLink>

    </div>
</template>
  
<script setup lang="ts">  
const countdown = ref(3);
const animationStartTime = ref<number | undefined>(undefined);
  
onMounted(() => {
    let c = countdown.value;
    const timer = setInterval(() => {
        c--;
        countdown.value = c;
        if (c <= 0) {
            animationStartTime.value = Date.now() / 1000;
            clearInterval(timer);
        }
    }, 1000);
});
</script>