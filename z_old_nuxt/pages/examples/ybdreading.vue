<template>
    <div>
        <YBDReading 
            v-if="ideas.length > 0"
            :ideas="ideas" 
            :audio-file="audioFile" 
            :between-thoughts="betweenThoughts"
            @update:favorite="handleFavoriteUpdate"
        />
        <div v-else class="flex items-center justify-center h-screen">
            <p class="text-xl text-gray-500">Loading YBD content...</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { YBDMessage, RandomThought, AudioFile } from '~/types/ybd';

definePageMeta({
    layout: 'wbackbtn'
})

// Data refs
const ideas = ref<YBDMessage[]>([]);
const betweenThoughts = ref<RandomThought[]>([]);

// Sample audio file (can be changed to actual file from your server)
const audioFile = ref<AudioFile>({
    id: 'test',
    remoteUrl: 'https://ailsawefdhyfbpvbepui.supabase.co/storage/v1/object/sign/temp.audio.dev/audiobooks/test.mp3?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJ0ZW1wLmF1ZGlvLmRldi9hdWRpb2Jvb2tzL3Rlc3QubXAzIiwiaWF0IjoxNzQxODE0MjU2LCJleHAiOjE3NDE4MTc4NTZ9.7x6klDpL66J3PttAuLhYj6Fov3pEB0800hbaawpVv1c'
});

// Load data from JSON files
onMounted(async () => {
    try {
        // Fetch YBD messages
        const messagesResponse = await fetch('/data/ybd_messages.json');
        if (!messagesResponse.ok) throw new Error('Failed to load YBD messages');
        const messagesData = await messagesResponse.json();
        ideas.value = messagesData;
        
        // Fetch between thoughts
        const thoughtsResponse = await fetch('/data/ybd_between_actions.json');
        if (!thoughtsResponse.ok) throw new Error('Failed to load between thoughts');
        const thoughtsData = await thoughtsResponse.json();
        betweenThoughts.value = thoughtsData;
    } catch (error) {
        console.error('Error loading YBD data:', error);
    }
});

// Favorite update handler
function handleFavoriteUpdate({ id, favorite }: { id: string; favorite: boolean }): void {
    // Find and update the favorite status in the ideas array
    const idea = ideas.value.find(item => item.id === id);
    if (idea) {
        idea.favorite = favorite;
        console.log(`Updated favorite status for ${id} to ${favorite}`);
        
        // In a real app, you might want to persist this change to your backend
        // Example: await $fetch('/api/favorites', { method: 'POST', body: { id, favorite } });
    }
}
</script>
