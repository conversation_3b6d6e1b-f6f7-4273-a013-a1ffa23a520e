<template>
    <div class="max-h-[32rem] overflow-y-auto p-4 space-y-8">
        <!-- Your Inner Focus Section -->
        <div class="space-y-5 pb-6 border-b">
            <h2 class="text-xl font-semibold">Your Inner Focus</h2>
      
            <div>
                <p class="font-medium text-lg mb-4">"I am the creator of meaning in my life."</p>
        
                <p class="text-gray-700 mb-5 leading-relaxed">
                    Let's identify what truly matters to you — beyond what others expect, beyond what looks good on paper. This is about your inner compass. The values you can return to on any day to feel powerful, aligned, and whole.
                </p>
        
                <div class="text-xs text-gray-500 italic border-l-2 border-gray-300 pl-4 py-2 mt-4">
                    Even if your goals are material (like money, success, or appearance), what are you really after? Peace of mind? Respect? Freedom? Let's name those deeper truths.
                </div>
            </div>
        </div>

        <div v-for="(group, idx) in categories" :key="idx" class="mb-6">
            <h2 class="flex items-center text-lg font-semibold mb-2">
                <span class="mr-2">{{ group.icon }}</span>
                {{ group.label }}
            </h2>
            <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                <button
                    v-for="value in group.values"
                    :key="value"
                    class="relative flex items-center justify-between px-4 py-2 border rounded-full text-sm transition"
                    :class="selected.has(value)
                        ? 'bg-indigo-100 border-indigo-500 text-indigo-700'
                        : 'bg-white border-gray-300 text-gray-800 hover:bg-gray-50'"
                    @click="toggle(value)"
                >
                    <span>{{ value }}</span>
                    <span
                        v-if="selected.has(value)"
                        class="ml-2 text-indigo-600"
                    >
                        <!-- Checkmark -->
                        ✓
                    </span>
                </button>
            </div>
        </div>
        <!-- Why Ladder Tool -->
        <div class="mt-4">
            <button
                class="w-full text-left text-sm text-gray-600 hover:text-gray-800"
                @click="showLadder = !showLadder"
            >
                <span class="font-medium">Can’t decide? Try this...</span>
            </button>
            <div v-if="showLadder" class="mt-2 p-3 bg-gray-50 rounded border border-gray-200 text-sm space-y-1">
                <p>Think of something you want — money, a dream house, a big opportunity. Then ask yourself:</p>
                <ol class="list-decimal list-inside space-y-1">
                    <li>Why do I want this?</li>
                    <li>What do I feel I would be or would happen if I didn’t achieve this?</li>
                    <li>And why does that matter to me?</li>
                    <li>What would I value if I already had all material things I want?</li>
                </ol>
                <p>Repeat until you find the deeper value or belief behind the external.</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const categories = [
    {
        icon: '🌱',
        label: 'Inner Security',
        values: ['Emotional Stability', 'Freedom', 'Respect', 'Confidence', 'Security', 'Daily habits', 'Resilience'],
    },
    {
        icon: '🧭',
        label: 'Self & Growth',
        values: ['Integrity', 'Growth', 'Self-Respect', 'Learning', 'Awareness', 'Resilience'],
    },
    {
        icon: '💛',
        label: 'Relationships & Connection',
        values: ['Family', 'Loyalty', 'Friendship', 'Love', 'Belonging', 'Compassion'],
    },
    {
        icon: '🕊️',
        label: 'Peace & Presence',
        values: ['Calm', 'Gratitude', 'Faith', 'Forgiveness', 'Presence', 'Joy'],
    },
    {
        icon: '🔥',
        label: 'Expression & Drive',
        values: ['Creativity', 'Purpose', 'Play', 'Health', 'Adventure', 'Humor'],
    },
]

const selected = ref(new Set())
const showLadder = ref(false)

function toggle(item: string): void {
    if (selected.value.has(item)) {
        selected.value.delete(item)
    } else if (selected.value.size < 5) {
        selected.value.add(item)
    }
}
</script>
