<template>
    <div class="flex flex-col items-center bg-gray-100">
        <h1 class="text-4xl font-bold mb-2">Welcome to My Nuxt App</h1>

        <DevOnly>
            <div>Debugging only rendered in dev. is removed from production builds automatically</div>
        </DevOnly>

        <ACard
            class="mt-8"
            icon="lucide:leaf" 
            title="Recommendations" 
            content="See unread YBD messages and activities for the moment. These can support a mindset for your goals based on your profile."
            count="5"
            destination="/ybd"
        />

        <ACard 
            icon="fa6-solid:bell" 
            title="Recommendations" 
            content="See unread YBD messages and activities for the moment. These can support a mindset for your goals based on your profile."
        />

        <div class="flex flex-col gap-4">
            <h1>Examples</h1>

            <NuxtLink
                to="/examples/ybdreading"
                class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition">
                YBD Reading example
            </NuxtLink>

            <NuxtLink
                to="/examples/sound_loop"
                class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition">
                Sound Loop
            </NuxtLink>

            <NuxtLink
                to="/examples/sound_fullplayer"
                class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition">
                Sound Full Player
            </NuxtLink>

            <NuxtLink
                to="/examples/sound_interact"
                class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition">
                Sound Interact
            </NuxtLink>

            <NuxtLink
                to="/examples/breathing"
                class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition">
                Breathing
            </NuxtLink>


            <NuxtLink
                to="/contact"
                class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition">
                Contact Us
            </NuxtLink>

            <NuxtLink
                to="/signup"
                class="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition">
                Sign Up
            </NuxtLink>
        </div>

        <div class="p-4 space-y-3">
            <p v-for="i in 50" :key="i">Scrollable content {{ i }}</p>
        </div>
    </div>
</template>

<script setup lang="ts">
definePageMeta({
    layout: 'wbackbtn'
})
</script>