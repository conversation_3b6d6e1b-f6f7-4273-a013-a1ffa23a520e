# Component Playground

This `dev-playground.vue` file serves as a dynamic component testing environment. It allows developers to quickly preview and interact with individual Vue components in isolation.

## Why

It was created as a quick basic alternative to Storybook and Histoire. Storybook is much heavier than we need and requires complex setup for simple display. <PERSON><PERSON> solves the complexity problem but doesn't keep npm modules up to date which leads to vulnerabilities in code bases. This is one of many simple ways to accomplish the goal - quick and dirty component display/preview/playground.

## Works With

This playground works with Vue and Nuxt 3. When using Nuxt 3, it's unnecessary to manually import original component.

## How it Works

The playground dynamically imports all files ending with `.play.vue` from the `~/components` directory and its subdirectories. These `.play.vue` files are treated as "stories" for your components. Each `.play.vue` file should typically import and render one or more instances of a component you wish to test, showcasing different states or variations.

The `dev-playground.vue` file then creates a dropdown list populated with the names of these "stories" (derived from their filenames). When a story is selected from the dropdown, the corresponding component defined in the `.play.vue` file is rendered in the preview area.

## How to Use It

1.  **Access the Playground:** Navigate to the `/dev-playground` route in your application (e.g., `http://localhost:3000/dev-playground`).

2.  **Select a Story:** Use the "Select Story" dropdown to choose a component story you want to preview. The preview area will update to display the selected component.

3.  **Create New Stories:** To add a new component to the playground:
    *   Create a new Vue file with the `.play.vue` extension (e.g., `MyComponent.play.vue`) inside any subdirectory of `~/components`.
    *   Inside this `.play.vue` file, import the component you want to test and render it within its `<template>` section. You can include multiple instances of the component with different props or states to demonstrate various scenarios.

    **Example `components/Form/MyComponent.play.vue`:**

    ```vue
    <template>
      <div class="p-4 border rounded-md">
        <h3 class="text-lg font-semibold mb-2">My Component - Default</h3>
        <MyComponent :prop1="value1" />

        <h3 class="text-lg font-semibold mt-4 mb-2">My Component - Variant B</h3>
        <MyComponent :prop1="value2" variant="B" />
      </div>
    </template>

    <script setup lang="ts">
    import MyComponent from './MyComponent.vue'; // Adjust path as needed
    </script>

    <style scoped>
    /* Optional: Add specific styles for this story */
    </style>
    ```

    Once you save your new `.play.vue` file, it should automatically appear in the "Select Story" dropdown in the playground.
