# useAudioPlayer Composable

A Vue/Nuxt 3 composable providing a reactive audio player with support for gapless looping, seamless crossfade, and full playback controls via the Web Audio API.

---

## Installation

1. Copy the `useAudioPlayer.ts` file into your project's `composables/` directory.
2. Ensure your app runs in a browser or WebView that supports the Web Audio API.
3. (Optional) Place built-in audio files under `public/audio/` and reference them by `/audio/filename.ext`.

---

## API Reference

### `AudioCommand`

| Type    | Description                                    |
|---------|------------------------------------------------|
| `play`  | Start or resume playback.                      |
| `pause` | Pause playback.                                |
| `stop`  | Stop playback and reset to the beginning.      |

### `AudioOptions`

| Property            | Type      | Default | Description                                                                                  |
|---------------------|-----------|---------|----------------------------------------------------------------------------------------------|
| `command`           | `string`  | —       | One of `'play'`, `'pause'`, or `'stop'`.                                                     |
| `loop`              | `boolean` | `false` | Whether to loop the audio seamlessly (uses Web Audio API crossfading).                       |
| `fileLocation`      | `string`  | —       | URL or path to the audio file (MP3, WAV, OGG, etc.).                                         |
| `crossfadeDuration` | `number`  | `0.04`  | Crossfade duration in seconds when looping (`0.04` = 40ms).                                   |

### `AudioPlayer`

An object returned by `useAudioPlayer()`:

| Property      | Type                         | Description                                                      |
|---------------|------------------------------|------------------------------------------------------------------|
| `sendCommand` | `(opts: AudioOptions) => Promise<void>` | Send a playback command with options.                            |
| `isPlaying`   | `Ref<boolean>`               | Reactive boolean indicating if audio is currently playing.       |
| `duration`    | `Ref<number>`                | Reactive total duration of the audio in seconds.                 |
| `timeLeft`    | `Ref<number>`                | Reactive remaining time left (resets each loop).                |
| `error`       | `Ref<Error | null>`      | Reactive Error object if playback or loading failed; otherwise `null`. |

### `useAudioPlayer()`

Returns the `AudioPlayer` API. Call `sendCommand()` whenever you need to control playback.

---

## Example audio
https://dn721604.ca.archive.org/0/items/samples-and-examples-vol.-1/%231/CD%201/01-Walt%20Barr-Free%20Spirits%20%28Freddie%20Gibbs%20%26%20Madlib-Crime%20Pays%29.mp3

https://archive.org/details/samples-and-examples-vol.-1/%231/CD+1/01-Walt+Barr-Free+Spirits+(Freddie+Gibbs+%26+Madlib-Crime+Pays).mp3



## Examples

### 1. Simple Loop Toggle

A compact play/pause button that loops an ambient track seamlessly.

```vue
<template>
  <button @click="toggle()" class="px-4 py-2 bg-indigo-600 text-white rounded">
    {{ isPlaying ? 'Pause Ambience' : 'Play Ambience' }}
  </button>
</template>

<script setup lang="ts">
import { useAudioPlayer } from '~/composables/useAudioPlayer';
import { watch } from 'vue';

const { sendCommand, isPlaying, error } = useAudioPlayer();
const file = '/audio/forest_ambience.wav';

watch(error, (err) => {
  if (err) console.error('Audio error:', err.message);
});

function toggle() {
  sendCommand({
    command: isPlaying.value ? 'pause' : 'play',
    loop: true,
    fileLocation: file,
    crossfadeDuration: 0.05, // 50ms crossfade
  });
}
</script>
```

### 2. Full Player with Stop & Time Display

A traditional audio player with Play, Pause, Stop buttons and a live time display.

```vue
<template>
  <div class="space-x-2">
    <button @click="play()" class="px-3 py-1 bg-green-500 rounded">Play</button>
    <button @click="pause()" class="px-3 py-1 bg-yellow-500 rounded">Pause</button>
    <button @click="stop()" class="px-3 py-1 bg-red-500 rounded">Stop</button>
    <span>{{ timeLeft.toFixed(1) }}s / {{ duration.toFixed(1) }}s</span>
  </div>
</template>

<script setup lang="ts">
import { useAudioPlayer } from '~/composables/useAudioPlayer';

const { sendCommand, timeLeft, duration, error } = useAudioPlayer();
const url = '/audio/mountain_stream.mp3';

sendCommand({ command: 'stop', fileLocation: url }); // preload

function play() {
  sendCommand({ command: 'play', loop: false, fileLocation: url });
}
function pause() {
  sendCommand({ command: 'pause', loop: false, fileLocation: url });
}
function stop() {
  sendCommand({ command: 'stop', loop: false, fileLocation: url });
}

if (error.value) console.error('Playback error:', error.value.message);
</script>
```

### 3. Handling Errors Gracefully

Display an error message in the UI if loading or playback fails.

```vue
<template>
  <div>
    <button @click="play()">Play</button>
    <p v-if="error" class="text-red-600">Error: {{ error.message }}</p>
  </div>
</template>

<script setup lang="ts">
import { useAudioPlayer } from '~/composables/useAudioPlayer';

const { sendCommand, error } = useAudioPlayer();

function play() {
  sendCommand({ command: 'play', loop: false, fileLocation: '/audio/notfound.mp3' });
}
</script>
```

