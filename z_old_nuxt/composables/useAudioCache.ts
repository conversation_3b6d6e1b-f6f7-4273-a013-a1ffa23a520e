/**
 * Audio caching system for offline playback
 * Uses IndexedDB to store audio files for use in WebView environments
 */

export function useAudioCache(): {
    isAudioCached: (url: string) => Promise<boolean>;
    getCachedAudio: (url: string) => Promise<ArrayBuffer | null>;
    downloadAndCacheAudio: (url: string) => Promise<ArrayBuffer>;
    getAudioBlobUrl: (url: string) => Promise<string>;
    preloadAudio: (url: string) => void;
    } {
    const DB_NAME = 'audio-cache-db';
    const STORE_NAME = 'audio-files';
    const DB_VERSION = 1;

    // Track downloads in progress to prevent duplicates
    const pendingDownloads = new Map<string, Promise<ArrayBuffer>>();

    /**
   * Open the IndexedDB database
   */
    function openDatabase(): Promise<IDBDatabase> {
        return new Promise((resolve, reject): void => {
            const request = indexedDB.open(DB_NAME, DB_VERSION);
      
            request.onerror = (): void => reject(request.error);
            request.onsuccess = (): void => resolve(request.result);
      
            request.onupgradeneeded = (): void => {
                const db = request.result;
                if (!db.objectStoreNames.contains(STORE_NAME)) {
                    db.createObjectStore(STORE_NAME);
                }
            };
        });
    }

    /**
   * Save audio data to the cache
   */
    async function saveToCache(url: string, data: ArrayBuffer): Promise<void> {
        const db = await openDatabase();
        return new Promise((resolve, reject): void => {
            const transaction = db.transaction(STORE_NAME, 'readwrite');
            const store = transaction.objectStore(STORE_NAME);
      
            const request = store.put(data, url);
      
            request.onerror = (): void => reject(request.error);
            request.onsuccess = (): void => resolve();
      
            transaction.oncomplete = (): void => db.close();
        });
    }

    /**
   * Check if an audio file is already cached
   */
    async function isAudioCached(url: string): Promise<boolean> {
        if (!(url?.startsWith('http'))) return false;
    
        try {
            const db = await openDatabase();
            return new Promise((resolve): void => {
                const transaction = db.transaction(STORE_NAME, 'readonly');
                const store = transaction.objectStore(STORE_NAME);
        
                const request = store.getKey(url);
        
                request.onsuccess = (): void => resolve(request.result !== undefined);
                request.onerror = (): void => resolve(false);
        
                transaction.oncomplete = (): void => db.close();
            });
        } catch (error) {
            console.error('Error checking cached audio:', error);
            return false;
        }
    }

    /**
   * Get cached audio data
   */
    async function getCachedAudio(url: string): Promise<ArrayBuffer | null> {
        if (!(url?.startsWith('http'))) return null;
    
        try {
            const db = await openDatabase();
            return new Promise((resolve, reject): void => {
                const transaction = db.transaction(STORE_NAME, 'readonly');
                const store = transaction.objectStore(STORE_NAME);
        
                const request = store.get(url);
        
                request.onerror = (): void => reject(request.error);
                request.onsuccess = (): void => resolve(request.result ?? null);
        
                transaction.oncomplete = (): void => db.close();
            });
        } catch (error) {
            console.error('Error getting cached audio:', error);
            return null;
        }
    }

    /**
   * Download and cache an audio file
   */
    async function downloadAndCacheAudio(url: string): Promise<ArrayBuffer> {
        if (!(url?.startsWith('http'))) {
            throw new Error('Invalid URL for caching');
        }
    
        // Check if already downloading
        if (pendingDownloads.has(url)) {
            const download = pendingDownloads.get(url);
            if (download) {
                return download;
            }
        }
    
        const downloadPromise = (async (): Promise<ArrayBuffer> => {
            try {
                // Check cache first
                const cachedData = await getCachedAudio(url);
                if (cachedData !== null) {
                    return cachedData;
                }
        
                // Download the file
                console.log(`Downloading audio: ${url}`);
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`Failed to download: ${response.status}`);
                }
        
                const buffer = await response.arrayBuffer();
        
                // Save to cache
                await saveToCache(url, buffer);
                console.log(`Cached audio: ${url}`);
        
                return buffer;
            } finally {
                pendingDownloads.delete(url);
            }
        })();
    
        pendingDownloads.set(url, downloadPromise);
        return downloadPromise;
    }

    /**
   * Get a blob URL for an audio file
   * Returns a blob URL if cached, or original URL otherwise
   */
    async function getAudioBlobUrl(url: string): Promise<string> {
        if (!(url?.startsWith('http'))) return url;
    
        try {
            // Check cache first
            const audioData = await getCachedAudio(url);
      
            if (audioData !== null) {
                // Create blob URL from cached data
                const blob = new Blob([audioData], { type: 'audio/mpeg' });
                return URL.createObjectURL(blob);
            }
      
            // If not cached, start download in background but return original URL
            downloadAndCacheAudio(url).catch((err): void => {
                console.error('Background download failed:', err);
            });
      
            return url;
        } catch (error) {
            console.error('Error creating blob URL:', error);
            return url;
        }
    }

    /**
   * Preload an audio file in the background
   */
    function preloadAudio(url: string): void {
        if (!(url?.startsWith('http'))) return;
    
        // Check if already cached, if not download in background
        isAudioCached(url).then((cached): void => {
            if (!cached) {
                console.log(`Preloading audio: ${url}`);
                downloadAndCacheAudio(url).catch((err): void => {
                    console.error('Preload failed:', err);
                });
            }
        });
    }

    return {
        isAudioCached,
        getCachedAudio,
        downloadAndCacheAudio,
        getAudioBlobUrl,
        preloadAudio
    };
}
