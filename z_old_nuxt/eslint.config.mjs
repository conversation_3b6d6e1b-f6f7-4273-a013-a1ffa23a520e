// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs'
import vueParser from 'vue-eslint-parser'
import tsParser from '@typescript-eslint/parser'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

// Create equivalent of __dirname for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

export default withNuxt({
    languageOptions: {
        parser: vueParser,
        parserOptions: {
            parser: tsParser,
            project: './tsconfig.json',
            extraFileExtensions: ['.vue'],
            tsconfigRootDir: __dirname,
        },
    },
    rules: {
        // Indentation rules
        indent: ['error', 4],
        'vue/html-indent': ['error', 4],
        
        // Disable vue/script-indent to avoid conflict with indent rule
        'vue/script-indent': 'off',
        
        // TypeScript strict mode rules
        '@typescript-eslint/strict-boolean-expressions': 'error',
        '@typescript-eslint/no-explicit-any': 'error',
        '@typescript-eslint/explicit-function-return-type': 'error',
        '@typescript-eslint/no-unused-vars': 'error',
        '@typescript-eslint/no-non-null-assertion': 'error',
        '@typescript-eslint/no-unnecessary-type-assertion': 'error',
        '@typescript-eslint/prefer-nullish-coalescing': 'error',
        '@typescript-eslint/prefer-optional-chain': 'error'
    }
})
