# Nuxt Minimal Starter

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

# PWA
https://nuxt.com/modules/vite-pwa-nuxt

# Tailwind
https://tailwindcss.com/docs/installation/framework-guides/nuxt




to prep for ios

npm run build
then
npm run generate




1. Create a new folder in your Xcode project called "nuxt-dist"
2. Right-click your project in Xcode → Add Files to "YourBestDays"
3. Select your Nuxt static build output folder (usually .output/public or dist)
4. Make sure "Copy items if needed" is checked and "Create folder references" is selected
5. Add the files to your main app target

The WebView will look for index.html in a folder called "nuxt-dist". Make sure your Nuxt static files are properly bundled and included in your Xcode project.

if issue:

1. Make sure your "nuxt-dist" folder is properly added to your Xcode project:

- In Xcode, click on your project in the navigator
- Select your target
- Go to "Build Phases" > "Copy Bundle Resources"
- Verify that your "nuxt-dist" folder is listed there







Fastest fix (takes < 1 min once)  
1. In the target’s Build Phases → Copy Bundle Resources  
– Select every file that lives under nuxt-dist / public (all the html, js, css, images).  
– Press Delete to remove them from that phase (they stay on disk).  
2. In the Project Navigator, drag the *folder* nuxt-dist/public into your Xcode project.  
– In the dialog, tick your app target.  
– Most important: choose “Create folder references” (blue folder), not “Create groups”.  
– Keep “Copy items if needed” *unchecked* so the folder stays where it is in the repo.  

Why this works  
• A blue “folder reference” is copied into the bundle as a folder, hierarchy intact.  
• Xcode no longer generates individual CpResource commands, so no duplicate index.html names are produced, no matter how many there are.  







TODO:

- Disable gesture zoom and other web gestures so the web app works like a regular ios app, not web app