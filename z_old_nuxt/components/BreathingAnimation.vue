<template>
    <div class="flex flex-col w-full h-fulls">
        <!-- Top section: fixed half-screen height, circle always centered here -->
        <div class="h-[50vh] flex items-center justify-center">
            <!-- Countdown Phase -->
            <div v-if="countdownValue > 0" class="flex items-center justify-center">
                <span class="text-6xl text-black">{{ countdownValue }}</span>
            </div>
            <!-- Circle + Phase Text -->
            <div
                v-else
                class="relative"
                :style="{ width: `${size}px`, height: `${size}px` }"
            >
                <div class="absolute inset-0" :style="gradientStyle" />
                <div class="absolute inset-0 z-10 flex flex-col items-center justify-center text-center">
                    <p :class="themeClass" class="font-semibold">{{ phase }}</p>
                    <p :class="themeClass" class="text-sm">{{ Math.ceil(remaining) }}</p>
                </div>
            </div>
        </div>
  
        <!-- Bottom section: focus words (and anything else) -->
        <div class="w-full flex justify-center py-4">
            <BreathingFocusWord
                :focus-words="focusWords"
                :cycle-time="cycleTime"
                :inhale-duration="inhaleDuration"
                :breathing-cycle-duration="breathingCycleDuration"
                :elapsed="elapsed"
            />
        </div>
    </div>
</template>  

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import BreathingFocusWord from '~/components/BreathingFocusWord.vue'

interface Props {
    inhaleDuration: number
    holdDuration: number
    exhaleDuration: number
    relaxDuration: number
    minSize: number
    maxSize: number
    animationStartTime?: number
    focusWords: string[]
    countdown: number
    themeTextColor?: string
}

const props = defineProps<Props>()
const {
    inhaleDuration,
    holdDuration,
    exhaleDuration,
    relaxDuration,
    minSize,
    maxSize,
    animationStartTime: externalAnimationStartTime,
    focusWords,
    countdown: initialCountdown,
    themeTextColor
} = props

// Internal countdown state that will be decremented
const countdownValue = ref(initialCountdown)
// Rename internal animation start ref to avoid collision with prop
const animationStart = ref<number | undefined>(externalAnimationStartTime)

// Theme text color utility
const themeClass = computed(() => themeTextColor ?? 'text-gray-700')

// Full breathing cycle duration
const breathingCycleDuration = computed(
    () => inhaleDuration + holdDuration + exhaleDuration + relaxDuration
)

// Time tracking via requestAnimationFrame
const now = ref(performance.now() / 1000)
let rafId: number
let countdownInterval: number | null = null

function tick(): void {
    now.value = performance.now() / 1000
    rafId = requestAnimationFrame(tick)
}

function startCountdown(): void {
    if (countdownInterval !== null) clearInterval(countdownInterval)

    countdownInterval = window.setInterval(() => {
        if (countdownValue.value > 1) {
            countdownValue.value--
        } else {
            countdownValue.value = 0
            clearInterval(countdownInterval as number)
            countdownInterval = null

            // Use nullish coalescing assignment
            animationStart.value ??= performance.now() / 1000
        }
    }, 1000)
}

onMounted(() => {
    rafId = requestAnimationFrame(tick)

    if (countdownValue.value > 0) {
        startCountdown()
    }
})

onUnmounted(() => {
    cancelAnimationFrame(rafId)
    if (countdownInterval !== null) clearInterval(countdownInterval)
})

// Compute elapsed and cycle time using the renamed ref
const elapsed = computed(() => now.value - (animationStart.value ?? now.value))
const cycleTime = computed(() => {
    const mod = elapsed.value % breathingCycleDuration.value
    return mod < 0 ? mod + breathingCycleDuration.value : mod
})

// Determine current phase label
const phase = computed(() => {
    const t = cycleTime.value
    if (t < inhaleDuration) return 'Inhale'
    if (t < inhaleDuration + holdDuration) return 'Hold'
    if (t < inhaleDuration + holdDuration + exhaleDuration) return 'Exhale'
    return 'Breathe normally'
})

// Circle size interpolation
const size = computed(() => {
    const t = cycleTime.value
    if (t < inhaleDuration) {
        const f = t / inhaleDuration
        return minSize + (maxSize - minSize) * f
    }
    if (t < inhaleDuration + holdDuration) return maxSize
    if (t < inhaleDuration + holdDuration + exhaleDuration) {
        const f = (t - inhaleDuration - holdDuration) / exhaleDuration
        return maxSize - (maxSize - minSize) * f
    }
    return minSize
})

// Remaining time in current phase
const remaining = computed(() => {
    const t = cycleTime.value
    if (t < inhaleDuration) return inhaleDuration - t
    if (t < inhaleDuration + holdDuration) return inhaleDuration + holdDuration - t
    if (t < inhaleDuration + holdDuration + exhaleDuration)
        return inhaleDuration + holdDuration + exhaleDuration - t
    return breathingCycleDuration.value - t
})

// Radial gradient style
const gradientStyle = computed(() => ({
    background: 'radial-gradient(circle at center, rgba(59,130,246,0.8) 0%, rgba(59,130,246,0.3) 100%)',
    borderRadius: '9999px'
}))
</script>
