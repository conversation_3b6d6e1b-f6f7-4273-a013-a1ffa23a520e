<template>
    <NuxtLink 
        :to="to" 
        class="flex items-center p-4 rounded-lg bg-white shadow-md hover:bg-gray-50 transition-colors"
    >
        <div class="w-6 h-6 text-yellow-400 mr-3">
            <slot name="icon" />
        </div>
        <span class="text-gray-900 text-lg">{{ text }}</span>
    </NuxtLink>
</template>
  
<script setup lang="ts">
/**
   * A reusable button component that combines an icon with text and navigation functionality
   * @example
   * ```vue
   * <BtnMain text="Settings" to="/settings">
   *   <template #icon>
   *     <Icon name="uil:setting" />
   *     <!-- Or use custom SVG -->
   *     <!-- <svg>...</svg> -->
   *   </template>
   * </BtnMain>
   * ```
   */

interface ButtonProps {
    /** The text to display next to the icon */
    text: string
    /** The route destination when the button is clicked */
    to: string
}

defineProps<ButtonProps>()
</script>