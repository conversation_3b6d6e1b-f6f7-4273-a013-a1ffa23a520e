<template>
    <NuxtLink 
        v-if="destination"
        :to="destination"
        class="relative border border-slate-200 rounded-lg p-6 mb-4 bg-white shadow-sm flex flex-col cursor-pointer hover:shadow-md transition-shadow duration-200"
    >
        <div class="flex flex-col">
            <div class="flex items-center gap-3 mb-3">
                <div class="text-slate-600 flex items-center">
                    <Icon v-if="icon" :name="icon" size="2rem" />
                    <Icon v-else name="fa6-solid:feather" size="2rem" />
                </div>
                <h2 class="text-xl font-semibold text-slate-800 m-0">{{ title }}</h2>
            </div>
            <hr class="border-t border-slate-200 w-full mb-3" >
            <p class="text-slate-600 leading-normal">{{ content }}</p>
        </div>
        <div v-if="count !== undefined" class="absolute -top-2.5 -right-2.5 bg-slate-100 text-slate-500 rounded-full w-8 h-8 flex items-center justify-center font-bold text-base border border-slate-200">
            {{ count }}
        </div>
    </NuxtLink>
  
    <!-- Non-clickable version if no destination is provided -->
    <div 
        v-else
        class="relative border border-slate-200 rounded-lg p-6 mb-4 bg-white shadow-sm flex flex-col"
    >
        <div class="flex flex-col">
            <div class="flex items-center gap-3 mb-3">
                <div class="text-slate-600 flex items-center">
                    <Icon v-if="icon" :name="icon" size="2rem" />
                    <Icon v-else name="fa6-solid:feather" size="2rem" />
                </div>
                <h2 class="text-2xl font-semibold text-slate-800 m-0">{{ title }}</h2>
            </div>
            <hr class="border-t border-slate-200 w-full mb-3" >
            <p class="text-slate-600 leading-normal">{{ content }}</p>
        </div>
        <div v-if="count !== undefined" class="absolute -top-2.5 -right-2.5 bg-slate-100 text-slate-500 rounded-full w-8 h-8 flex items-center justify-center font-bold text-base border border-slate-200">
            {{ count }}
        </div>
    </div>
</template>

<script setup lang="ts">
interface CardProps {
  icon?: string | null;
  title: string;
  content: string;
  count?: number | string;
  destination?: string;
}

defineProps<CardProps>();
</script>