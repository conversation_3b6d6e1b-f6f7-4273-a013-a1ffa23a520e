<template>
    <div class="audio-player p-4 bg-white rounded shadow max-w-md mx-auto">
        <audio
            ref="audioRef" preload="metadata" @loadedmetadata="onLoadedMetadata" @progress="onProgress" @timeupdate="onTimeUpdate" @ended="onEnded"
            @error="onMediaError" @waiting="onWaiting" @canplaythrough="onCanPlay">
            <source :src="effectiveSrc" >
            Audio playback not supported.
        </audio>

        <div class="controls flex items-center space-x-4 mb-2">
            <button
                :disabled="isLoading || isBuffering" class="flex items-center px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50"
                @click="togglePlay">
                <Icon :name="isPlaying ? 'lucide-pause' : 'lucide-play'" class="h-5 w-5" />
            </button>

            <button
                :disabled="(!isPlaying && currentTime === 0) || isLoading || isBuffering" class="flex items-center px-3 py-1 bg-red-500 text-white rounded disabled:opacity-50"
                @click="stop">
                <Icon name="lucide-stop-circle" class="h-5 w-5" />
            </button>

            <div class="flex items-center space-x-2">
                <div v-if="isLoading || isBuffering">
                    <Icon name="lucide-loader" class="animate-spin h-5 w-5 text-gray-600" />
                </div>
                <div v-else-if="duration">
                    {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
                </div>
            </div>
            
            <!-- Offline cache status indicator -->
            <div v-if="props.src && props.src.startsWith('http')" class="ml-auto flex items-center">
                <div v-if="isCached" class="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full flex items-center">
                    <Icon name="lucide-hard-drive" class="h-3 w-3 mr-1" />
                    <span>Cached</span>
                </div>
                <div v-else class="text-xs px-2 py-0.5 bg-yellow-100 text-yellow-800 rounded-full flex items-center">
                    <Icon name="lucide-wifi" class="h-3 w-3 mr-1" />
                    <span>Online</span>
                </div>
            </div>
        </div>

        <PlayerAudioSeekBar
            :current-time="currentTime" :duration="duration" :buffered-percent="bufferedPercent"
            @update:current-time="onSeek" />

        <div v-if="error" class="text-sm text-red-600">
            ⚠️ {{ error }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
import { useAudioCache } from '~/composables/useAudioCache'

const props = defineProps<{ src: string }>()

const audioRef = ref<HTMLAudioElement | null>(null)
const isPlaying = ref(false)
const isLoading = ref(false)
const isBuffering = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const bufferedTime = ref(0)
const error = ref<string | null>(null)
const tickId = ref<number | null>(null)
const effectiveSrc = ref<string>(props.src)
const isCached = ref<boolean>(false)

// Initialize audio cache system
const { isAudioCached, getAudioBlobUrl, preloadAudio } = useAudioCache()

const MEDIA_ERROR_MESSAGES: Record<number, string> = {
    1: 'Network error while fetching audio.',
    2: 'Decoding error – audio not playable.',
    3: 'Format not supported by your browser.',
    4: 'Playback aborted.',
}

// percentage of file buffered
const bufferedPercent = computed(
    () => (duration.value > 0 ? (bufferedTime.value / duration.value) * 100 : 0)
)

// pause this player if another one starts
function onOtherPlayed(e: CustomEvent): void {
    if (e.detail !== audioRef.value) pause()
}

onMounted(() => {
    window.addEventListener('audio-played', onOtherPlayed as EventListener)
    
    // Check if audio is already cached and update the source
    if (props.src?.startsWith('http')) {
        checkCacheAndUpdateSource()
        
        // Preload audio in the background for offline use
        preloadAudio(props.src)
    }
    
    audioRef.value?.load()
})

onBeforeUnmount(() => {
    window.removeEventListener('audio-played', onOtherPlayed as EventListener)
    if (audioRef.value) {
        audioRef.value.pause()
        audioRef.value.src = ''
    }
    stopTicker()
})

function formatTime(sec: number): string {
    const m = Math.floor(sec / 60)
    const s = Math.floor(sec % 60)
        .toString()
        .padStart(2, '0')
    return `${m}:${s}`
}

function onLoadedMetadata(): void {
    if (audioRef.value) {
        duration.value = audioRef.value.duration
    }
}

function updateBufferProgress(): void {
    const audio = audioRef.value
    if (audio === null || audio === undefined) return

    const buf = audio.buffered
    if (buf.length > 0) {
        // Find the range that contains the current time
        let currentRange = -1
        for (let i = 0; i < buf.length; i++) {
            if (audio.currentTime >= buf.start(i) && audio.currentTime <= buf.end(i)) {
                currentRange = i
                break
            }
        }

        // If we found a range containing current time, use that, otherwise use the last range
        if (currentRange !== -1) {
            bufferedTime.value = buf.end(currentRange)
        } else if (buf.length > 0) {
            bufferedTime.value = buf.end(buf.length - 1)
        }
    }
}

function onProgress(): void {
    updateBufferProgress()
}

function onTimeUpdate(): void {
    updateBufferProgress()
}

/**
 * Check if the audio file is cached and update the source accordingly
 */
async function checkCacheAndUpdateSource(): Promise<void> {
    if (!props.src?.startsWith('http')) {
        effectiveSrc.value = props.src
        return
    }
    
    // Check if already cached
    isCached.value = await isAudioCached(props.src)
    
    // Get the blob URL or original URL
    const blobUrl = await getAudioBlobUrl(props.src)
    effectiveSrc.value = blobUrl
    
    // If the audio source changed while audio is already loaded, reload it
    if (audioRef.value?.src !== blobUrl) {
        audioRef.value?.load()
    }
}

async function validateUrl(): Promise<boolean> {
    if (!props.src) {
        error.value = 'No audio source provided'
        return false
    }
    
    // If it's not an HTTP URL or it's already cached, no need to validate
    if (!props.src.startsWith('http') || isCached.value) return true
    
    try {
        const res = await fetch(props.src, { method: 'HEAD' })
        if (!res.ok) {
            error.value =
                res.status === 404
                    ? 'File not found (404)'
                    : res.status === 403
                        ? 'Access denied (403)'
                        : `HTTP error ${res.status}`
            return false
        }
    } catch (e: unknown) {
        // Check if we have it cached, if so we can still play even offline
        const cached = await isAudioCached(props.src)
        if (cached) {
            isCached.value = true
            return true
        }
        
        const errorMessage = e instanceof Error ? e.message : String(e)
        error.value = `Network error: ${errorMessage}`
        return false
    }
    return true
}

async function play(): Promise<void> {
    if (!audioRef.value) return
    window.dispatchEvent(
        new CustomEvent('audio-played', { detail: audioRef.value })
    )

    error.value = null
    isLoading.value = true

    if (!(await validateUrl())) {
        isLoading.value = false
        return
    }

    try {
        await audioRef.value.play()
        duration.value = audioRef.value.duration ?? duration.value
        isPlaying.value = true
        startTicker()
    } catch (e: unknown) {
        const err = e as Error
        error.value = err.message ?? 'Cannot play audio.'
    } finally {
        isLoading.value = false
    }
}

function pause(): void {
    if (!audioRef.value) return
    audioRef.value.pause()
    isPlaying.value = false
    stopTicker()
}

function stop(): void {
    if (!audioRef.value) return
    audioRef.value.pause()
    audioRef.value.currentTime = 0
    isPlaying.value = false
    currentTime.value = 0
    stopTicker()
}

function togglePlay(): void {
    if (isPlaying.value) {
        pause()
    } else {
        play()
    }
}

function tick(): void {
    if (audioRef.value) {
        currentTime.value = audioRef.value.currentTime
        tickId.value = requestAnimationFrame(tick)
    }
}

function startTicker(): void {
    if (tickId.value === null) {
        tick()
    }
}

function stopTicker(): void {
    if (tickId.value !== null) {
        cancelAnimationFrame(tickId.value)
        tickId.value = null
    }
}

function onEnded(): void {
    stop()
}

function onMediaError(): void {
    const code = audioRef.value?.error?.code
    error.value = code !== undefined
        ? MEDIA_ERROR_MESSAGES[code] ?? 'Unknown media error.'
        : 'Playback error.'
    isPlaying.value = false
    isLoading.value = false
    isBuffering.value = false
    stopTicker()
}

function onWaiting(): void {
    isBuffering.value = true
}

function onCanPlay(): void {
    isBuffering.value = false
}

function onSeek(newTime: number): void {
    if (!audioRef.value) return
    audioRef.value.currentTime = newTime
    currentTime.value = newTime
}

watch(
    () => props.src,
    () => {
        error.value = null
        stop()
        isCached.value = false
        
        // Update the audio source when src prop changes
        if (props.src?.startsWith('http')) {
            checkCacheAndUpdateSource()
            
            // Preload the audio file for offline use
            preloadAudio(props.src)
        } else {
            effectiveSrc.value = props.src
        }
        
        if (audioRef.value) {
            audioRef.value.load()
            duration.value = 0
            bufferedTime.value = 0
        }
    },
    { immediate: true }
)
</script>
