# Audio Player

## Example audio
https://dn721604.ca.archive.org/0/items/samples-and-examples-vol.-1/%231/CD%201/01-Walt%20Barr-Free%20Spirits%20%28F<PERSON>die%20Gibbs%20%26%20Madlib-Crime%20Pays%29.mp3

https://archive.org/details/samples-and-examples-vol.-1/%231/CD+1/01-<PERSON>+Barr-Free+Spirits+(<PERSON>+<PERSON>+%26+Madlib-Crime+Pays).mp3


### 4. Custom Crossfade Duration

Adjust the crossfade length to fine-tune seamless looping.

## Understanding `crossfadeDuration`

The `crossfadeDuration` option controls how long (in seconds) the end of one play-through and the start of the next overlap and fade into each other. By adjusting it you’re trading off between:

- **Very short crossfades (e.g. 0.01–0.03 s):**
  - Pros: Minimal loss of loop length, very tight transition  
  - Cons: May still leave a tiny click if the waveform isn’t phase-matched

- **Medium crossfades (e.g. 0.04–0.08 s):**
  - Pros: Generally smooth on most ambient loops, masks minor waveform mismatches  
  - Cons: You “lose” up to ~80 ms of perceived loop each time—it feels slightly shorter

- **Longer crossfades (e.g. 0.1–0.2 s):**
  - Pros: Very forgiving of any waveform discontinuities  
  - Cons: Can sound like the audio is “breathing” or pulsing because you’re effectively fading out and in over a noticeable fraction of the loop

### How it works under the hood

1. **Load the entire audio buffer** once.  
2. **Schedule two overlapping buffer sources**:  
   - **Source A** starts at _t = 0_ and plays the full buffer.  
   - **Source B** starts at _t = (duration – crossfadeDuration)_.  
3. **Crossfade gain** on A → B over that `crossfadeDuration` window:  
   - A’s gain ramps from 1 → 0  
   - B’s gain ramps from 0 → 1  
4. After B finishes, the logic re-schedules A (and so on) so you get a continuous cycle.

### Choosing your value

- **Very tight loops** (e.g. percussion, glitch sounds):  
  Try **0.01–0.03 s**. Keep it as small as possible to preserve “tightness.”

- **Ambient textures** (rain, wind, drones):  
  Start around **0.04–0.06 s**—this usually hides any jump without noticeably shortening the ambience.

- **Musical loops with strong attacks** (pads, keys, plucks):  
  You might need **0.08–0.12 s** to avoid thumps, but listen for any “washy” feeling.