<template>
    <div class="p-4 bg-gray-100 min-h-[400px] flex flex-col space-y-8">
        <h2 class="text-xl font-bold">Audio Loop Player Demo</h2>
    
        <!-- Basic loop example -->
        <div>
            <h3 class="text-lg font-medium mb-2">Basic Loop (Default Crossfade)</h3>
            <div class="flex items-center gap-2">
                <LoopPlayer 
                    src="/audio/mountain_stream.wav"
                />
                <span class="text-sm text-gray-600">Default crossfade (0.05s)</span>
            </div>
        </div>
    
        <!-- Short crossfade example -->
        <div>
            <h3 class="text-lg font-medium mb-2">Short Crossfade</h3>
            <div class="flex items-center gap-2">
                <LoopPlayer 
                    src="/audio/mountain_stream.wav"
                    :crossfade-duration="0.01"
                />
                <span class="text-sm text-gray-600">Very tight crossfade (0.01s)</span>
            </div>
        </div>
    
        <!-- Long crossfade example -->
        <div>
            <h3 class="text-lg font-medium mb-2">Long Crossfade</h3>
            <div class="flex items-center gap-2">
                <LoopPlayer 
                    src="/audio/mountain_stream.wav"
                    :crossfade-duration="0.2"
                />
                <span class="text-sm text-gray-600">Smooth crossfade (0.2s)</span>
            </div>
        </div>
    
        <!-- Online audio example -->
        <div>
            <h3 class="text-lg font-medium mb-2">Online Audio</h3>
            <div class="flex items-center gap-2">
                <LoopPlayer 
                    src="https://dn721604.ca.archive.org/0/items/samples-and-examples-vol.-1/%231/CD%201/01-Walt%20Barr-Free%20Spirits%20%28Freddie%20Gibbs%20%26%20Madlib-Crime%20Pays%29.mp3"
                    :crossfade-duration="0.05"
                />
                <span class="text-sm text-gray-600">Online audio with standard crossfade</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import LoopPlayer from './LoopPlayer.vue'
</script>