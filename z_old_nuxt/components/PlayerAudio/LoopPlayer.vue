<template>
    <button class="p-2 bg-gray-200 rounded hover:bg-gray-300 focus:outline-none" @click="toggle">
        <Icon :name="isPlaying ? 'lucide-pause' : 'lucide-play'" class="h-5 w-5" />
    </button>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount } from 'vue'

const props = defineProps<{
    src: string
    crossfadeDuration?: number
}>()

const isPlaying = ref(false)
const error = ref<Error | null>(null)

let audioContext: AudioContext | null = null
let buffer: AudioBuffer | null = null
let sources: AudioBufferSourceNode[] = []
let gains: GainNode[] = []
let loopTimer: number | null = null

/** Load and decode the audio buffer from source */
async function loadBuffer(): Promise<void> {
    if (!buffer && audioContext) {
        const res = await fetch(props.src)
        if (!res.ok) throw new Error(`Network error: ${res.status}`)
        const data = await res.arrayBuffer()
        buffer = await audioContext.decodeAudioData(data)
    }
}

/** Schedule a loop with crossfade */
function scheduleLoop(): void {
    if (!audioContext || !buffer) return

    // stop old nodes
    sources.forEach(s => s.stop())
    gains.forEach(g => g.disconnect())
    sources = []
    gains = []

    const ctx = audioContext
    const now = ctx.currentTime
    const dur = buffer.duration
    const cf = props.crossfadeDuration ?? 0.05

    // First buffer
    const s1 = ctx.createBufferSource()
    s1.buffer = buffer
    const g1 = ctx.createGain()
    g1.gain.setValueAtTime(1, now)
    s1.connect(g1).connect(ctx.destination)
    s1.start(now)

    // Second buffer
    const start2 = now + dur - cf
    const s2 = ctx.createBufferSource()
    s2.buffer = buffer
    const g2 = ctx.createGain()
    g2.gain.setValueAtTime(0, now)
    g2.gain.linearRampToValueAtTime(1, start2 + cf)
    s2.connect(g2).connect(ctx.destination)
    s2.start(start2)

    // Crossfade out the first
    g1.gain.setValueAtTime(1, start2)
    g1.gain.linearRampToValueAtTime(0, start2 + cf)

    sources = [s1, s2]
    gains = [g1, g2]

    loopTimer = window.setTimeout(() => {
        if (isPlaying.value) scheduleLoop()
    }, (dur - cf) * 1000)

    isPlaying.value = true
}

/** Force-unlock iOS AudioContext by playing a silent buffer */
function unlockAudioContext(ctx: AudioContext): void {
    const silentBuffer = ctx.createBuffer(1, 1, ctx.sampleRate)
    const source = ctx.createBufferSource()
    source.buffer = silentBuffer
    source.connect(ctx.destination)
    source.start()
}

/** Toggle play / pause */
async function toggle(): Promise<void> {
    try {
        if (!audioContext) {
            audioContext = new AudioContext()
            unlockAudioContext(audioContext) // 🔑 unlock before any await
            await loadBuffer()
            scheduleLoop()
        } else if (!isPlaying.value && audioContext.state === 'suspended') {
            await audioContext.resume()
            scheduleLoop()
        } else if (isPlaying.value && audioContext !== null) {
            audioContext.suspend()
            isPlaying.value = false
            if (loopTimer !== null) clearTimeout(loopTimer)
        } else {
            scheduleLoop()
        }
    } catch (e: unknown) {
        error.value = e instanceof Error ? e : new Error(String(e))
    }
}

onBeforeUnmount(() => {
    sources.forEach(s => s.stop())
    gains.forEach(g => g.disconnect())
    if (loopTimer !== null) clearTimeout(loopTimer)
    audioContext?.close()
})
</script>
