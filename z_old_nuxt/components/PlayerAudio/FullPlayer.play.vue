<template>
    <div class="p-4 bg-gray-100 min-h-[400px] flex flex-col space-y-8">
        <h2 class="text-xl font-bold">Audio Player Demo</h2>
    
        <!-- Online audio example -->
        <div>
            <h3 class="text-lg font-medium mb-2">Online Audio (with caching)</h3>
            <PlayerAudioFullPlayer 
                src="https://dn721604.ca.archive.org/0/items/samples-and-examples-vol.-1/%231/CD%201/01-Walt%20Barr-Free%20Spirits%20%28Freddie%20Gibbs%20%26%20Madlib-Crime%20Pays%29.mp3"
            />
        </div>
    
        <!-- Local audio example -->
        <div>
            <h3 class="text-lg font-medium mb-2">Local Audio File</h3>
            <PlayerAudioFullPlayer 
                src="/audio/mountain_stream.wav"
            />
        </div>
    
        <!-- Controls to test error handling -->
        <!-- <div>
            <h3 class="text-lg font-medium mb-2">Error Handling Test</h3>
            <div class="flex space-x-4 mb-2">
                <button 
                    class="px-3 py-1 bg-red-500 text-white rounded" 
                    @click="testSrc = ''"
                >
                    Empty Source
                </button>
                <button 
                    class="px-3 py-1 bg-orange-500 text-white rounded" 
                    @click="testSrc = '/non-existent-file.mp3'"
                >
                    Missing File
                </button>
                <button 
                    class="px-3 py-1 bg-yellow-500 text-white rounded" 
                    @click="testSrc = 'https://example.com/not-found.mp3'"
                >
                    Invalid URL
                </button>
            </div>
            <PlayerAudioFullPlayer 
                :src="testSrc"
            />
        </div> -->
    </div>
</template>

<script setup lang="ts">
// const testSrc = ref<string>('/non-existent-file.mp3')
</script>