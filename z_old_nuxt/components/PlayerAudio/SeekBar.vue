<template>
    <div class="w-full px-4 py-2">
        <div ref="bar" class="seek-bar relative w-full h-2 bg-gray-300 rounded cursor-pointer" @click="onClick">
            <!-- buffered portion -->
            <div class="absolute top-0 left-0 h-2 bg-gray-400 rounded" :style="{ width: `${bufferedPercent}%` }"/>
            <!-- played portion -->
            <div class="absolute top-0 left-0 h-2 bg-blue-500 rounded" :style="{ width: `${progress}%` }"/>
            <!-- draggable handle -->
            <div
                class="absolute top-1/2 h-4 w-4 bg-white border border-gray-500 rounded-full transform -translate-y-1/2 -translate-x-1/2 cursor-grab"
                :style="{ left: `${progress}%` }" @mousedown.prevent="startDrag"/>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue'

const props = defineProps<{
    currentTime: number
    duration: number
    bufferedPercent: number
}>()
const emit = defineEmits<{
    (e: 'update:currentTime', value: number): void
}>()

const bar = ref<HTMLElement | null>(null)
const isDragging = ref(false)
const progress = ref(0)

// update play progress when audio moves (unless dragging)
watch(
    () => props.currentTime,
    (t) => {
        if (!isDragging.value && props.duration > 0) {
            progress.value = Math.min(100, (t / props.duration) * 100)
        }
    }
)

// calculate time based on click or drag
function calculateNewTime(clientX: number): number {
    if (!bar.value) return 0
    const { left, width } = bar.value.getBoundingClientRect()
    let x = clientX - left
    x = Math.max(0, Math.min(x, width))
    const ratio = x / width
    return ratio * props.duration
}

function onClick(e: MouseEvent): void {
    const newTime = calculateNewTime(e.clientX)
    emit('update:currentTime', newTime)
}

function onMouseMove(e: MouseEvent): void {
    if (!isDragging.value) return
    const newTime = calculateNewTime(e.clientX)
    const clampedTime = Math.min(newTime, props.duration)
    progress.value = (clampedTime / props.duration) * 100
    emit('update:currentTime', clampedTime)
}

function onMouseUp(): void {
    if (isDragging.value) {
        isDragging.value = false
        window.removeEventListener('mousemove', onMouseMove)
        window.removeEventListener('mouseup', onMouseUp)
    }
}

function startDrag(): void {
    isDragging.value = true
    window.addEventListener('mousemove', onMouseMove)
    window.addEventListener('mouseup', onMouseUp)
}

onBeforeUnmount(() => {
    window.removeEventListener('mousemove', onMouseMove)
    window.removeEventListener('mouseup', onMouseUp)
})
</script>
