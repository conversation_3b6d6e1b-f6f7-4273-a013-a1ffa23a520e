<template>
    <div class="p-4 bg-gray-100 min-h-[400px] flex flex-col space-y-8">
        <h2 class="text-xl font-bold">BackBtn Component Demo</h2>
    
        <!-- Demo controls -->
        <div class="bg-white p-4 rounded-lg shadow mb-4">
            <h3 class="text-lg font-medium mb-2">Demo Controls</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Title Text</label>
                    <input 
                        v-model="titleText" 
                        type="text" 
                        placeholder="Enter title text" 
                        class="w-full p-2 border border-gray-300 rounded-md"
                    >
                </div>
                <div class="flex items-end">
                    <button 
                        class="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors" 
                        @click="titleText = ''"
                    >
                        Clear Title
                    </button>
                </div>
            </div>
      
            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p class="text-sm text-blue-800">
                    <strong>Note:</strong> The back functionality depends on browser history. 
                    In this demo environment, clicking the back button will attempt to navigate to the previous page.
                </p>
            </div>
        </div>
    
        <!-- Component examples -->
        <div class="space-y-6">
            <div>
                <h3 class="text-md font-medium mb-2">Example 1: Default (No Title)</h3>
                <div class="border border-gray-300 rounded-md overflow-hidden">
                    <BackBtn />
                </div>
                <p class="text-sm text-gray-600 mt-1">Basic back button without a title.</p>
            </div>
      
            <div>
                <h3 class="text-md font-medium mb-2">Example 2: With Title</h3>
                <div class="border border-gray-300 rounded-md overflow-hidden">
                    <BackBtn :title="titleText || 'Page Title'" />
                </div>
                <p class="text-sm text-gray-600 mt-1">Back button with a centered title.</p>
            </div>
      
            <div>
                <h3 class="text-md font-medium mb-2">Example 3: On Gray Background</h3>
                <div class="bg-gray-100 border border-gray-300 rounded-md overflow-hidden">
                    <BackBtn :title="titleText" />
                </div>
                <p class="text-sm text-gray-600 mt-1">Back button on a gray background.</p>
            </div>
      
            <div>
                <h3 class="text-md font-medium mb-2">Example 4: On Dark Background</h3>
                <div class="bg-gray-800 border border-gray-300 rounded-md overflow-hidden">
                    <BackBtn :title="titleText" />
                </div>
                <p class="text-sm text-gray-600 mt-1">Back button on a dark background.</p>
            </div>
      
            <div>
                <h3 class="text-md font-medium mb-2">Example 5: In Header Context</h3>
                <div class="border border-gray-300 rounded-md overflow-hidden">
                    <div class="bg-white shadow-sm">
                        <BackBtn :title="titleText || 'App Section'" />
                        <div class="p-4">
                            <p class="text-gray-700">Page content would appear here...</p>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-1">Back button in a typical header context.</p>
            </div>
      
            <div>
                <h3 class="text-md font-medium mb-2">Example 6: With Long Title</h3>
                <div class="border border-gray-300 rounded-md overflow-hidden">
                    <BackBtn title="This is a very long title that might need to be truncated on smaller screens" />
                </div>
                <p class="text-sm text-gray-600 mt-1">Back button with a long title to test overflow handling.</p>
            </div>
        </div>
    </div>
</template>

<script setup>
const titleText = ref('Page Title')
</script>
