# NavTabBar Component

A responsive bottom navigation bar component for Nuxt 3 applications with TypeScript and Tailwind CSS support.

## Features
- Fixed bottom positioning
- Active route highlighting
- Material Design Icons integration
- Fully responsive
- TypeScript support

## Prerequisites
- Nuxt 3
- Tailwind CSS
- nuxt-icon package

## Usage

1. Import the component in your layout or page:
```vue
<template>
  <div>
    <!-- Your page content -->
    <NavTabBar />
  </div>
</template>
```

2. The component will automatically handle navigation between:
- Home (`/`)
- Relief (`/relief`)
- More (`/more`)

## Customization

To modify the navigation items, edit the `navigationItems` array in NavTabBar.vue:

```typescript
const navigationItems = [
  {
    path: '/',
    icon: 'mdi:leaf',
    label: 'YBD'
  },
  // Add or modify items here
]
```

## Styling
The component uses Tailwind CSS classes for styling. Modify the classes in the template section to customize the appearance.

