<template>
    <div class="p-4 bg-gray-100 min-h-[400px] flex flex-col space-y-8">
        <h2 class="text-xl font-bold">YBD Actions Component Demo</h2>
    
        <!-- Controls to show/hide the action modal -->
        <div class="flex flex-col space-y-4">
            <button 
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors" 
                @click="showActions = true"
            >
                Show Actions Modal
            </button>
      
            <div class="bg-white p-4 rounded-lg shadow">
                <h3 class="text-lg font-medium mb-2">Demo Controls</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Between Thoughts Display Time</label>
                        <input 
                            v-model="betweenActionsDisplayTime" 
                            type="range" 
                            min="1" 
                            max="10" 
                            step="0.5" 
                            class="w-full"
                        >
                        <span class="text-sm text-gray-600">{{ betweenActionsDisplayTime }}s</span>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Action component (conditionally rendered) -->
        <Action
            v-if="showActions"
            :actions="sampleActions"
            :between-actions-display-time="betweenActionsDisplayTime"
            @close="showActions = false"
        />
    </div>
</template>

<script setup>
import { ref } from 'vue'
import Action from './Action.vue'

// State for controlling the demo
const showActions = ref(false)
const betweenActionsDisplayTime = ref(3.0)

// Sample actions data
const sampleActions = ref([
    {
        id: '1',
        action: 'Take 5 deep breaths, focusing on the sensation of air entering and leaving your body.',
        completed: false,
        favorite: false
    },
    {
        id: '2',
        action: "Write down three things you're grateful for right now.",
        completed: false,
        favorite: false
    },
    {
        id: '3',
        action: 'Go for a 10-minute walk outside, paying attention to the sensations around you.',
        completed: false,
        favorite: true
    },
    {
        id: '4',
        action: 'Drink a glass of water slowly and mindfully.',
        completed: false,
        favorite: false
    },
    {
        id: '5',
        action: 'Stretch your body for 5 minutes, focusing on areas of tension.',
        completed: false,
        favorite: false
    }
])
</script>
