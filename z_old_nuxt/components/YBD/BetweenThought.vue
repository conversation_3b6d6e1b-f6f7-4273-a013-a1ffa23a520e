
<template>
    <div class="fixed inset-0 bg-gray-900 bg-opacity-80 flex items-center justify-center p-4 z-50">
        <div class="bg-gray-800 p-8 rounded-xl shadow-2xl text-center max-w-md w-full flex flex-col items-center">
            <transition name="fade">
                <p v-if="currentThought" class="text-2xl text-gray-100 italic mb-8" :style="{ opacity: opacity }">
                    "{{ currentThought.thought }}"
                </p>

                <p v-else class="text-xl text-gray-300" :style="{ opacity: opacity }">
                    Preparing next thought...
                </p>
            </transition>
            
            <!-- Progress Circle -->
            <div v-if="showCountdown" class="mt-6 relative h-12 w-12">
                <svg class="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                    <!-- Background Circle -->
                    <circle 
                        cx="18" cy="18" r="16" 
                        fill="none" 
                        stroke="#4B5563" 
                        stroke-width="2"
                        opacity="0.3"
                    />
                    <!-- Progress Circle -->
                    <circle 
                        cx="18" cy="18" r="16" 
                        fill="none" 
                        stroke="#3B82F6" 
                        stroke-width="2" 
                        stroke-dasharray="100" 
                        :stroke-dashoffset="progressOffset" 
                        stroke-linecap="round"
                    />
                </svg>
            </div>
        </div>
    </div>
</template>
  
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import type { RandomThought } from '~/types/ybd';
  
interface Props {
    thoughts: RandomThought[];
    displayTime?: number; // in seconds
}
  
const props = withDefaults(defineProps<Props>(), {
    displayTime: 8.0
});

const emit = defineEmits(['done']);
  
const currentThoughtIndex = ref(0);
const opacity = ref(0);
const timeRemaining = ref(0);
const showCountdown = ref(false);
let timer: NodeJS.Timeout | null = null;
let progressInterval: NodeJS.Timeout | null = null;

// Calculate a random thought from the array
const currentThought = computed(() => {
    if (props.thoughts.length > 0) {
        return props.thoughts[currentThoughtIndex.value % props.thoughts.length];
    }
    return null;
});

// Calculate the circle progress offset (100 = full circle, 0 = empty)
const progressOffset = computed((): number => {
    if (!props.displayTime) return 100;
    const percent = timeRemaining.value / props.displayTime;
    return 100 - (percent * 100);
});

// Either remove this unused function or prefix with underscore to indicate it's intentionally unused
function _changeThought(): void {
    if (timer) clearTimeout(timer);
    if (progressInterval) clearInterval(progressInterval);
    
    showCountdown.value = false;
    opacity.value = 0;
    
    // Select a random thought
    if (props.thoughts.length > 0) {
        const randomIndex = Math.floor(Math.random() * props.thoughts.length);
        currentThoughtIndex.value = randomIndex;
    }
    
    // Fade in the new thought
    setTimeout(() => {
        opacity.value = 1;
        
        if (props.displayTime) {
            startTimer(props.displayTime);
        }
    }, 500);
}

function startTimer(duration: number): void {
    timeRemaining.value = duration;
    showCountdown.value = true;
    
    if (progressInterval) clearInterval(progressInterval);
    
    progressInterval = setInterval(() => {
        if (timeRemaining.value > 0) {
            timeRemaining.value -= 0.1;
        } else {
            if (progressInterval) clearInterval(progressInterval);
            showCountdown.value = false;
        }
    }, 100);
    
    timer = setTimeout(() => {
        emit('done');
    }, duration * 1000);
}
  
onMounted(() => {
    if (props.thoughts.length === 0) {
        emit('done');
        return;
    }
    
    // Select initial random thought
    if (props.thoughts.length > 0) {
        const randomIndex = Math.floor(Math.random() * props.thoughts.length);
        currentThoughtIndex.value = randomIndex;
    }
    
    // Animate in
    setTimeout(() => {
        opacity.value = 1;
    }, 100);
    
    // Start the countdown if we have a display time
    if (typeof props.displayTime === 'number' && props.displayTime > 0) {
        startTimer(props.displayTime);
    }
});
  
onUnmounted(() => {
    if (timer) clearTimeout(timer);
    if (progressInterval) clearInterval(progressInterval);
});
</script>

<style scoped>
/* Optional transitions */
.fade-enter-active, .fade-leave-active {
    transition: opacity 1.5s;
}
.fade-enter-from, .fade-leave-to {
    opacity: 0;
}
</style>
