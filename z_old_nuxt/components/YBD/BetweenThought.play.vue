<template>
    <div class="p-4 bg-gray-100 min-h-[400px] flex flex-col space-y-8">
        <h2 class="text-xl font-bold">BetweenThought Component Demo</h2>
    
        <!-- Controls to show/hide the component -->
        <div class="flex flex-col space-y-4">
            <button 
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors" 
                @click="showThought = true"
            >
                Show Between Thought
            </button>
      
            <div class="bg-white p-4 rounded-lg shadow">
                <h3 class="text-lg font-medium mb-2">Demo Controls</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Display Time</label>
                        <input 
                            v-model="displayTime" 
                            type="range" 
                            min="2" 
                            max="15" 
                            step="0.5" 
                            class="w-full"
                        >
                        <span class="text-sm text-gray-600">{{ displayTime }}s</span>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- BetweenThought component (conditionally rendered) -->
        <YBDBetweenThought
            v-if="showThought"
            :thoughts="sampleThoughts"
            :display-time="displayTime"
            @done="handleDone"
        />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import YBDBetweenThought from './BetweenThought.vue'

// State for controlling the demo
const showThought = ref(false)
const displayTime = ref(5.0)

// Sample thoughts for demonstration
const sampleThoughts = ref([
    { id: '1', thought: 'Take a deep breath and center yourself.' },
    { id: '2', thought: 'Notice how your body feels in this moment.' },
    { id: '3', thought: 'Allow your thoughts to come and go without judgment.' },
    { id: '4', thought: 'Remember that you are exactly where you need to be.' },
    { id: '5', thought: 'Each breath is an opportunity to begin again.' }
])

// Handle the done event from the component
function handleDone(): void {
    showThought.value = false
    console.log('BetweenThought completed')
}
</script>