
<template>
    <div class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-2 z-50">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full flex flex-col h-full">
            <!-- Title -->
            <h2 class="text-2xl font-semibold mb-4 text-gray-800">Actions</h2>
            
            <!-- Main content section -->
            <div class="flex-grow flex flex-col justify-center items-center">
                <div v-if="actions && actions.length > 0" class="w-full mb-8">
                    <!-- Current action content -->
                    <p class="text-xl font-light text-center text-gray-700 mb-8">
                        {{ currentAction?.action }}
                    </p>
                    
                    <!-- Action buttons (star and checkbox) -->
                    <div class="flex justify-center space-x-8 mb-8">
                        <button class="focus:outline-none" @click="toggleFavorite">
                            <svg
                                :class="isFavorite ? 'text-yellow-500' : 'text-gray-400'" 
                                class="w-8 h-8 transition-colors duration-200" 
                                xmlns="http://www.w3.org/2000/svg" 
                                viewBox="0 0 24 24" 
                                fill="currentColor">
                                <path d="M9.153 3.24c.433-.863 1.662-.863 2.094 0l1.874 3.764 4.195.608c.956.139 1.337 1.312.645 1.986l-3.035 2.947.717 4.169c.163.95-.833 1.675-1.684 1.226L10 15.874l-3.959 2.066c-.85.449-1.846-.275-1.684-1.226l.717-4.169-3.035-2.947c-.692-.674-.31-1.847.645-1.986l4.195-.608L9.153 3.24z" />
                            </svg>
                        </button>
                        <button class="focus:outline-none" @click="toggleCompleted">
                            <svg
                                :class="isCompleted ? 'text-green-500' : 'text-gray-400'" 
                                class="w-8 h-8 transition-colors duration-200" 
                                xmlns="http://www.w3.org/2000/svg" 
                                viewBox="0 0 24 24" 
                                fill="currentColor">
                                <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm-1.5 9.793l-2.646-2.647a.5.5 0 0 0-.708.708l3 3a.5.5 0 0 0 .708 0l5-5a.5.5 0 0 0-.708-.708L10.5 11.793z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                    
                    <!-- How button -->
                    <div class="flex justify-center">
                        <button 
                            class="px-8 py-3 bg-gray-800 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 focus:outline-none mb-8">
                            How?
                        </button>
                    </div>
                </div>
                <p v-else class="text-gray-600 text-center">No actions available.</p>
            </div>

            <!-- Navigation Controls -->
            <div class="mt-auto">
                <div class="grid grid-cols-2 gap-3 mb-3">
                    <button
                        :disabled="currentActionIndex === 0"
                        :class="[
                            'flex items-center justify-center py-3 rounded-lg transition-colors',
                            currentActionIndex > 0 
                                ? 'bg-blue-500 text-white hover:bg-blue-600' 
                                : 'bg-gray-300 text-gray-500'
                        ]"
                        @click="prevAction"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                        Previous
                    </button>
                    <button
                        :disabled="currentActionIndex >= actions.length - 1 || showingBetweenThought"
                        :class="[
                            'flex items-center justify-center py-3 rounded-lg transition-colors',
                            currentActionIndex < actions.length - 1 && !showingBetweenThought
                                ? 'bg-blue-500 text-white hover:bg-blue-600' 
                                : 'bg-gray-300 text-gray-500'
                        ]"
                        @click="nextAction"
                    >
                        Next
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                </div>
                
                <!-- Exit button -->
                <button
                    class="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
                    @click="$emit('close')"
                >
                    <div class="flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Actions Exit
                    </div>
                </button>
                
                <p class="text-center text-sm text-gray-500 mt-2">
                    {{ currentActionIndex + 1 }} / {{ actions.length }}
                </p>
            </div>
            
            <!-- Sparkling star animation if needed -->
            <div v-if="showingStar" class="absolute inset-0 pointer-events-none flex items-center justify-center">
                <!-- Simple star animation using CSS -->
                <div class="stars-container">
                    <div v-for="i in 8" :key="i" class="star" :style="`--delay: ${i*0.1}s`"/>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Between Thought Modal -->
    <YBDBetweenThought
        v-if="showingBetweenThought"
        :thoughts="betweenThoughts"
        :display-time="betweenActionsDisplayTime"
        @done="handleBetweenThoughtDone"
    />
</template>
  
<script setup lang="ts">
import { ref, computed } from 'vue';
import type { YBDAction, RandomThought } from '~/types/ybd';
  
interface Props {
    actions: YBDAction[];
    betweenActionsDisplayTime?: number;
}
  
const props = withDefaults(defineProps<Props>(), {
    betweenActionsDisplayTime: 3.0
});

const _emit = defineEmits(['close']);
  
// State variables
const currentActionIndex = ref(0);
const favoriteActions = ref<Set<string>>(new Set());
const completedActions = ref<Set<string>>(new Set());
const showingStar = ref(false);
const showingBetweenThought = ref(false);
const betweenThoughts = ref<RandomThought[]>([]);

// Computed properties
const currentAction = computed(() => {
    return props.actions[currentActionIndex.value];
});

const isFavorite = computed(() => {
    return currentAction.value !== undefined && favoriteActions.value.has(currentAction.value.id);
});

const isCompleted = computed(() => {
    return currentAction.value !== undefined && completedActions.value.has(currentAction.value.id);
});

// Methods
function toggleFavorite(): void {
    if (currentAction.value !== undefined) {
        if (favoriteActions.value.has(currentAction.value.id)) {
            favoriteActions.value.delete(currentAction.value.id);
        } else {
            favoriteActions.value.add(currentAction.value.id);
        }
    }
}

function toggleCompleted(): void {
    if (currentAction.value !== undefined) {
        if (completedActions.value.has(currentAction.value.id)) {
            completedActions.value.delete(currentAction.value.id);
            showingStar.value = false;
        } else {
            completedActions.value.add(currentAction.value.id);
            showingStar.value = true;
            
            // Hide star after 1.5 seconds
            setTimeout(() => {
                showingStar.value = false;
            }, 1500);
        }
    }
}

function prevAction(): void {
    if (currentActionIndex.value > 0) {
        currentActionIndex.value--;
    }
}

function nextAction(): void {
    if (currentActionIndex.value < props.actions.length - 1) {
        // Show between thought before going to next action
        showingBetweenThought.value = true;
    }
}

function handleBetweenThoughtDone(): void {
    showingBetweenThought.value = false;
    if (currentActionIndex.value < props.actions.length - 1) {
        currentActionIndex.value++;
    }
}

// Function to load between thoughts
async function loadBetweenThoughts(): Promise<void> {
    try {
        const response = await fetch('/data/ybd_between_actions.json');
        if (!response.ok) throw new Error('Failed to load between thoughts');
        betweenThoughts.value = await response.json();
    } catch (error) {
        console.error('Error loading between thoughts:', error);
        // Fallback data in case the fetch fails
        betweenThoughts.value = [
            { id: '1', thought: 'Taking a deep breath...' },
            { id: '2', thought: 'Finding your center...' },
            { id: '3', thought: 'You\'re doing great...' }
        ];
    }
}

// Initialize
onMounted(() => {
    // Load between thoughts on component mount
    loadBetweenThoughts();
});
</script>

<style scoped>
/* Star animation */
.stars-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.star {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #FFD700;
    border-radius: 50%;
    opacity: 0;
    animation: star-burst 1.5s ease-out forwards;
    animation-delay: var(--delay, 0s);
}

@keyframes star-burst {
    0% {
        transform: translate(0, 0) scale(0);
        opacity: 1;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translate(
            calc(cos(var(--angle, 0deg)) * 80px),
            calc(sin(var(--angle, 90deg)) * 80px)
        ) scale(1);
        opacity: 0;
    }
}

/* Position the stars in a circle */
.star:nth-child(1) { --angle: 0deg; }
.star:nth-child(2) { --angle: 45deg; }
.star:nth-child(3) { --angle: 90deg; }
.star:nth-child(4) { --angle: 135deg; }
.star:nth-child(5) { --angle: 180deg; }
.star:nth-child(6) { --angle: 225deg; }
.star:nth-child(7) { --angle: 270deg; }
.star:nth-child(8) { --angle: 315deg; }
</style>
