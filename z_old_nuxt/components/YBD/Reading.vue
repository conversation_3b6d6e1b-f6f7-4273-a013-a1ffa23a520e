
<template>
    <div class="flex flex-col min-h-screen bg-gray-100 text-gray-800">  
        <div ref="scrollContainer" class="flex-grow overflow-y-auto p-5 pt-0">
            <div ref="scrollToTopAnchor"/>
            <div v-if="currentIdea" class="max-w-2xl mx-auto">
                <!-- Audio Player -->
                <div v-if="audioFile" class="mb-6">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <h3 class="text-sm font-semibold text-gray-600 mb-2">Audio Guide</h3>
                        <audio controls :src="audioFile.remoteUrl" class="w-full">
                            Your browser does not support the audio element.
                        </audio>
                    </div>
                </div>
  
                <!-- Title -->
                <h1 v-if="currentIdea.title" class="text-3xl font-bold text-center mb-4 text-gray-900">
                    {{ currentIdea.title }}
                </h1>
  
                <!-- Quote -->
                <div v-if="currentIdea.quote" class="mb-6 p-4 bg-gray-50 rounded-lg shadow-sm">
                    <p class="text-2xl italic text-center text-gray-700">
                        "{{ currentIdea.quote.quote }}"
                    </p>
                    <p v-if="currentIdea.quote.quote_author" class="text-sm text-right mt-2 text-gray-500">
                        — {{ currentIdea.quote.quote_author }}
                    </p>
                </div>
  
                <!-- Message -->
                <p v-if="currentIdea.message" class="text-lg font-thin leading-relaxed mb-8 text-gray-700 whitespace-pre-line">
                    {{ currentIdea.message }}
                </p>
  
                <!-- Favorite Button -->
                <div class="flex justify-center my-6">
                    <button aria-label="Toggle favorite" @click="toggleFavorite">
                        <svg
                            xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 transition-transform duration-150 ease-in-out"
                            :class="currentIdea.favorite ? 'text-pink-500 fill-current' : 'text-gray-400 hover:text-pink-400'"
                            viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
  
                <!-- See Actions Button -->
                <button
                    class="w-full flex items-center justify-center py-3 px-4 border-2 border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition-colors mb-3"
                    @click="openActions"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                    See Actions
                </button>
  
                <!-- Guidance Text -->
                <p v-if="props.ideas.length > 1 && !canProceedToNext" class="text-sm italic font-bold text-center text-gray-500 mb-6 px-4">
                    Review and take actions before moving on.
                </p>
  
                <!-- Navigation Controls (for multiple ideas) -->
                <div v-if="props.ideas.length > 1" class="mt-6">
                    <div class="grid grid-cols-2 gap-3">
                        <button
                            :disabled="currentIdeaIndex === 0"
                            :class="[
                                'flex items-center justify-center py-3 rounded-lg transition-colors',
                                currentIdeaIndex > 0 
                                    ? 'bg-blue-500 text-white hover:bg-blue-600' 
                                    : 'bg-gray-300 text-gray-500'
                            ]"
                            @click="prevIdea"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                            Previous
                        </button>
                        <button
                            :disabled="currentIdeaIndex >= props.ideas.length - 1 || !canProceedToNext || showingBetweenThought"
                            :class="[
                                'flex items-center justify-center py-3 rounded-lg transition-colors',
                                currentIdeaIndex < props.ideas.length - 1 && !showingBetweenThought && canProceedToNext
                                    ? 'bg-blue-500 text-white hover:bg-blue-600' 
                                    : 'bg-gray-300 text-gray-500'
                            ]"
                            @click="nextIdea"
                        >
                            Next
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>
                    <p class="text-center text-sm text-gray-500 mt-3">
                        {{ currentIdeaIndex + 1 }} / {{ props.ideas.length }}
                    </p>
                </div>
                <div class="h-12"/> <!-- Spacer at the bottom -->
            </div>
            <div v-else class="text-center py-10">
                <p class="text-gray-500">No ideas to display.</p>
            </div>
        </div>
  
        <!-- Modals -->
        <YBDAction
            v-if="showingActions && currentIdea"
            :actions="currentIdea.actionItems || []"
            @close="closeActions"
        />
        <YBDBetweenThought
            v-if="showingBetweenThought"
            :thoughts="props.betweenThoughts"
            :display-time="props.displayTimeBetweenThoughts"
            @done="handleBetweenThoughtDone"
        />
    </div>
</template>
  
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { YBDMessage, RandomThought, AudioFile } from '~/types/ybd';
  
interface Props {
    ideas?: YBDMessage[];
    audioFile?: AudioFile;
    betweenThoughts?: RandomThought[];
    icon?: string; // Kept for consistency, though not used in this template
    requiredActionViewTime?: number; // in seconds
    displayTimeBetweenThoughts?: number; // in seconds
}
  
const props = withDefaults(defineProps<Props>(), {
    requiredActionViewTime: 5,
    displayTimeBetweenThoughts: 8,
    ideas: () => [],
    betweenThoughts: () => [],
    audioFile: undefined,
    icon: undefined,
});
  
const emit = defineEmits(['update:favorite']);
  
const currentIdeaIndex = ref(0);
const showingActions = ref(false);
const showingBetweenThought = ref(false);
const actionViewStartTime = ref<Date | null>(null);
const hasViewedActions = ref(false);
const canProceedToNext = ref(props.ideas.length <= 1); // Allow proceeding if only one item or no items
  
const scrollContainer = ref<HTMLElement | null>(null);
const scrollToTopAnchor = ref<HTMLElement | null>(null);
  
const currentIdea = computed<YBDMessage | undefined>(() => {
    return props.ideas[currentIdeaIndex.value];
});
  
watch(currentIdeaIndex, () => {
    // Reset progression blockers when idea changes, unless there's only one idea
    if (props.ideas.length > 1) {
        hasViewedActions.value = false;
        canProceedToNext.value = false;
    }
    scrollToTop();
});
  
// If ideas array changes (e.g. from parent), reset index if out of bounds and progression
watch(() => props.ideas, (newIdeas) => {
    if (currentIdeaIndex.value >= newIdeas.length) {
        currentIdeaIndex.value = Math.max(0, newIdeas.length - 1);
    }
    canProceedToNext.value = newIdeas.length <= 1;
}, { deep: true });
  
function scrollToTop(): void {
    scrollToTopAnchor.value?.scrollIntoView({ behavior: 'smooth' });
    // Fallback for the main scroll container if anchor is not enough
    // scrollContainer.value?.scrollTo({ top: 0, behavior: 'smooth' });
}
  
function toggleFavorite(): void {
    if (currentIdea.value) {
        emit('update:favorite', { id: currentIdea.value.id, favorite: !currentIdea.value.favorite });
    }
}
  
function openActions(): void {
    showingActions.value = true;
    actionViewStartTime.value = new Date();
    hasViewedActions.value = false;
}
  
function closeActions(): void {
    showingActions.value = false;
    if (actionViewStartTime.value) {
        const timeSpent = (new Date().getTime() - actionViewStartTime.value.getTime()) / 1000;
        if (timeSpent >= props.requiredActionViewTime) {
            canProceedToNext.value = true;
        }
        actionViewStartTime.value = null;
    }
}
  
function prevIdea(): void {
    if (currentIdeaIndex.value > 0) {
        currentIdeaIndex.value--;
    }
}
  
function nextIdea(): void {
    if (currentIdeaIndex.value < props.ideas.length - 1 && canProceedToNext.value && !showingBetweenThought.value) {
        showingBetweenThought.value = true;
        // Actual idea change will happen in handleBetweenThoughtDone
    }
}
  
function handleBetweenThoughtDone(): void {
    showingBetweenThought.value = false;
    if (currentIdeaIndex.value < props.ideas.length - 1) {
        currentIdeaIndex.value++;
    }
}
</script>
