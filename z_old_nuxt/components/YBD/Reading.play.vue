<template>
    <div class="p-4 bg-gray-100 min-h-[400px] flex flex-col space-y-8">
        <h2 class="text-xl font-bold">Reading Component Demo</h2>
    
        <!-- Controls for the demo -->
        <div class="bg-white p-4 rounded-lg shadow mb-4">
            <h3 class="text-lg font-medium mb-2">Demo Controls</h3>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Required Action View Time</label>
                    <input 
                        v-model="requiredActionViewTime" 
                        type="range" 
                        min="1" 
                        max="10" 
                        step="1" 
                        class="w-full"
                    >
                    <span class="text-sm text-gray-600">{{ requiredActionViewTime }}s</span>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Between Thoughts Display Time</label>
                    <input 
                        v-model="displayTimeBetweenThoughts" 
                        type="range" 
                        min="2" 
                        max="15" 
                        step="0.5" 
                        class="w-full"
                    >
                    <span class="text-sm text-gray-600">{{ displayTimeBetweenThoughts }}s</span>
                </div>
            </div>
        </div>
    
        <!-- Reading component -->
        <YBDReading
            :ideas="sampleIdeas"
            :audio-file="audioFile"
            :between-thoughts="betweenThoughts"
            :required-action-view-time="requiredActionViewTime"
            :display-time-between-thoughts="displayTimeBetweenThoughts"
            @update:favorite="handleFavoriteUpdate"
        />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import YBDReading from './Reading.vue'

// Demo configuration
const requiredActionViewTime = ref(5)
const displayTimeBetweenThoughts = ref(5)

// Sample audio file
const audioFile = ref({
    id: 'sample-audio',
    remoteUrl: 'https://ailsawefdhyfbpvbepui.supabase.co/storage/v1/object/sign/temp.audio.dev/audiobooks/test.mp3?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJ0ZW1wLmF1ZGlvLmRldi9hdWRpb2Jvb2tzL3Rlc3QubXAzIiwiaWF0IjoxNzQxODE0MjU2LCJleHAiOjE3NDE4MTc4NTZ9.7x6klDpL66J3PttAuLhYj6Fov3pEB0800hbaawpVv1c'
})

// Sample between thoughts
const betweenThoughts = ref([
    { id: '1', thought: 'Take a deep breath and center yourself.' },
    { id: '2', thought: 'Notice how your body feels in this moment.' },
    { id: '3', thought: 'Allow your thoughts to come and go without judgment.' },
    { id: '4', thought: 'Remember that you are exactly where you need to be.' },
    { id: '5', thought: 'Each breath is an opportunity to begin again.' }
])

// Sample ideas with action items
const sampleIdeas = ref([
    {
        id: '1',
        title: 'Finding Inner Peace',
        quote: {
            quote: "Peace comes from within. Do not seek it without.",
            quote_author: 'Buddha'
        },
        message: "When we feel overwhelmed, our first instinct is often to look outside ourselves for solutions. But true peace begins with accepting our present moment experience.\n\nTake time today to turn inward and notice what you're feeling without judgment.",
        favorite: false,
        actionItems: [
            {
                id: 'a1',
                action: 'Take 5 deep breaths, focusing on the sensation of air entering and leaving your body.',
                completed: false,
                favorite: false
            },
            {
                id: 'a2',
                action: "Write down three things you're grateful for right now.",
                completed: false,
                favorite: false
            }
        ]
    },
    {
        id: '2',
        title: 'Embracing Change',
        quote: {
            quote: 'The only constant in life is change.',
            quote_author: 'Heraclitus'
        },
        message: "Change can be uncomfortable, but it's also the doorway to growth and new possibilities. When we resist change, we create suffering for ourselves.\n\nToday, practice noticing your resistance to change and gently letting it go.",
        favorite: false,
        actionItems: [
            {
                id: 'a3',
                action: "Identify one change you've been resisting and write down three potential positive outcomes from this change.",
                completed: false,
                favorite: false
            },
            {
                id: 'a4',
                action: 'Practice the phrase "I am open to change" as a gentle mantra throughout your day.',
                completed: false,
                favorite: false
            }
        ]
    }
])

// Handle favorite updates
function handleFavoriteUpdate({ id, favorite }): void {
    const idea = sampleIdeas.value.find(item => item.id === id)
    if (idea) {
        idea.favorite = favorite
        console.log(`Updated favorite status for ${id} to ${favorite}`)
    }
}
</script>
