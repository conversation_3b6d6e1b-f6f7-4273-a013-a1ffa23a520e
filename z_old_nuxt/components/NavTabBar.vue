<template>
    <nav class="h-[60px] bg-white border-t border-gray-200 flex justify-around items-center">
        <NuxtLink
            v-for="(item, index) in navigationItems" 
            :key="index"
            :to="item.path"
            class="flex flex-col items-center flex-1 py-2 text-gray-500 hover:text-yellow-400"
            :class="{ 'text-yellow-400': $route.path === item.path }"
        >
            <Icon 
                :name="item.icon" 
                class="text-2xl mb-1"
            />
            <span class="text-xs">{{ item.label }}</span>
        </NuxtLink>
    </nav>
</template>
  
<script setup lang="ts">
interface NavigationItem {
    path: string
    icon: string
    label: string
}
  
const navigationItems: NavigationItem[] = [
    {
        path: '/',
        icon: 'mdi:leaf',
        label: 'YBD'
    },
    {
        path: '/relief',
        icon: 'mdi:alert-circle-outline',
        label: 'Relief'
    },
    {
        path: '/more',
        icon: 'mdi:cog-outline',
        label: 'More'
    }
]
</script>