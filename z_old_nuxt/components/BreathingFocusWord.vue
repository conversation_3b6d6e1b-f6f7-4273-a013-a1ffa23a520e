<template>
    <p
        class="text-4xl font-medium text-black text-center break-words"
        :style="wordStyle"
    >
        {{ focusWords[currentWordIndex] }}
    </p>
</template>
  
<script setup lang="ts">
import { ref, computed, watch, toRefs, nextTick } from 'vue'
  
interface Props {
    focusWords: string[]
    cycleTime: number
    inhaleDuration: number
    breathingCycleDuration: number
    elapsed: number
}
  
const props = defineProps<Props>()
const { focusWords, cycleTime, inhaleDuration, breathingCycleDuration, elapsed } = toRefs(props)
  
// State
const currentWordIndex   = ref(0)
const wordOpacity        = ref(0)
const transitionDuration = ref('0s')
  
// seconds before end-of-cycle to start fading out
const fadeOutOffset = 8
  
watch(
    () => cycleTime.value,
    async (t) => {
        const total     = breathingCycleDuration.value
        const fadeStart = total - fadeOutOffset
        const cycles    = Math.floor(elapsed.value / total)
  
        // advance word on each inhale
        currentWordIndex.value = cycles % focusWords.value.length
  
        if (t < inhaleDuration.value) {
            // 1️⃣ fade-in over the full inhaleDuration
            transitionDuration.value = `${inhaleDuration.value}s`
            await nextTick()
            wordOpacity.value        = 1
  
        } else if (t < fadeStart) {
            // 2️⃣ hold fully visible (no transition)
            transitionDuration.value = '0s'
            await nextTick()
            wordOpacity.value        = 1
  
        } else {
            // 3️⃣ fade-out over fadeOutOffset seconds
            transitionDuration.value = `${fadeOutOffset}s`
            await nextTick()
            wordOpacity.value        = 0
        }
    },
    { immediate: true }
)
  
const wordStyle = computed(() => ({
    opacity:    wordOpacity.value,
    transition: `opacity ${transitionDuration.value} ease-in-out`
}))
</script>
  