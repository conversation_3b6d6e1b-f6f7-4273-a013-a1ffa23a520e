<template>
    <div class="w-full h-[60px] bg-white flex items-center">
        <div class="w-1/3 flex justify-start pl-4">
            <button class="text-blue-600 hover:underline flex items-center gap-1" @click="$router.back()">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>
        </div>

        <div v-if="title" class="w-1/3 text-center font-medium text-gray-800">
            {{ title }}
        </div>
        <div v-else class="w-1/3"/>

        <div class="w-1/3"/>
    </div>
</template>

<script setup lang="ts">
defineProps({
    title: {
        type: String,
        default: ''
    }
})
</script>
  
