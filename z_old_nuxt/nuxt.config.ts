import tailwindcss from "@tailwindcss/vite";

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    compatibilityDate: '2025-04-04',
    devtools: { enabled: true },
    pages: true,
    modules: [
        '@nuxt/eslint',
        '@nuxt/fonts',
        '@nuxt/icon',
        '@nuxt/image',
        '@nuxt/scripts',
        '@nuxt/test-utils',
        '@pinia/nuxt',
        '@vite-pwa/nuxt',
    ],
    pwa: {
        registerType: 'prompt', // Show "Install PWA" 'prompt' or 'autoUpdate'
        manifest: {
            name: 'Your Best Days',
            short_name: 'YourBestDays',
            description: 'Personal development app for mental wellness and personal growth.',
            theme_color: '#ffffff',
            background_color: '#ffffff',
            display: 'standalone',
            start_url: '/',
            // icons: [
            //     {
            //         src: '/pwa/icon-192x192.png',
            //         sizes: '192x192',
            //         type: 'image/png',
            //     },
            //     {
            //         src: '/pwa/icon-512x512.png',
            //         sizes: '512x512',
            //         type: 'image/png',
            //     }
            // ]
        },
        workbox: {
            // Optional: cache strategies or offline support
        },
        client: {
            installPrompt: true, // Enable install prompt handling
        },
    },
    css: ['~/assets/css/main.css'],
    vite: {
        plugins: [
            tailwindcss(),
        ],
    },
    nitro: {
        routeRules: {
            // Exclude dev-playground from production builds
            ...(process.env.NODE_ENV === 'production'
                ? {
                    '/dev-playground': {
                        ssr: false,
                        prerender: false,
                        static: false,
                    },
                }
                : {}),
        }
    }
})