<template>
    <div
        v-if="!isPlaygroundPage"
        class="hidden sm:flex items-center justify-center h-screen px-4 text-center"
    >
        This app is designed for mobile only. Please visit from your phone.
    </div>
  
    <div :class="{ 'sm:hidden': !isPlaygroundPage }">
        <NuxtLayout>
            <NuxtPage />
        </NuxtLayout>
    </div>
</template>

<script setup lang="ts">
useHead({
    link: [
        {
            rel: 'manifest',
            href: '/manifest.webmanifest'
        }
    ]
})

const route = useRoute();

const isPlaygroundPage = computed(() => route.path === '/dev-playground');
</script>
