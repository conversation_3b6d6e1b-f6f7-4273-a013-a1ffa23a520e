{"name": "ybd-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --fix --ext .js,.ts,.vue"}, "dependencies": {"@nuxt/eslint": "^1.3.0", "@nuxt/fonts": "^0.11.2", "@nuxt/icon": "^1.12.0", "@nuxt/image": "^1.10.0", "@nuxt/scripts": "^0.11.6", "@nuxt/test-utils": "^3.17.2", "@pinia/nuxt": "^0.11.1", "@tailwindcss/vite": "^4.1.4", "@unhead/vue": "^2.0.8", "@vite-pwa/nuxt": "^1.0.0", "eslint": "^9.25.1", "nuxt": "^3.16.2", "pinia": "^3.0.3", "tailwindcss": "^4.1.4", "vue": "^3.5.13", "vue-router": "^4.5.0"}}