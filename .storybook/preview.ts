import type { Preview } from '@storybook/sveltekit'
import '../src/app.css'
import { withTheme, withViewport } from './decorators'

const preview: Preview = {
    parameters: {
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/i,
            },
        },
        backgrounds: {
            default: 'light',
            values: [
                {
                    name: 'light',
                    value: '#ffffff',
                },
                {
                    name: 'dark',
                    value: '#0f172a',
                },
            ],
        },
        viewport: {
            viewports: {
                desktop: {
                    name: 'Desktop',
                    styles: {
                        width: '1200px',
                        height: '800px',
                    },
                },
                mobile: {
                    name: 'Mobile',
                    styles: {
                        width: '375px',
                        height: '667px',
                    },
                },
            },
            defaultViewport: 'desktop',
        },
    },
    globalTypes: {
        theme: {
            description: 'Global theme for components',
            defaultValue: 'blue',
            toolbar: {
                title: 'Theme',
                icon: 'paintbrush',
                items: [
                    { value: 'blue', title: 'Blue' },
                    { value: 'hunter-green', title: '<PERSON> Green' },
                    { value: 'forest-green', title: 'Forest Green' },
                    { value: 'red', title: 'Red' },
                ],
                dynamicTitle: true,
            },
        },
        darkMode: {
            description: 'Toggle dark mode',
            defaultValue: 'light',
            toolbar: {
                title: 'Dark Mode',
                icon: 'moon',
                items: [
                    { value: 'light', title: 'Light' },
                    { value: 'dark', title: 'Dark' },
                ],
                dynamicTitle: true,
            },
        },
        viewport: {
            description: 'Switch between desktop and mobile views',
            defaultValue: 'desktop',
            toolbar: {
                title: 'Viewport',
                icon: 'mobile',
                items: [
                    { value: 'desktop', title: 'Desktop' },
                    { value: 'mobile', title: 'Mobile' },
                ],
                dynamicTitle: true,
            },
        },
    },
    decorators: [withTheme, withViewport],
};

export default preview;