import type { Decorator } from '@storybook/svelte';

export const withTheme: Decorator = (story, context) => {
    const theme = context.globals.theme || 'blue';
    const darkMode = context.globals.darkMode || 'light';
  
    // Apply theme and dark mode to the document
    if (typeof document !== 'undefined') {
        document.documentElement.setAttribute('data-theme', theme);
        if (darkMode === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }
  
    return story();
};

export const withViewport: Decorator = (story, context) => {
    const viewport = context.globals.viewport || 'desktop';
    
    // Update the viewport parameter based on the global selection
    if (context.parameters.viewport) {
        context.parameters.viewport.defaultViewport = viewport;
    }
    
    return story();
};