<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Setup Test</title>
</head>
<body>
    <h1>Profile Setup Test</h1>
    <button onclick="setupProfile()">Setup Test Profile</button>
    <button onclick="clearProfile()">Clear Profile</button>
    <button onclick="checkProfile()">Check Profile</button>
    <pre id="output"></pre>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.textContent += message + '\n';
            console.log(message);
        }

        function setupProfile() {
            const output = document.getElementById('output');
            output.textContent = '';
            
            const testProfile = {
                personal: { 
                    preferredName: 'Test User', 
                    ageRange: '25-34', 
                    genderIdentity: 'prefer-not-to-say' 
                },
                preferences: { 
                    theme: { name: 'blue', colors: {} } 
                },
                experiences: [
                    { name: 'meditation', summary: 'I practice meditation for clarity' },
                    { name: 'exercise', summary: 'Regular exercise helps me stay focused' }
                ],
                obstacles: [
                    { name: 'anxiety', summary: 'I struggle with anxiety sometimes' },
                    { name: 'stress', summary: 'Work stress affects my wellbeing' }
                ],
                goals: [],
                schedules: [
                    {
                        id: 1,
                        name: 'Friday Schedule',
                        selectedDays: ['Fri'],
                        timeRanges: [
                            {
                                id: 1,
                                startHour: 9,
                                endHour: 10,
                                maxPerHour: 2
                            }
                        ]
                    }
                ],
                wizardCompleted: true
            };

            log('Setting up test profile with proper schedule format...');
            localStorage.setItem('profile', JSON.stringify(testProfile));
            log('Profile saved to localStorage');
            
            // Verify it was saved
            const saved = localStorage.getItem('profile');
            if (saved) {
                const parsed = JSON.parse(saved);
                log('Verified saved profile schedules: ' + JSON.stringify(parsed.schedules, null, 2));
                log('Wizard completed: ' + parsed.wizardCompleted);
            }
        }

        function clearProfile() {
            const output = document.getElementById('output');
            output.textContent = '';
            
            localStorage.removeItem('profile');
            log('Profile cleared from localStorage');
        }

        function checkProfile() {
            const output = document.getElementById('output');
            output.textContent = '';
            
            const saved = localStorage.getItem('profile');
            if (saved) {
                const parsed = JSON.parse(saved);
                log('Current profile data:');
                log(JSON.stringify(parsed, null, 2));
            } else {
                log('No profile data found in localStorage');
            }
        }
    </script>
</body>
</html>