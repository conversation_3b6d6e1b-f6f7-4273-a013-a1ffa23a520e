{"name": "yourbestdays-svelte", "private": true, "version": "0.4.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "build-cap": "vite build && npx cap sync", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "eslint . --fix", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test:e2e": "playwright test"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@chromatic-com/storybook": "^4.0.1", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.2", "@playwright/test": "^1.49.1", "@storybook/addon-a11y": "^9.0.15", "@storybook/addon-docs": "^9.0.15", "@storybook/addon-svelte-csf": "^5.0.5", "@storybook/addon-vitest": "^9.0.15", "@storybook/sveltekit": "^9.0.15", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/crypto-js": "^4.2.2", "@types/node": "^22", "@vitest/browser": "^3.2.3", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-plugin-storybook": "^9.0.15", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "playwright": "^1.53.0", "storybook": "^9.0.15", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.4", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "vite-plugin-devtools-json": "^0.2.0", "vitest": "^3.2.3", "vitest-browser-svelte": "^0.1.0"}, "dependencies": {"@capacitor/android": "^7.4.0", "@capacitor/app": "^7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/cli": "^7.4.0", "@capacitor/core": "^7.4.0", "@capacitor/ios": "^7.4.0", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@fontsource-variable/nunito-sans": "^5.2.6", "@fontsource-variable/playfair-display": "^5.2.6", "@stripe/stripe-js": "^7.4.0", "@supabase/supabase-js": "^2.50.3", "crypto-js": "^4.2.0", "lucide-svelte": "^0.408.0", "stripe": "^18.3.0"}}