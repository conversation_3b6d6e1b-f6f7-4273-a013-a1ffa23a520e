// e2e/theme.test.ts
import { test, expect } from '@playwright/test';

test.describe('Theme consistency during navigation', () => {
    test('theme should not change when navigating to settings', async ({ page }) => {
    // 1. Load page
        await page.goto('/'); // No need for waitUntil: 'domcontentloaded' here, as we'll wait for the attribute

        // Wait for the data-theme attribute to be present on the documentElement
        await page.waitForFunction(() => document.documentElement.hasAttribute('data-theme'));

        // Get initial theme
        const initialTheme = await page.evaluate(() => document.documentElement.getAttribute('data-theme'));
        expect(initialTheme).not.toBeNull(); // This expectation should now always pass
        console.log(`Initial theme: ${initialTheme}`);

        // 2. Click "More" in bottom tab navigation
        await page.getByText('More').click();
        await page.waitForURL('/more'); // Wait for navigation to complete

        // 3. Click "Settings"
        await page.getByText('Settings').click();
        await page.waitForURL('/settings'); // Wait for navigation to complete

        // Wait 1 second after navigation (optional, but good for visual confirmation)
        await page.waitForTimeout(1000);

        // Verify theme remains the same
        const finalTheme = await page.evaluate(() => document.documentElement.getAttribute('data-theme'));
        console.log(`Final theme: ${finalTheme}`);
        expect(finalTheme).toBe(initialTheme);
    });
});
