// playwright.config.ts
import { defineConfig } from '@playwright/test';

export default defineConfig({
    // 1) point to your test files
    testDir: 'e2e',

    // 2) global timeouts & retries
    timeout: 60_000,
    retries: process.env.CI ? 2 : 0,

    // 3) defaults for every test
    use: {
        baseURL: 'http://localhost:5173',
        headless: false,
        launchOptions: { slowMo: 100 },

        // record video on failure & trace on first retry
        video: 'retain-on-failure',
        trace: 'on-first-retry',
    },

    // 4) start your SvelteKit dev server before tests
    webServer: {
        command: 'npm run dev',
        port: 5173,
        reuseExistingServer: !process.env.CI,
        timeout: 120_000,
    },

    // 5) only one project: Chromium at a mobile/iPhone viewport
    projects: [
        {
            name: 'chromium-mobile',
            use: {
                browserName: 'chromium',            // force Chromium
                viewport: { width: 390, height: 844 }, // iPhone 12 CSS size
                isMobile: true,                     // apply mobile media-queries
                hasTouch: true,                     // enable touch events
            },
        },
    ],
});
