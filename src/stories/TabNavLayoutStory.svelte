<script lang="ts">
    import NavTabBar from '$lib/components/NavTabBar.svelte';
</script>

<div class="flex flex-col h-screen">
    <!-- Scrollable page content -->
    <div class="flex-1 overflow-y-auto bg-background">
        <div class="p-4">
            <h1 class="text-2xl font-bold mb-4">Tab Navigation Layout</h1>
            <p class="text-gray-600 mb-4">This shows how the tab navigation layout works</p>
            
            <!-- Sample content to show the layout -->
            <div class="space-y-4">
                <div class="bg-white p-4 rounded-lg shadow">
                    <h2 class="text-lg font-semibold mb-2">Sample Card 1</h2>
                    <p>This demonstrates how content appears in the tab navigation layout.</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow">
                    <h2 class="text-lg font-semibold mb-2">Sample Card 2</h2>
                    <p>The content area is scrollable while the tab bar stays fixed at the bottom.</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow">
                    <h2 class="text-lg font-semibold mb-2">Sample Card 3</h2>
                    <p>This layout is used for the main app pages: Dashboard, Relief, and More.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Sticky bottom tab navigation -->
    <div class="sticky bottom-0 z-10">
        <NavTabBar />
    </div>
</div>