<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import BtnMain from '../lib/components/Form/BtnMain.svelte';

    const { Story } = defineMeta({
        title: 'Components/Form/BtnMain',
        component: BtnMain,
        tags: ['autodocs'],
        argTypes: {
            text: {
                control: 'text',
                description: 'Button text to display',
            },
            to: {
                control: 'text',
                description: 'URL or path to navigate to',
            },
        },
        args: {
            text: 'Main Button',
            to: '/example',
        }
    });
</script>

<!-- Basic button -->
<Story name="Basic Main Button" args={{
    text: 'Get Started',
    to: '/getting-started'
}}>
    <BtnMain text="Get Started" to="/getting-started">
        <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
        </svg>
    </BtnMain>
</Story>

<!-- Different icons -->
<Story name="With Home Icon" args={{
    text: 'Go Home',
    to: '/'
}}>
    <BtnMain text="Go Home" to="/">
        <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
    </BtnMain>
</Story>

<Story name="With Settings Icon" args={{
    text: 'Settings',
    to: '/settings'
}}>
    <BtnMain text="Settings" to="/settings">
        <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
    </BtnMain>
</Story>

<Story name="With Profile Icon" args={{
    text: 'My Profile',
    to: '/profile'
}}>
    <BtnMain text="My Profile" to="/profile">
        <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
    </BtnMain>
</Story>

<Story name="With Heart Icon" args={{
    text: 'Favorites',
    to: '/favorites'
}}>
    <BtnMain text="Favorites" to="/favorites">
        <svg slot="icon" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"></path>
        </svg>
    </BtnMain>
</Story>

<!-- Different text lengths -->
<Story name="Short Text" args={{
    text: 'Go',
    to: '/go'
}}>
    <BtnMain text="Go" to="/go">
        <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
        </svg>
    </BtnMain>
</Story>

<Story name="Long Text" args={{
    text: 'Start Your Mindfulness Journey Today',
    to: '/mindfulness-journey'
}}>
    <BtnMain text="Start Your Mindfulness Journey Today" to="/mindfulness-journey">
        <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
    </BtnMain>
</Story>

<!-- Without icon -->
<Story name="No Icon" args={{
    text: 'Simple Button',
    to: '/simple'
}}>
    <BtnMain text="Simple Button" to="/simple" />
</Story>

<!-- Multiple buttons layout -->
<Story name="Button Grid">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
        <BtnMain text="Meditation" to="/meditation">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
        </BtnMain>
    
        <BtnMain text="Breathing" to="/breathing">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
        </BtnMain>
    
        <BtnMain text="Journal" to="/journal">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
        </BtnMain>
    
        <BtnMain text="Progress" to="/progress">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
        </BtnMain>
    </div>
</Story>

<!-- Vertical list layout -->
<Story name="Navigation List">
    <div class="space-y-3 max-w-md mx-auto p-4">
        <h3 class="text-lg font-semibold mb-4">Main Navigation</h3>
    
        <BtnMain text="Your Best Days" to="/ybd">
            <svg slot="icon" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17,8C8,10 5.9,16.17 3.82,21.34L5.71,22L6.66,19.7C7.14,19.87 7.64,20 8,20C19,20 22,3 22,3C21,5 14,5.25 9,6.25C4,7.25 2,11.5 2,13.5C2,15.5 3.75,17.25 3.75,17.25C7,8 17,8 17,8Z"/>
            </svg>
        </BtnMain>
    
        <BtnMain text="Relief Center" to="/relief">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </BtnMain>
    
        <BtnMain text="Settings & More" to="/more">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
        </BtnMain>
    </div>
</Story>

<!-- Different link types -->
<Story name="External Link" args={{
    text: 'Visit Website',
    to: 'https://example.com'
}}>
    <BtnMain text="Visit Website" to="https://example.com">
        <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
        </svg>
    </BtnMain>
</Story>

<Story name="Email Link" args={{
    text: 'Contact Support',
    to: 'mailto:<EMAIL>'
}}>
    <BtnMain text="Contact Support" to="mailto:<EMAIL>">
        <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
    </BtnMain>
</Story>

<!-- Mobile layout -->
<Story name="Mobile Layout">
    <div class="max-w-sm mx-auto space-y-3 p-4">
        <h3 class="text-center font-semibold mb-4">Mobile Navigation</h3>
    
        <BtnMain text="Start Session" to="/session">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"></path>
            </svg>
        </BtnMain>
    
        <BtnMain text="My Journey" to="/journey">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
        </BtnMain>
    
        <BtnMain text="Quick Relief" to="/quick-relief">
            <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
        </BtnMain>
    </div>
</Story>

<!-- Feature demonstration -->
<Story name="Feature Showcase">
    <div class="p-4 space-y-6">
        <h3 class="text-lg font-semibold">BtnMain Component Features</h3>
    
        <div class="space-y-2 text-sm text-muted-foreground">
            <p>• <strong>Icon slot:</strong> Flexible icon placement with consistent sizing</p>
            <p>• <strong>Navigation:</strong> Uses anchor tags for proper link behavior</p>
            <p>• <strong>Hover effects:</strong> Smooth color transitions on interaction</p>
            <p>• <strong>Responsive design:</strong> Adapts to different screen sizes</p>
            <p>• <strong>Accessibility:</strong> Semantic HTML with proper link structure</p>
            <p>• <strong>Theme integration:</strong> Uses design system colors and spacing</p>
        </div>
    
        <div class="space-y-3">
            <h4 class="font-medium">Try these examples:</h4>
      
            <BtnMain text="With Icon" to="/with-icon">
                <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                </svg>
            </BtnMain>
      
            <BtnMain text="Without Icon" to="/without-icon" />
      
            <BtnMain text="Hover to see transition effect" to="/hover-demo">
                <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
            </BtnMain>
        </div>
    </div>
</Story>

<!-- Real-world usage -->
<Story name="Dashboard Actions">
    <div class="max-w-2xl mx-auto p-6">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold mb-2">Welcome to Your Best Days</h2>
            <p class="text-muted-foreground">Choose an activity to begin your mindfulness journey</p>
        </div>
    
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <BtnMain text="Daily Meditation" to="/meditation">
                <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
            </BtnMain>
      
            <BtnMain text="Breathing Exercises" to="/breathing">
                <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
            </BtnMain>
      
            <BtnMain text="Mindful Reading" to="/reading">
                <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
            </BtnMain>
      
            <BtnMain text="Progress Tracking" to="/progress">
                <svg slot="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </BtnMain>
        </div>
    </div>
</Story>