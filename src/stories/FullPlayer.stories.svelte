<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import FullPlayer from '../lib/components/PlayerAudio/FullPlayer.svelte';

    const { Story } = defineMeta({
        title: 'Components/PlayerAudio/FullPlayer',
        component: FullPlayer,
        tags: ['autodocs'],
        argTypes: {
            src: {
                control: 'text',
                description: 'Audio source URL or file path',
            },
        },
        args: {
            src: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
        }
    });
</script>

<!-- Basic audio player -->
<Story name="Basic Audio Player" args={{
    src: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Basic Audio Player</h3>
        <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
    </div>
</Story>

<!-- Different audio sources -->
<Story name="Nature Sounds" args={{
    src: 'https://www.soundjay.com/nature/sounds/rain-01.wav'
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Nature Sounds - Rain</h3>
        <FullPlayer src="https://www.soundjay.com/nature/sounds/rain-01.wav" />
    </div>
</Story>

<Story name="Meditation Bell" args={{
    src: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Meditation Bell</h3>
        <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
    </div>
</Story>

<!-- Local file example -->
<Story name="Local Audio File" args={{
    src: '/audio/mountain_stream.wav'
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Local Audio File</h3>
        <p class="text-sm text-muted-foreground mb-4">
            This example uses a local audio file from the public directory.
        </p>
        <FullPlayer src="/audio/mountain_stream.wav" />
    </div>
</Story>

<!-- Multiple players -->
<Story name="Multiple Players">
    <div class="p-4 space-y-6">
        <h3 class="text-lg font-semibold">Multiple Audio Players</h3>
        <p class="text-sm text-muted-foreground">
            Only one player can play at a time. Starting one will pause the others.
        </p>
    
        <div class="space-y-4">
            <div>
                <h4 class="font-medium mb-2">Rain Sounds</h4>
                <FullPlayer src="https://www.soundjay.com/nature/sounds/rain-01.wav" />
            </div>
      
            <div>
                <h4 class="font-medium mb-2">Ocean Waves</h4>
                <FullPlayer src="https://www.soundjay.com/nature/sounds/ocean-wave-1.wav" />
            </div>
      
            <div>
                <h4 class="font-medium mb-2">Forest Ambience</h4>
                <FullPlayer src="https://www.soundjay.com/nature/sounds/forest-1.wav" />
            </div>
        </div>
    </div>
</Story>

<!-- Error handling examples -->
<Story name="Invalid URL" args={{
    src: 'https://invalid-url-that-does-not-exist.com/audio.mp3'
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Error Handling - Invalid URL</h3>
        <p class="text-sm text-muted-foreground mb-4">
            This demonstrates how the player handles invalid URLs.
        </p>
        <FullPlayer src="https://invalid-url-that-does-not-exist.com/audio.mp3" />
    </div>
</Story>

<Story name="Not Found Error" args={{
    src: 'https://httpstat.us/404'
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Error Handling - 404 Not Found</h3>
        <p class="text-sm text-muted-foreground mb-4">
            This demonstrates how the player handles 404 errors.
        </p>
        <FullPlayer src="https://httpstat.us/404" />
    </div>
</Story>

<!-- Different layouts -->
<Story name="In Card Layout">
    <div class="max-w-md mx-auto">
        <div class="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
            <div class="p-4 border-b border-border">
                <h3 class="font-semibold text-foreground">Relaxation Audio</h3>
                <p class="text-sm text-muted-foreground">Peaceful sounds for meditation</p>
            </div>
            <div class="p-4">
                <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
            </div>
        </div>
    </div>
</Story>

<Story name="In Sidebar">
    <div class="flex h-96">
        <div class="w-64 bg-background border-r border-border p-4">
            <h3 class="font-semibold mb-4">Audio Controls</h3>
            <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
        </div>
        <div class="flex-1 p-4 bg-muted/20">
            <h3 class="font-semibold mb-2">Main Content</h3>
            <p class="text-muted-foreground">
                The audio player is positioned in the sidebar for easy access while viewing content.
            </p>
        </div>
    </div>
</Story>

<!-- Responsive examples -->
<Story name="Mobile Layout">
    <div class="max-w-sm mx-auto">
        <div class="bg-background border border-border rounded-lg p-4">
            <h3 class="font-semibold mb-4 text-center">Mobile Audio Player</h3>
            <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
        </div>
    </div>
</Story>

<!-- Playlist-like interface -->
<Story name="Playlist Interface">
    <div class="max-w-2xl mx-auto p-4">
        <h3 class="text-xl font-bold mb-6">Meditation Playlist</h3>
    
        <div class="space-y-4">
            <div class="bg-background border border-border rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <div>
                        <h4 class="font-medium">1. Morning Bell</h4>
                        <p class="text-sm text-muted-foreground">Start your day with mindfulness</p>
                    </div>
                    <span class="text-xs text-muted-foreground">0:30</span>
                </div>
                <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
            </div>
      
            <div class="bg-background border border-border rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <div>
                        <h4 class="font-medium">2. Rain Meditation</h4>
                        <p class="text-sm text-muted-foreground">Gentle rain for deep relaxation</p>
                    </div>
                    <span class="text-xs text-muted-foreground">2:15</span>
                </div>
                <FullPlayer src="https://www.soundjay.com/nature/sounds/rain-01.wav" />
            </div>
      
            <div class="bg-background border border-border rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <div>
                        <h4 class="font-medium">3. Ocean Waves</h4>
                        <p class="text-sm text-muted-foreground">Calming ocean sounds</p>
                    </div>
                    <span class="text-xs text-muted-foreground">3:45</span>
                </div>
                <FullPlayer src="https://www.soundjay.com/nature/sounds/ocean-wave-1.wav" />
            </div>
        </div>
    </div>
</Story>

<!-- Feature demonstrations -->
<Story name="Buffering and Loading States">
    <div class="p-4 space-y-6">
        <h3 class="text-lg font-semibold">Loading and Buffering States</h3>
        <p class="text-sm text-muted-foreground">
            The player shows loading spinners and buffering indicators during playback.
        </p>
    
        <div class="space-y-4">
            <div>
                <h4 class="font-medium mb-2">Large Audio File (may show buffering)</h4>
                <FullPlayer src="https://www.soundjay.com/nature/sounds/rain-01.wav" />
            </div>
        </div>
    </div>
</Story>

<Story name="Seek Bar Functionality">
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Seek Bar Features</h3>
        <div class="space-y-4">
            <div class="text-sm text-muted-foreground space-y-2">
                <p>• Click anywhere on the seek bar to jump to that position</p>
                <p>• Shows current time and total duration</p>
                <p>• Displays buffering progress</p>
                <p>• Visual feedback for current position</p>
            </div>
            <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
        </div>
    </div>
</Story>

<!-- Accessibility features -->
<Story name="Accessibility Features">
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Accessibility Features</h3>
        <div class="space-y-4">
            <div class="text-sm text-muted-foreground space-y-2">
                <p>• Keyboard accessible controls</p>
                <p>• Screen reader compatible</p>
                <p>• Clear visual feedback for all states</p>
                <p>• Proper ARIA labels and roles</p>
                <p>• High contrast button states</p>
            </div>
            <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
        </div>
    </div>
</Story>

<!-- Cache status demonstration -->
<Story name="Cache Status Indicator">
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Offline Cache Status</h3>
        <div class="space-y-4">
            <div class="text-sm text-muted-foreground space-y-2">
                <p>• Shows "Online" for remote files being streamed</p>
                <p>• Shows "Cached" for files stored locally</p>
                <p>• Automatic caching for offline playback</p>
            </div>
            <FullPlayer src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" />
        </div>
    </div>
</Story>