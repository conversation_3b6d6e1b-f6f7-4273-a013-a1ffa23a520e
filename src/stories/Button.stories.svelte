<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import Button from '../lib/components/Button.svelte';
    import { fn } from 'storybook/test';

    const { Story } = defineMeta({
        title: 'Components/Button',
        component: Button,
        tags: ['autodocs'],
        argTypes: {
            variant: {
                control: { type: 'select' },
                options: ['default', 'secondary', 'destructive', 'outline', 'ghost', 'link'],
            },
            size: {
                control: { type: 'select' },
                options: ['sm', 'md', 'lg'],
            },
            disabled: {
                control: 'boolean',
            },
            type: {
                control: { type: 'select' },
                options: ['button', 'submit', 'reset'],
            },
        },
        args: {
            onclick: fn(),
        }
    });
</script>

<!-- Default variants -->
<Story name="Default" args={{ variant: 'default', children: 'Default Button' }}>
    <Button variant="default" on:click>Default Button</Button>
</Story>

<Story name="Secondary" args={{ variant: 'secondary', children: 'Secondary Button' }}>
    <Button variant="secondary" on:click>Secondary Button</Button>
</Story>

<Story name="Destructive" args={{ variant: 'destructive', children: 'Destructive Button' }}>
    <Button variant="destructive" on:click>Destructive Button</Button>
</Story>

<Story name="Outline" args={{ variant: 'outline', children: 'Outline Button' }}>
    <Button variant="outline" on:click>Outline Button</Button>
</Story>

<Story name="Ghost" args={{ variant: 'ghost', children: 'Ghost Button' }}>
    <Button variant="ghost" on:click>Ghost Button</Button>
</Story>

<Story name="Link" args={{ variant: 'link', children: 'Link Button' }}>
    <Button variant="link" on:click>Link Button</Button>
</Story>

<!-- Size variants -->
<Story name="Small" args={{ size: 'sm', children: 'Small Button' }}>
    <Button size="sm" on:click>Small Button</Button>
</Story>

<Story name="Default Size" args={{ size: 'md', children: 'Default Size Button' }}>
    <Button size="md" on:click>Default Size Button</Button>
</Story>

<Story name="Large" args={{ size: 'lg', children: 'Large Button' }}>
    <Button size="lg" on:click>Large Button</Button>
</Story>

<!-- State variants -->
<Story name="Disabled" args={{ disabled: true, children: 'Disabled Button' }}>
    <Button disabled on:click>Disabled Button</Button>
</Story>

<Story name="Loading State" args={{ children: 'Loading...' }}>
    <Button disabled on:click>
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading...
    </Button>
</Story>

<!-- Type variants -->
<Story name="Submit Button" args={{ type: 'submit', children: 'Submit' }}>
    <Button type="submit" on:click>Submit</Button>
</Story>

<Story name="Reset Button" args={{ type: 'reset', children: 'Reset' }}>
    <Button type="reset" on:click>Reset</Button>
</Story>

<!-- With icons -->
<Story name="With Icon Left" args={{ children: 'Download' }}>
    <Button on:click>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        Download
    </Button>
</Story>

<Story name="With Icon Right" args={{ children: 'Next' }}>
    <Button on:click>
        Next
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
    </Button>
</Story>

<Story name="Icon Only" args={{ children: 'Settings' }}>
    <Button variant="outline" size="sm" on:click>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
    </Button>
</Story>

<!-- Combination examples -->
<Story name="All Variants Showcase">
    <div class="space-y-4">
        <div class="space-x-2">
            <Button variant="default">Default</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="destructive">Destructive</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="link">Link</Button>
        </div>
        <div class="space-x-2">
            <Button size="sm">Small</Button>
            <Button size="md">Default</Button>
            <Button size="lg">Large</Button>
        </div>
        <div class="space-x-2">
            <Button disabled>Disabled</Button>
            <Button variant="outline" disabled>Disabled Outline</Button>
            <Button variant="destructive" disabled>Disabled Destructive</Button>
        </div>
    </div>
</Story>
