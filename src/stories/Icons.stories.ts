import type { Meta, StoryObj } from '@storybook/sveltekit';
import IconDisplay from './IconDisplay.svelte';

// Import a single icon for the example
import { Rocket } from 'lucide-svelte';

const meta = {
    title: 'Components/Icons',
    component: IconDisplay, // Set component to IconDisplay
    tags: ['autodocs'],
    argTypes: {
        IconComponent: {
            control: { type: 'select' },
            options: [Rocket], // Use an array for options
            mapping: { Rocket: 'Rocket' },
        },
        label: { control: 'text' },
        size: { control: 'text' },
        color: { control: 'color' },
    },
} satisfies Meta<IconDisplay>;

export default meta;

type Story = StoryObj<typeof meta>;

export const ExampleIcon: Story = {
    args: {
        IconComponent: Rocket,
        label: 'Rocket Icon',
        size: '48px',
        color: 'blue',
    },
};