<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import BreathingFocusWord from '../lib/components/BreathingFocusWord.svelte';

    const { Story } = defineMeta({
        title: 'Components/BreathingFocusWord',
        component: BreathingFocusWord,
        tags: ['autodocs'],
        argTypes: {
            focusWords: {
                control: 'object',
                description: 'Array of focus words to cycle through',
            },
            cycleTime: {
                control: { type: 'range', min: 0, max: 20, step: 0.1 },
                description: 'Current time within the breathing cycle',
            },
            inhaleDuration: {
                control: { type: 'range', min: 1, max: 10, step: 0.5 },
                description: 'Duration of inhale phase in seconds',
            },
            breathingCycleDuration: {
                control: { type: 'range', min: 5, max: 30, step: 1 },
                description: 'Total breathing cycle duration in seconds',
            },
            elapsed: {
                control: { type: 'range', min: 0, max: 100, step: 1 },
                description: 'Total elapsed time since start',
            },
        },
        args: {
            focusWords: ['Breathe', 'Relax', 'Focus', 'Calm'],
            cycleTime: 2,
            inhaleDuration: 4,
            breathingCycleDuration: 13,
            elapsed: 15,
        }
    });
</script>

<!-- Basic focus word display -->
<Story name="Basic Focus Words" args={{
    focusWords: ['Breathe', 'Relax', 'Focus', 'Calm'],
    cycleTime: 2,
    inhaleDuration: 4,
    breathingCycleDuration: 13,
    elapsed: 15
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Focus Word Display</h3>
        <p class="text-sm text-muted-foreground mb-8">
            Words fade in during inhale, stay visible, then fade out near cycle end
        </p>
        <BreathingFocusWord 
            focusWords={['Breathe', 'Relax', 'Focus', 'Calm']}
            cycleTime={2}
            inhaleDuration={4}
            breathingCycleDuration={13}
            elapsed={15}
        />
    </div>
</Story>

<!-- Different word sets -->
<Story name="Mindfulness Words" args={{
    focusWords: ['Present', 'Aware', 'Mindful', 'Centered'],
    cycleTime: 1,
    inhaleDuration: 3,
    breathingCycleDuration: 10,
    elapsed: 5
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Mindfulness Focus</h3>
        <BreathingFocusWord 
            focusWords={['Present', 'Aware', 'Mindful', 'Centered']}
            cycleTime={1}
            inhaleDuration={3}
            breathingCycleDuration={10}
            elapsed={5}
        />
    </div>
</Story>

<Story name="Positive Affirmations" args={{
    focusWords: ['I am calm', 'I am strong', 'I am peaceful', 'I am centered'],
    cycleTime: 3,
    inhaleDuration: 5,
    breathingCycleDuration: 15,
    elapsed: 20
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Positive Affirmations</h3>
        <BreathingFocusWord 
            focusWords={['I am calm', 'I am strong', 'I am peaceful', 'I am centered']}
            cycleTime={3}
            inhaleDuration={5}
            breathingCycleDuration={15}
            elapsed={20}
        />
    </div>
</Story>

<Story name="Nature Words" args={{
    focusWords: ['Ocean', 'Mountain', 'Forest', 'Sky', 'River', 'Sunset'],
    cycleTime: 4,
    inhaleDuration: 4,
    breathingCycleDuration: 12,
    elapsed: 30
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Nature Imagery</h3>
        <BreathingFocusWord 
            focusWords={['Ocean', 'Mountain', 'Forest', 'Sky', 'River', 'Sunset']}
            cycleTime={4}
            inhaleDuration={4}
            breathingCycleDuration={12}
            elapsed={30}
        />
    </div>
</Story>

<!-- Single word -->
<Story name="Single Focus Word" args={{
    focusWords: ['Peace'],
    cycleTime: 2,
    inhaleDuration: 4,
    breathingCycleDuration: 10,
    elapsed: 8
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Single Word Focus</h3>
        <BreathingFocusWord 
            focusWords={['Peace']}
            cycleTime={2}
            inhaleDuration={4}
            breathingCycleDuration={10}
            elapsed={8}
        />
    </div>
</Story>

<!-- Different breathing patterns -->
<Story name="Quick Breathing Pattern" args={{
    focusWords: ['In', 'Hold', 'Out', 'Rest'],
    cycleTime: 1,
    inhaleDuration: 2,
    breathingCycleDuration: 6,
    elapsed: 12
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Quick Breathing (6s cycle)</h3>
        <BreathingFocusWord 
            focusWords={['In', 'Hold', 'Out', 'Rest']}
            cycleTime={1}
            inhaleDuration={2}
            breathingCycleDuration={6}
            elapsed={12}
        />
    </div>
</Story>

<Story name="Slow Breathing Pattern" args={{
    focusWords: ['Deep In', 'Hold Steady', 'Slow Out', 'Rest'],
    cycleTime: 6,
    inhaleDuration: 6,
    breathingCycleDuration: 20,
    elapsed: 40
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Slow Breathing (20s cycle)</h3>
        <BreathingFocusWord 
            focusWords={['Deep In', 'Hold Steady', 'Slow Out', 'Rest']}
            cycleTime={6}
            inhaleDuration={6}
            breathingCycleDuration={20}
            elapsed={40}
        />
    </div>
</Story>

<!-- Animation phases demonstration -->
<Story name="Fade In Phase" args={{
    focusWords: ['Inhaling'],
    cycleTime: 1,
    inhaleDuration: 4,
    breathingCycleDuration: 12,
    elapsed: 1
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Fade In Phase</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Word fades in during inhale (first {4} seconds)
        </p>
        <BreathingFocusWord 
            focusWords={['Inhaling']}
            cycleTime={1}
            inhaleDuration={4}
            breathingCycleDuration={12}
            elapsed={1}
        />
    </div>
</Story>

<Story name="Hold Phase" args={{
    focusWords: ['Holding'],
    cycleTime: 6,
    inhaleDuration: 4,
    breathingCycleDuration: 12,
    elapsed: 6
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Hold Phase</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Word stays fully visible during middle of cycle
        </p>
        <BreathingFocusWord 
            focusWords={['Holding']}
            cycleTime={6}
            inhaleDuration={4}
            breathingCycleDuration={12}
            elapsed={6}
        />
    </div>
</Story>

<Story name="Fade Out Phase" args={{
    focusWords: ['Fading'],
    cycleTime: 10,
    inhaleDuration: 4,
    breathingCycleDuration: 12,
    elapsed: 10
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Fade Out Phase</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Word fades out in last 8 seconds of cycle
        </p>
        <BreathingFocusWord 
            focusWords={['Fading']}
            cycleTime={10}
            inhaleDuration={4}
            breathingCycleDuration={12}
            elapsed={10}
        />
    </div>
</Story>

<!-- Word cycling demonstration -->
<Story name="Word Cycling Demo">
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Word Cycling</h3>
        <p class="text-sm text-muted-foreground mb-8">
            Words advance with each breathing cycle. Current cycle determines which word is shown.
        </p>
    
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="p-4 bg-muted/20 rounded-lg">
                <h4 class="font-medium mb-3">Cycle 1 - "Breathe"</h4>
                <BreathingFocusWord 
                    focusWords={['Breathe', 'Relax', 'Focus', 'Calm']}
                    cycleTime={2}
                    inhaleDuration={4}
                    breathingCycleDuration={10}
                    elapsed={2}
                />
            </div>
      
            <div class="p-4 bg-muted/20 rounded-lg">
                <h4 class="font-medium mb-3">Cycle 2 - "Relax"</h4>
                <BreathingFocusWord 
                    focusWords={['Breathe', 'Relax', 'Focus', 'Calm']}
                    cycleTime={2}
                    inhaleDuration={4}
                    breathingCycleDuration={10}
                    elapsed={12}
                />
            </div>
      
            <div class="p-4 bg-muted/20 rounded-lg">
                <h4 class="font-medium mb-3">Cycle 3 - "Focus"</h4>
                <BreathingFocusWord 
                    focusWords={['Breathe', 'Relax', 'Focus', 'Calm']}
                    cycleTime={2}
                    inhaleDuration={4}
                    breathingCycleDuration={10}
                    elapsed={22}
                />
            </div>
      
            <div class="p-4 bg-muted/20 rounded-lg">
                <h4 class="font-medium mb-3">Cycle 4 - "Calm"</h4>
                <BreathingFocusWord 
                    focusWords={['Breathe', 'Relax', 'Focus', 'Calm']}
                    cycleTime={2}
                    inhaleDuration={4}
                    breathingCycleDuration={10}
                    elapsed={32}
                />
            </div>
        </div>
    </div>
</Story>

<!-- Different languages -->
<Story name="Different Languages" args={{
    focusWords: ['Respirar', 'Relajar', 'Enfocar', 'Calma'],
    cycleTime: 3,
    inhaleDuration: 4,
    breathingCycleDuration: 12,
    elapsed: 15
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Spanish Focus Words</h3>
        <BreathingFocusWord 
            focusWords={['Respirar', 'Relajar', 'Enfocar', 'Calma']}
            cycleTime={3}
            inhaleDuration={4}
            breathingCycleDuration={12}
            elapsed={15}
        />
    </div>
</Story>

<!-- Long words -->
<Story name="Long Focus Phrases" args={{
    focusWords: ['Breathing deeply', 'Releasing tension', 'Finding peace', 'Embracing calm'],
    cycleTime: 2,
    inhaleDuration: 5,
    breathingCycleDuration: 15,
    elapsed: 10
}}>
    <div class="p-8 bg-background border border-border rounded-lg text-center">
        <h3 class="text-lg font-semibold mb-6">Longer Phrases</h3>
        <BreathingFocusWord 
            focusWords={['Breathing deeply', 'Releasing tension', 'Finding peace', 'Embracing calm']}
            cycleTime={2}
            inhaleDuration={5}
            breathingCycleDuration={15}
            elapsed={10}
        />
    </div>
</Story>

<!-- Feature demonstration -->
<Story name="Feature Demonstration">
    <div class="p-8 space-y-8">
        <h3 class="text-lg font-semibold text-center">BreathingFocusWord Features</h3>
    
        <div class="space-y-4 text-sm text-muted-foreground">
            <p>• <strong>Synchronized animation:</strong> Words fade in during inhale phase</p>
            <p>• <strong>Cycle-based progression:</strong> Advances to next word each breathing cycle</p>
            <p>• <strong>Smooth transitions:</strong> Configurable fade timing based on breathing pattern</p>
            <p>• <strong>Responsive text:</strong> Adapts to different word lengths and screen sizes</p>
            <p>• <strong>Customizable timing:</strong> Works with any breathing pattern duration</p>
        </div>
    
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="p-4 bg-muted/20 rounded-lg text-center">
                <h4 class="font-medium mb-3">4-7-8 Breathing</h4>
                <BreathingFocusWord 
                    focusWords={['In 4', 'Hold 7', 'Out 8']}
                    cycleTime={2}
                    inhaleDuration={4}
                    breathingCycleDuration={19}
                    elapsed={5}
                />
            </div>
      
            <div class="p-4 bg-muted/20 rounded-lg text-center">
                <h4 class="font-medium mb-3">Box Breathing</h4>
                <BreathingFocusWord 
                    focusWords={['In', 'Hold', 'Out', 'Hold']}
                    cycleTime={2}
                    inhaleDuration={4}
                    breathingCycleDuration={16}
                    elapsed={10}
                />
            </div>
      
            <div class="p-4 bg-muted/20 rounded-lg text-center">
                <h4 class="font-medium mb-3">Simple Breathing</h4>
                <BreathingFocusWord 
                    focusWords={['Breathe In', 'Breathe Out']}
                    cycleTime={1}
                    inhaleDuration={3}
                    breathingCycleDuration={7}
                    elapsed={15}
                />
            </div>
        </div>
    </div>
</Story>

<!-- Integration example -->
<Story name="Integration with Breathing Exercise">
    <div class="p-8 bg-background border border-border rounded-lg">
        <div class="text-center mb-8">
            <h3 class="text-xl font-semibold mb-2">Guided Breathing Session</h3>
            <p class="text-muted-foreground">Focus on the word as you breathe</p>
        </div>
    
        <div class="flex flex-col items-center space-y-8">
            <!-- Simulated breathing circle -->
            <div class="w-32 h-32 bg-primary/20 rounded-full flex items-center justify-center">
                <div class="w-24 h-24 bg-primary/40 rounded-full flex items-center justify-center">
                    <div class="w-16 h-16 bg-primary/60 rounded-full"></div>
                </div>
            </div>
      
            <!-- Focus word -->
            <BreathingFocusWord 
                focusWords={['Peace', 'Calm', 'Serenity', 'Balance']}
                cycleTime={3}
                inhaleDuration={4}
                breathingCycleDuration={12}
                elapsed={25}
            />
      
            <!-- Instructions -->
            <div class="text-center text-sm text-muted-foreground max-w-md">
                <p>Breathe in for 4 seconds, hold for 2 seconds, breathe out for 6 seconds. 
                    Let the focus word guide your attention and help you stay present.</p>
            </div>
        </div>
    </div>
</Story>