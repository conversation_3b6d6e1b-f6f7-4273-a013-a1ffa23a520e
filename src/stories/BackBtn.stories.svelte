<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import BackBtn from '../lib/components/BackBtn.svelte';

    const { Story } = defineMeta({
        title: 'Components/BackBtn',
        component: BackBtn,
        tags: ['autodocs'],
        argTypes: {
            title: {
                control: 'text',
                description: 'Optional title to display in the center of the header',
            },
        },
        args: {
            title: '',
        }
    });
</script>

<!-- Basic back button -->
<Story name="Basic Back Button" args={{ title: '' }}>
    <div class="border border-border rounded-lg overflow-hidden">
        <BackBtn />
        <div class="p-4 bg-muted/20">
            <p class="text-sm text-muted-foreground">
                Content area below the back button header.
            </p>
        </div>
    </div>
</Story>

<Story name="With Title" args={{ title: 'Settings' }}>
    <div class="border border-border rounded-lg overflow-hidden">
        <BackBtn title="Settings" />
        <div class="p-4 bg-muted/20">
            <p class="text-sm text-muted-foreground">
                Back button with a centered title in the header.
            </p>
        </div>
    </div>
</Story>

<!-- Different page titles -->
<Story name="Profile Page" args={{ title: 'Profile' }}>
    <div class="border border-border rounded-lg overflow-hidden">
        <BackBtn title="Profile" />
        <div class="p-4 bg-muted/20">
            <div class="space-y-4">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-primary rounded-full"></div>
                    <div>
                        <h3 class="font-semibold">John Doe</h3>
                        <p class="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</Story>

<Story name="About Page" args={{ title: 'About' }}>
    <div class="border border-border rounded-lg overflow-hidden">
        <BackBtn title="About" />
        <div class="p-4 bg-muted/20">
            <h3 class="font-semibold mb-2">About This App</h3>
            <p class="text-sm text-muted-foreground">
                This is an example about page with a back button header.
            </p>
        </div>
    </div>
</Story>

<Story name="Contact Page" args={{ title: 'Contact' }}>
    <div class="border border-border rounded-lg overflow-hidden">
        <BackBtn title="Contact" />
        <div class="p-4 bg-muted/20">
            <div class="space-y-3">
                <div>
                    <label for="contact-name" class="block text-sm font-medium mb-1">Name</label>
                    <input id="contact-name" type="text" class="w-full p-2 border border-border rounded" />
                </div>
                <div>
                    <label for="contact-email" class="block text-sm font-medium mb-1">Email</label>
                    <input id="contact-email" type="email" class="w-full p-2 border border-border rounded" />
                </div>
            </div>
        </div>
    </div>
</Story>

<!-- Long titles -->
<Story name="Long Title" args={{ title: 'Very Long Page Title That Might Wrap' }}>
    <div class="border border-border rounded-lg overflow-hidden">
        <BackBtn title="Very Long Page Title That Might Wrap" />
        <div class="p-4 bg-muted/20">
            <p class="text-sm text-muted-foreground">
                Testing how the component handles longer titles.
            </p>
        </div>
    </div>
</Story>

<!-- Mobile-like layouts -->
<Story name="Mobile Layout">
    <div class="max-w-sm mx-auto border border-border rounded-lg overflow-hidden">
        <BackBtn title="Mobile View" />
        <div class="p-4 bg-muted/20">
            <div class="space-y-4">
                <div class="text-center">
                    <h3 class="font-semibold">Mobile Interface</h3>
                    <p class="text-sm text-muted-foreground">
                        This shows how the back button looks on mobile devices.
                    </p>
                </div>
                <div class="space-y-2">
                    <button class="w-full p-3 bg-primary text-primary-foreground rounded">
                        Primary Action
                    </button>
                    <button class="w-full p-3 bg-secondary text-secondary-foreground rounded">
                        Secondary Action
                    </button>
                </div>
            </div>
        </div>
    </div>
</Story>

<!-- Different content types -->
<Story name="Settings Interface" args={{ title: 'Settings' }}>
    <div class="max-w-md mx-auto border border-border rounded-lg overflow-hidden">
        <BackBtn title="Settings" />
        <div class="p-4 bg-muted/20">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">Notifications</span>
                    <input type="checkbox" class="rounded" />
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">Dark Mode</span>
                    <input type="checkbox" class="rounded" checked />
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">Auto-save</span>
                    <input type="checkbox" class="rounded" />
                </div>
            </div>
        </div>
    </div>
</Story>

<Story name="Form Interface" args={{ title: 'Edit Profile' }}>
    <div class="max-w-md mx-auto border border-border rounded-lg overflow-hidden">
        <BackBtn title="Edit Profile" />
        <div class="p-4 bg-muted/20">
            <form class="space-y-4">
                <div>
                    <label for="edit-first-name" class="block text-sm font-medium mb-1">First Name</label>
                    <input id="edit-first-name" type="text" value="John" class="w-full p-2 border border-border rounded" />
                </div>
                <div>
                    <label for="edit-last-name" class="block text-sm font-medium mb-1">Last Name</label>
                    <input id="edit-last-name" type="text" value="Doe" class="w-full p-2 border border-border rounded" />
                </div>
                <div>
                    <label for="edit-email" class="block text-sm font-medium mb-1">Email</label>
                    <input id="edit-email" type="email" value="<EMAIL>" class="w-full p-2 border border-border rounded" />
                </div>
                <button type="submit" class="w-full p-2 bg-primary text-primary-foreground rounded">
                    Save Changes
                </button>
            </form>
        </div>
    </div>
</Story>

<!-- Navigation flow examples -->
<Story name="Navigation Flow Example">
    <div class="space-y-4">
        <div class="text-sm text-muted-foreground mb-4">
            Example of how back buttons work in a navigation flow:
        </div>
    
        <!-- Home page mockup -->
        <div class="border border-border rounded-lg overflow-hidden">
            <div class="p-4 bg-background border-b border-border">
                <h3 class="font-semibold">Home Page</h3>
            </div>
            <div class="p-4 bg-muted/20">
                <button class="text-primary hover:underline">Go to Settings →</button>
            </div>
        </div>
    
        <!-- Settings page with back button -->
        <div class="border border-border rounded-lg overflow-hidden">
            <BackBtn title="Settings" />
            <div class="p-4 bg-muted/20">
                <div class="space-y-2">
                    <button class="text-primary hover:underline">Account Settings →</button>
                    <button class="text-primary hover:underline">Privacy Settings →</button>
                    <button class="text-primary hover:underline">Notification Settings →</button>
                </div>
            </div>
        </div>
    
        <!-- Nested page with back button -->
        <div class="border border-border rounded-lg overflow-hidden">
            <BackBtn title="Account Settings" />
            <div class="p-4 bg-muted/20">
                <div class="space-y-3">
                    <div>
                        <label for="account-username" class="block text-sm font-medium mb-1">Username</label>
                        <input id="account-username" type="text" value="johndoe" class="w-full p-2 border border-border rounded" />
                    </div>
                    <div>
                        <label for="account-display-name" class="block text-sm font-medium mb-1">Display Name</label>
                        <input id="account-display-name" type="text" value="John Doe" class="w-full p-2 border border-border rounded" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</Story>

<!-- Accessibility features -->
<Story name="Accessibility Features" args={{ title: 'Accessibility Demo' }}>
    <div class="border border-border rounded-lg overflow-hidden">
        <BackBtn title="Accessibility Demo" />
        <div class="p-4 bg-muted/20">
            <div class="space-y-4">
                <h3 class="font-semibold">Accessibility Features</h3>
                <ul class="text-sm space-y-2 text-muted-foreground">
                    <li>• <strong>aria-label:</strong> "Go back" for screen readers</li>
                    <li>• <strong>Keyboard accessible:</strong> Can be activated with Enter/Space</li>
                    <li>• <strong>Visual feedback:</strong> Hover states and focus indicators</li>
                    <li>• <strong>Semantic HTML:</strong> Proper button element</li>
                    <li>• <strong>Smart navigation:</strong> Falls back to home if no history</li>
                </ul>
            </div>
        </div>
    </div>
</Story>

<!-- Different header styles -->
<Story name="Header Variations">
    <div class="space-y-6">
        <div class="text-sm text-muted-foreground">
            The back button component provides a consistent header layout:
        </div>
    
        <div class="space-y-4">
            <div class="border border-border rounded-lg overflow-hidden">
                <BackBtn />
                <div class="p-2 bg-muted/20 text-center text-sm">No title</div>
            </div>
      
            <div class="border border-border rounded-lg overflow-hidden">
                <BackBtn title="Short" />
                <div class="p-2 bg-muted/20 text-center text-sm">Short title</div>
            </div>
      
            <div class="border border-border rounded-lg overflow-hidden">
                <BackBtn title="Medium Length Title" />
                <div class="p-2 bg-muted/20 text-center text-sm">Medium title</div>
            </div>
      
            <div class="border border-border rounded-lg overflow-hidden">
                <BackBtn title="Very Long Title That Tests Layout" />
                <div class="p-2 bg-muted/20 text-center text-sm">Long title</div>
            </div>
        </div>
    </div>
</Story>

<!-- Real-world usage -->
<Story name="Real-world Usage Examples">
    <div class="space-y-8">
        <!-- App-like interface -->
        <div class="max-w-sm mx-auto">
            <div class="bg-background border border-border rounded-lg overflow-hidden shadow-sm">
                <BackBtn title="Your Best Days" />
                <div class="p-4 space-y-4">
                    <div class="text-center">
                        <h3 class="text-lg font-semibold mb-2">Welcome Back!</h3>
                        <p class="text-sm text-muted-foreground">
                            Continue your mindfulness journey
                        </p>
                    </div>
                    <div class="space-y-2">
                        <button class="w-full p-3 bg-primary text-primary-foreground rounded-lg">
                            Start Meditation
                        </button>
                        <button class="w-full p-3 bg-secondary text-secondary-foreground rounded-lg">
                            View Progress
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detail page -->
        <div class="max-w-md mx-auto">
            <div class="bg-background border border-border rounded-lg overflow-hidden shadow-sm">
                <BackBtn title="Breathing Exercise" />
                <div class="p-4 space-y-4">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-primary/20 rounded-full mx-auto mb-3 flex items-center justify-center">
                            <svg class="w-10 h-10 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold mb-2">4-7-8 Breathing</h3>
                        <p class="text-sm text-muted-foreground">
                            A calming breathing technique for relaxation
                        </p>
                    </div>
                    <button class="w-full p-3 bg-primary text-primary-foreground rounded-lg">
                        Start Exercise
                    </button>
                </div>
            </div>
        </div>
    </div>
</Story>