<script>
    import { Meta, Template, Story } from '@storybook/addon-svelte-csf';
    import YbdIcon2 from '$lib/components/YbdIcon2.svelte';
</script>

<Meta
    title="Components/YbdIcon2"
    component={YbdIcon2}
    argTypes={{
        size: {
            control: 'text',
            description: 'Size of the icon in pixels',
            defaultValue: '24'
        },
        fill: {
            control: 'color',
            description: 'Fill color of the icon',
            defaultValue: 'currentColor'
        },
        stroke: {
            control: 'color',
            description: 'Stroke color of the icon',
            defaultValue: 'none'
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes',
            defaultValue: ''
        }
    }}
/>

<Template let:args>
    <YbdIcon2 {...args} />
</Template>

<!-- Default Story -->
<Story name="Default" args={{
    size: '24',
    fill: 'currentColor',
    stroke: 'none',
    className: ''
}}>
    <YbdIcon2 />
</Story>

<!-- Size Variations -->
<Story name="Small" args={{
    size: '16',
    fill: 'currentColor'
}}>
    <YbdIcon2 size="16" />
</Story>

<Story name="Medium" args={{
    size: '32',
    fill: 'currentColor'
}}>
    <YbdIcon2 size="32" />
</Story>

<Story name="Large" args={{
    size: '48',
    fill: 'currentColor'
}}>
    <YbdIcon2 size="48" />
</Story>

<Story name="Extra Large" args={{
    size: '64',
    fill: 'currentColor'
}}>
    <YbdIcon2 size="64" />
</Story>

<!-- Color Variations -->
<Story name="Blue" args={{
    size: '32',
    fill: '#3b82f6'
}}>
    <YbdIcon2 size="32" fill="#3b82f6" />
</Story>

<Story name="Red" args={{
    size: '32',
    fill: '#ef4444'
}}>
    <YbdIcon2 size="32" fill="#ef4444" />
</Story>

<Story name="Green" args={{
    size: '32',
    fill: '#10b981'
}}>
    <YbdIcon2 size="32" fill="#10b981" />
</Story>

<Story name="Purple" args={{
    size: '32',
    fill: '#8b5cf6'
}}>
    <YbdIcon2 size="32" fill="#8b5cf6" />
</Story>

<!-- With CSS Classes -->
<Story name="With Tailwind Classes" args={{
    size: '32',
    className: 'text-blue-500'
}}>
    <YbdIcon2 size="32" className="text-blue-500" />
</Story>

<!-- Stroke Examples -->
<Story name="Stroke Only" args={{
    size: '32',
    fill: 'none',
    stroke: '#3b82f6'
}}>
    <YbdIcon2 size="32" fill="none" stroke="#3b82f6" />
</Story>

<Story name="Fill and Stroke" args={{
    size: '32',
    fill: 'rgba(59, 130, 246, 0.3)',
    stroke: '#3b82f6'
}}>
    <YbdIcon2 size="32" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" />
</Story>

<!-- Usage Examples -->
<Story name="Navigation Example">
    <div class="flex items-center gap-2 p-4 bg-gray-100 rounded">
        <YbdIcon2 size="20" className="text-blue-600" />
        <span class="font-medium">Your Best Days</span>
    </div>
</Story>

<Story name="Button Example">
    <button class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
        <YbdIcon2 size="16" fill="currentColor" />
        Start YBD Session
    </button>
</Story>

<Story name="Card Header Example">
    <div class="p-6 border rounded-lg max-w-sm">
        <div class="flex items-center gap-3 mb-4">
            <YbdIcon2 size="24" className="text-blue-600" />
            <h3 class="text-lg font-semibold">Your Best Days</h3>
        </div>
        <p class="text-gray-600">Transform your mindset and achieve your goals with personalized guidance.</p>
    </div>
</Story>

<Story name="Size Showcase">
    <div class="flex items-end gap-4 p-4">
        <div class="text-center">
            <YbdIcon2 size="16" className="text-blue-600" />
            <p class="text-xs mt-1">16px</p>
        </div>
        <div class="text-center">
            <YbdIcon2 size="24" className="text-blue-600" />
            <p class="text-xs mt-1">24px</p>
        </div>
        <div class="text-center">
            <YbdIcon2 size="32" className="text-blue-600" />
            <p class="text-xs mt-1">32px</p>
        </div>
        <div class="text-center">
            <YbdIcon2 size="48" className="text-blue-600" />
            <p class="text-xs mt-1">48px</p>
        </div>
        <div class="text-center">
            <YbdIcon2 size="64" className="text-blue-600" />
            <p class="text-xs mt-1">64px</p>
        </div>
    </div>
</Story>

<Story name="Color Palette">
    <div class="grid grid-cols-5 gap-4 p-4">
        <div class="text-center">
            <YbdIcon2 size="32" fill="#3b82f6" />
            <p class="text-xs mt-1">Blue</p>
        </div>
        <div class="text-center">
            <YbdIcon2 size="32" fill="#ef4444" />
            <p class="text-xs mt-1">Red</p>
        </div>
        <div class="text-center">
            <YbdIcon2 size="32" fill="#10b981" />
            <p class="text-xs mt-1">Green</p>
        </div>
        <div class="text-center">
            <YbdIcon2 size="32" fill="#f59e0b" />
            <p class="text-xs mt-1">Yellow</p>
        </div>
        <div class="text-center">
            <YbdIcon2 size="32" fill="#8b5cf6" />
            <p class="text-xs mt-1">Purple</p>
        </div>
    </div>
</Story>

<!-- Comparison with YbdIcon -->
<Story name="Icon Comparison">
    <div class="flex items-center gap-8 p-4">
        <div class="text-center">
            <YbdIcon2 size="48" className="text-blue-600" />
            <p class="text-sm mt-2 font-medium">YBD Icon 2</p>
            <p class="text-xs text-gray-500">Outlined version</p>
        </div>
    </div>
</Story>