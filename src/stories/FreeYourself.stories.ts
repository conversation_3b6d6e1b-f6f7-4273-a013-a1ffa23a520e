import type { Meta, StoryObj } from '@storybook/svelte';
import FreeYourself from '$lib/components/FreeYourself/FreeYourself.svelte';

const meta = {
    title: 'Components/FreeYourself',
    component: FreeYourself,
    parameters: {
        layout: 'fullscreen',
        docs: {
            description: {
                component: 'The FreeYourself component helps users transform their mental state through interactive emotion selection and guided responses.'
            }
        }
    },
    argTypes: {}
} satisfies Meta<FreeYourself>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {},
    parameters: {
        docs: {
            description: {
                story: 'The default FreeYourself experience showing the emotion selection grid.'
            }
        }
    }
};

export const MobileView: Story = {
    args: {},
    parameters: {
        viewport: {
            defaultViewport: 'mobile1'
        },
        docs: {
            description: {
                story: 'FreeYourself component optimized for mobile devices with responsive grid layout.'
            }
        }
    }
};

export const TabletView: Story = {
    args: {},
    parameters: {
        viewport: {
            defaultViewport: 'tablet'
        },
        docs: {
            description: {
                story: 'FreeYourself component on tablet devices showing medium grid layout.'
            }
        }
    }
};

export const DarkMode: Story = {
    args: {},
    parameters: {
        backgrounds: {
            default: 'dark'
        },
        docs: {
            description: {
                story: 'FreeYourself component in dark mode, showcasing theme support.'
            }
        }
    },
    decorators: [
        (Story) => ({
            Component: Story,
            props: {
                class: 'dark'
            }
        })
    ]
};