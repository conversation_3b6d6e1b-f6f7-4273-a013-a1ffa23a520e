<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import LoopPlayer from '../lib/components/PlayerAudio/LoopPlayer.svelte';

    const { Story } = defineMeta({
        title: 'Components/PlayerAudio/LoopPlayer',
        component: LoopPlayer,
        tags: ['autodocs'],
        argTypes: {
            src: {
                control: 'text',
                description: 'Audio source URL or file path for looping',
            },
            crossfadeDuration: {
                control: { type: 'range', min: 0, max: 2, step: 0.05 },
                description: 'Duration of crossfade between loops in seconds',
            },
        },
        args: {
            src: 'https://www.soundjay.com/nature/sounds/rain-01.wav',
            crossfadeDuration: 0.05,
        }
    });
</script>

<!-- Basic loop player -->
<Story name="Basic Loop Player" args={{
    src: 'https://www.soundjay.com/nature/sounds/rain-01.wav',
    crossfadeDuration: 0.05
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Basic Loop Player</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Seamlessly loops audio with crossfade for continuous playback.
        </p>
        <LoopPlayer 
            src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
            crossfadeDuration={0.05} 
        />
    </div>
</Story>

<!-- Different crossfade durations -->
<Story name="No Crossfade" args={{
    src: 'https://www.soundjay.com/nature/sounds/rain-01.wav',
    crossfadeDuration: 0
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">No Crossfade</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Loops without crossfade - may have a slight gap between loops.
        </p>
        <LoopPlayer 
            src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
            crossfadeDuration={0} 
        />
    </div>
</Story>

<Story name="Short Crossfade" args={{
    src: 'https://www.soundjay.com/nature/sounds/rain-01.wav',
    crossfadeDuration: 0.1
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Short Crossfade (0.1s)</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Quick crossfade for minimal overlap.
        </p>
        <LoopPlayer 
            src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
            crossfadeDuration={0.1} 
        />
    </div>
</Story>

<Story name="Long Crossfade" args={{
    src: 'https://www.soundjay.com/nature/sounds/rain-01.wav',
    crossfadeDuration: 0.5
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Long Crossfade (0.5s)</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Longer crossfade for smoother transitions.
        </p>
        <LoopPlayer 
            src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
            crossfadeDuration={0.5} 
        />
    </div>
</Story>

<!-- Different audio types -->
<Story name="Nature Sounds - Rain" args={{
    src: 'https://www.soundjay.com/nature/sounds/rain-01.wav',
    crossfadeDuration: 0.2
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Rain Sounds Loop</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Perfect for meditation and relaxation.
        </p>
        <LoopPlayer 
            src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
            crossfadeDuration={0.2} 
        />
    </div>
</Story>

<Story name="Nature Sounds - Ocean" args={{
    src: 'https://www.soundjay.com/nature/sounds/ocean-wave-1.wav',
    crossfadeDuration: 0.15
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Ocean Waves Loop</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Continuous ocean sounds for deep relaxation.
        </p>
        <LoopPlayer 
            src="https://www.soundjay.com/nature/sounds/ocean-wave-1.wav" 
            crossfadeDuration={0.15} 
        />
    </div>
</Story>

<Story name="Local Audio File" args={{
    src: '/audio/mountain_stream.wav',
    crossfadeDuration: 0.1
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Local Audio Loop</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Looping a local audio file from the public directory.
        </p>
        <LoopPlayer 
            src="/audio/mountain_stream.wav" 
            crossfadeDuration={0.1} 
        />
    </div>
</Story>

<!-- Multiple loop players -->
<Story name="Multiple Loop Players">
    <div class="p-4 space-y-6">
        <h3 class="text-lg font-semibold">Multiple Loop Players</h3>
        <p class="text-sm text-muted-foreground">
            Each loop player operates independently. You can play multiple ambient sounds simultaneously.
        </p>
    
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="p-4 bg-background border border-border rounded-lg">
                <h4 class="font-medium mb-2">Rain</h4>
                <p class="text-xs text-muted-foreground mb-3">Gentle rainfall</p>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
                    crossfadeDuration={0.1} 
                />
            </div>
      
            <div class="p-4 bg-background border border-border rounded-lg">
                <h4 class="font-medium mb-2">Ocean</h4>
                <p class="text-xs text-muted-foreground mb-3">Ocean waves</p>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/ocean-wave-1.wav" 
                    crossfadeDuration={0.15} 
                />
            </div>
      
            <div class="p-4 bg-background border border-border rounded-lg">
                <h4 class="font-medium mb-2">Forest</h4>
                <p class="text-xs text-muted-foreground mb-3">Forest ambience</p>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/forest-1.wav" 
                    crossfadeDuration={0.2} 
                />
            </div>
        </div>
    </div>
</Story>

<!-- Error handling -->
<Story name="Error Handling - Invalid URL" args={{
    src: 'https://invalid-url-that-does-not-exist.com/audio.wav',
    crossfadeDuration: 0.1
}}>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Error Handling</h3>
        <p class="text-sm text-muted-foreground mb-4">
            This demonstrates how the loop player handles invalid URLs.
        </p>
        <LoopPlayer 
            src="https://invalid-url-that-does-not-exist.com/audio.wav" 
            crossfadeDuration={0.1} 
        />
    </div>
</Story>

<!-- In different layouts -->
<Story name="In Meditation Interface">
    <div class="max-w-md mx-auto">
        <div class="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
            <div class="p-6 text-center">
                <h3 class="text-xl font-semibold mb-2">Meditation Session</h3>
                <p class="text-sm text-muted-foreground mb-6">
                    Choose your ambient sounds for meditation
                </p>
        
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                        <div class="text-left">
                            <div class="font-medium">Rain Sounds</div>
                            <div class="text-xs text-muted-foreground">Gentle rainfall</div>
                        </div>
                        <LoopPlayer 
                            src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
                            crossfadeDuration={0.2} 
                        />
                    </div>
          
                    <div class="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                        <div class="text-left">
                            <div class="font-medium">Ocean Waves</div>
                            <div class="text-xs text-muted-foreground">Calming waves</div>
                        </div>
                        <LoopPlayer 
                            src="https://www.soundjay.com/nature/sounds/ocean-wave-1.wav" 
                            crossfadeDuration={0.15} 
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</Story>

<Story name="Ambient Sound Mixer">
    <div class="max-w-2xl mx-auto p-4">
        <h3 class="text-xl font-bold mb-6 text-center">Ambient Sound Mixer</h3>
        <p class="text-center text-muted-foreground mb-8">
            Create your perfect ambient soundscape by combining different nature sounds
        </p>
    
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-background border border-border rounded-lg">
                <div class="w-16 h-16 mx-auto mb-3 bg-primary-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z"/>
                    </svg>
                </div>
                <h4 class="font-medium mb-2">Rain</h4>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
                    crossfadeDuration={0.2} 
                />
            </div>
      
            <div class="text-center p-4 bg-background border border-border rounded-lg">
                <div class="w-16 h-16 mx-auto mb-3 bg-primary-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h4 class="font-medium mb-2">Ocean</h4>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/ocean-wave-1.wav" 
                    crossfadeDuration={0.15} 
                />
            </div>
      
            <div class="text-center p-4 bg-background border border-border rounded-lg">
                <div class="w-16 h-16 mx-auto mb-3 bg-primary-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h4 class="font-medium mb-2">Forest</h4>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/forest-1.wav" 
                    crossfadeDuration={0.25} 
                />
            </div>
      
            <div class="text-center p-4 bg-background border border-border rounded-lg">
                <div class="w-16 h-16 mx-auto mb-3 bg-primary-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM6 9.5a.5.5 0 01.5-.5h7a.5.5 0 01.5.5v1a.5.5 0 01-.5.5h-7a.5.5 0 01-.5-.5v-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h4 class="font-medium mb-2">Wind</h4>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/wind-1.wav" 
                    crossfadeDuration={0.3} 
                />
            </div>
        </div>
    </div>
</Story>

<!-- Technical features -->
<Story name="Crossfade Comparison">
    <div class="p-4 space-y-6">
        <h3 class="text-lg font-semibold">Crossfade Duration Comparison</h3>
        <p class="text-sm text-muted-foreground">
            Compare different crossfade durations to hear the difference in loop smoothness.
        </p>
    
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="p-4 bg-background border border-border rounded-lg text-center">
                <h4 class="font-medium mb-2">No Crossfade</h4>
                <p class="text-xs text-muted-foreground mb-3">0.0s</p>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
                    crossfadeDuration={0} 
                />
            </div>
      
            <div class="p-4 bg-background border border-border rounded-lg text-center">
                <h4 class="font-medium mb-2">Quick</h4>
                <p class="text-xs text-muted-foreground mb-3">0.1s</p>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
                    crossfadeDuration={0.1} 
                />
            </div>
      
            <div class="p-4 bg-background border border-border rounded-lg text-center">
                <h4 class="font-medium mb-2">Medium</h4>
                <p class="text-xs text-muted-foreground mb-3">0.3s</p>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
                    crossfadeDuration={0.3} 
                />
            </div>
      
            <div class="p-4 bg-background border border-border rounded-lg text-center">
                <h4 class="font-medium mb-2">Long</h4>
                <p class="text-xs text-muted-foreground mb-3">0.5s</p>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
                    crossfadeDuration={0.5} 
                />
            </div>
        </div>
    </div>
</Story>

<!-- Usage instructions -->
<Story name="Usage Instructions">
    <div class="p-4 max-w-2xl mx-auto">
        <h3 class="text-lg font-semibold mb-4">How to Use Loop Player</h3>
    
        <div class="space-y-6">
            <div class="bg-background border border-border rounded-lg p-4">
                <h4 class="font-medium mb-2">Features</h4>
                <ul class="text-sm text-muted-foreground space-y-1">
                    <li>• Seamless audio looping with crossfade</li>
                    <li>• Web Audio API for precise timing</li>
                    <li>• iOS compatibility with audio unlock</li>
                    <li>• Automatic error handling</li>
                    <li>• Minimal UI - just play/pause</li>
                </ul>
            </div>
      
            <div class="bg-background border border-border rounded-lg p-4">
                <h4 class="font-medium mb-2">Best Practices</h4>
                <ul class="text-sm text-muted-foreground space-y-1">
                    <li>• Use short audio files (10-30 seconds) for best results</li>
                    <li>• Crossfade duration should be 5-10% of audio length</li>
                    <li>• Ensure audio files have clean start/end points</li>
                    <li>• Test on mobile devices for iOS compatibility</li>
                </ul>
            </div>
      
            <div class="bg-background border border-border rounded-lg p-4">
                <h4 class="font-medium mb-2">Try it out</h4>
                <p class="text-sm text-muted-foreground mb-3">
                    Click the button below to start looping audio:
                </p>
                <LoopPlayer 
                    src="https://www.soundjay.com/nature/sounds/rain-01.wav" 
                    crossfadeDuration={0.2} 
                />
            </div>
        </div>
    </div>
</Story>