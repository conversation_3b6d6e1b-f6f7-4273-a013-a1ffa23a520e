<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import Reading from '../lib/components/YBD/Reading.svelte';

    const { Story } = defineMeta({
        component: Reading,
        tags: ['autodocs'],
        argTypes: {
            ideas: {
                control: 'object',
                description: 'Array of YBD messages/ideas to display',
            },
            audioFile: {
                control: 'object',
                description: 'Optional audio file for guided reading',
            },
            betweenThoughts: {
                control: 'object',
                description: 'Array of thoughts to show between ideas',
            },
            requiredActionViewTime: {
                control: { type: 'range', min: 1, max: 30, step: 1 },
                description: 'Required time in seconds to view actions before proceeding',
            },
            displayTimeBetweenThoughts: {
                control: { type: 'range', min: 1, max: 15, step: 0.5 },
                description: 'Time to display between thoughts in seconds',
            },
        },
        args: {
            ideas: [
                {
                    id: '1',
                    message: 'Start your day with intention and mindfulness.',
                    quote: {
                        id: 'q1',
                        quote: 'The way to get started is to quit talking and begin doing.',
                        quote_author: 'Walt Disney'
                    },
                    favorite: false,
                    actionItems: [
                        { id: 'a1', action: 'Take three deep breaths' },
                        { id: 'a2', action: 'Set an intention for your day' }
                    ]
                }
            ],
            betweenThoughts: [
                { id: 'bt1', thought: 'Taking a moment to reflect...' },
                { id: 'bt2', thought: 'Moving forward with awareness...' }
            ],
            requiredActionViewTime: 5,
            displayTimeBetweenThoughts: 8,
        }
    });
</script>

<!-- Single idea with all features -->
<Story name="Complete Reading Experience" args={{
    ideas: [
        {
            id: '1',
            message: 'In the midst of life\'s chaos, we can always find a moment of peace within ourselves. This peace is not dependent on external circumstances, but rather on our ability to connect with our inner stillness.\n\nTake a moment now to notice your breath, the sensation of your body, and the space around you. This is your sanctuary, always available to you.',
            quote: {
                id: 'q1',
                quote: 'Peace comes from within. Do not seek it without.',
                quote_author: 'Buddha'
            },
            favorite: false,
            actionItems: [
                { id: 'a1', action: 'Close your eyes and take five deep breaths' },
                { id: 'a2', action: 'Notice the sensation of your breath' },
                { id: 'a3', action: 'Feel your body relaxing with each exhale' }
            ]
        }
    ],
    audioFile: {
        id: 'audio1',
        remoteUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
    },
    betweenThoughts: [
        { id: 'bt1', thought: 'Finding your center...' },
        { id: 'bt2', thought: 'Breathing in peace...' }
    ],
    requiredActionViewTime: 5,
    displayTimeBetweenThoughts: 6
}}>
    <Reading 
        ideas={[
            {
                id: '1',
                message: 'In the midst of life\'s chaos, we can always find a moment of peace within ourselves. This peace is not dependent on external circumstances, but rather on our ability to connect with our inner stillness.\n\nTake a moment now to notice your breath, the sensation of your body, and the space around you. This is your sanctuary, always available to you.',
                quote: {
                    id: 'q1',
                    quote: 'Peace comes from within. Do not seek it without.',
                    quote_author: 'Buddha'
                },
                favorite: false,
                actionItems: [
                    { id: 'a1', action: 'Close your eyes and take five deep breaths' },
                    { id: 'a2', action: 'Notice the sensation of your breath' },
                    { id: 'a3', action: 'Feel your body relaxing with each exhale' }
                ]
            }
        ]}
        audioFile={{
            id: 'audio1',
            remoteUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
        }}
        betweenThoughts={[
            { id: 'bt1', thought: 'Finding your center...' },
            { id: 'bt2', thought: 'Breathing in peace...' }
        ]}
        requiredActionViewTime={5}
        displayTimeBetweenThoughts={6}
        on:update:favorite={() => {}}
    />
</Story>

<!-- Multiple ideas sequence -->
<Story name="Multi-Part Reading" args={{
    ideas: [
        {
            id: '1',
            message: 'The first step in any mindfulness practice is developing awareness. Awareness is simply noticing what is happening in this moment without trying to change it.',
            quote: {
                id: 'q1',
                quote: 'Awareness is the greatest agent for change.',
                quote_author: 'Eckhart Tolle'
            },
            favorite: false,
            actionItems: [
                { id: 'a1', action: 'Notice your current thoughts' },
                { id: 'a2', action: 'Observe without judgment' }
            ]
        },
        {
            id: '2',
            message: 'Once we become aware, the next step is acceptance. This doesn\'t mean we like everything we notice, but rather that we acknowledge what is present without resistance.',
            quote: {
                id: 'q2',
                quote: 'What you resist persists.',
                quote_author: 'Carl Jung'
            },
            favorite: false,
            actionItems: [
                { id: 'a3', action: 'Accept what you\'re feeling right now' },
                { id: 'a4', action: 'Release any resistance' }
            ]
        },
        {
            id: '3',
            message: 'With awareness and acceptance, we can now choose our response. This is where true freedom lies - in the space between stimulus and response.',
            quote: {
                id: 'q3',
                quote: 'Between stimulus and response there is a space. In that space is our power to choose our response.',
                quote_author: 'Viktor Frankl'
            },
            favorite: false,
            actionItems: [
                { id: 'a5', action: 'Choose your next action mindfully' },
                { id: 'a6', action: 'Act from a place of awareness' }
            ]
        }
    ],
    betweenThoughts: [
        { id: 'bt1', thought: 'Moving to the next insight...' },
        { id: 'bt2', thought: 'Integrating this wisdom...' },
        { id: 'bt3', thought: 'Building on what you\'ve learned...' }
    ],
    requiredActionViewTime: 7,
    displayTimeBetweenThoughts: 5
}}>
    <Reading 
        ideas={[
            {
                id: '1',
                message: 'The first step in any mindfulness practice is developing awareness. Awareness is simply noticing what is happening in this moment without trying to change it.',
                quote: {
                    id: 'q1',
                    quote: 'Awareness is the greatest agent for change.',
                    quote_author: 'Eckhart Tolle'
                },
                favorite: false,
                actionItems: [
                    { id: 'a1', action: 'Notice your current thoughts' },
                    { id: 'a2', action: 'Observe without judgment' }
                ]
            },
            {
                id: '2',
                message: 'Once we become aware, the next step is acceptance. This doesn\'t mean we like everything we notice, but rather that we acknowledge what is present without resistance.',
                quote: {
                    id: 'q2',
                    quote: 'What you resist persists.',
                    quote_author: 'Carl Jung'
                },
                favorite: false,
                actionItems: [
                    { id: 'a3', action: 'Accept what you\'re feeling right now' },
                    { id: 'a4', action: 'Release any resistance' }
                ]
            },
            {
                id: '3',
                message: 'With awareness and acceptance, we can now choose our response. This is where true freedom lies - in the space between stimulus and response.',
                quote: {
                    id: 'q3',
                    quote: 'Between stimulus and response there is a space. In that space is our power to choose our response.',
                    quote_author: 'Viktor Frankl'
                },
                favorite: false,
                actionItems: [
                    { id: 'a5', action: 'Choose your next action mindfully' },
                    { id: 'a6', action: 'Act from a place of awareness' }
                ]
            }
        ]}
        betweenThoughts={[
            { id: 'bt1', thought: 'Moving to the next insight...' },
            { id: 'bt2', thought: 'Integrating this wisdom...' },
            { id: 'bt3', thought: 'Building on what you\'ve learned...' }
        ]}
        requiredActionViewTime={7}
        displayTimeBetweenThoughts={5}
        on:update:favorite={() => {}}
    />
</Story>

<!-- Simple message without quote -->
<Story name="Simple Message" args={{
    ideas: [
        {
            id: '1',
            message: 'You are exactly where you need to be in this moment. Trust the process of your life and know that every experience is contributing to your growth and wisdom.',
            favorite: false,
            actionItems: [
                { id: 'a1', action: 'Take a moment to appreciate where you are' },
                { id: 'a2', action: 'Trust in your journey' }
            ]
        }
    ],
    betweenThoughts: [
        { id: 'bt1', thought: 'Trusting the process...' }
    ],
    requiredActionViewTime: 3,
    displayTimeBetweenThoughts: 4
}}>
    <Reading 
        ideas={[
            {
                id: '1',
                message: 'You are exactly where you need to be in this moment. Trust the process of your life and know that every experience is contributing to your growth and wisdom.',
                favorite: false,
                actionItems: [
                    { id: 'a1', action: 'Take a moment to appreciate where you are' },
                    { id: 'a2', action: 'Trust in your journey' }
                ]
            }
        ]}
        betweenThoughts={[
            { id: 'bt1', thought: 'Trusting the process...' }
        ]}
        requiredActionViewTime={3}
        displayTimeBetweenThoughts={4}
        on:update:favorite={() => {}}
    />
</Story>

<!-- Quote-only content -->
<Story name="Quote Focus" args={{
    ideas: [
        {
            id: '1',
            quote: {
                id: 'q1',
                quote: 'The present moment is the only time over which we have dominion.',
                quote_author: 'Thích Nhất Hạnh'
            },
            favorite: false,
            actionItems: [
                { id: 'a1', action: 'Reflect on this quote for a moment' },
                { id: 'a2', action: 'Consider how it applies to your life' }
            ]
        }
    ],
    betweenThoughts: [
        { id: 'bt1', thought: 'Contemplating wisdom...' }
    ],
    requiredActionViewTime: 4,
    displayTimeBetweenThoughts: 6
}}>
    <Reading 
        ideas={[
            {
                id: '1',
                quote: {
                    id: 'q1',
                    quote: 'The present moment is the only time over which we have dominion.',
                    quote_author: 'Thích Nhất Hạnh'
                },
                favorite: false,
                actionItems: [
                    { id: 'a1', action: 'Reflect on this quote for a moment' },
                    { id: 'a2', action: 'Consider how it applies to your life' }
                ]
            }
        ]}
        betweenThoughts={[
            { id: 'bt1', thought: 'Contemplating wisdom...' }
        ]}
        requiredActionViewTime={4}
        displayTimeBetweenThoughts={6}
        on:update:favorite={() => {}}
    />
</Story>

<!-- Long-form content -->
<Story name="Extended Reading" args={{
    ideas: [
        {
            id: '1',
            message: 'Mindful living is not about perfection or achieving a constant state of zen. It\'s about bringing conscious awareness to the ordinary moments of our lives.\n\nWhen we wash dishes, we can feel the warm water and notice the sensation of cleaning. When we walk, we can feel our feet touching the ground. When we eat, we can taste and appreciate our food.\n\nThese simple acts of presence transform the mundane into the sacred. They remind us that life is not something that happens to us while we\'re busy making other plans - life is happening right now, in this very moment.\n\nThe invitation is simple: wherever you are, whatever you\'re doing, can you bring a little more awareness to this moment? Can you be here, now, with what is?',
            quote: {
                id: 'q1',
                quote: 'Life is what happens to you while you\'re busy making other plans.',
                quote_author: 'John Lennon'
            },
            favorite: false,
            actionItems: [
                { id: 'a1', action: 'Choose one daily activity to do mindfully today' },
                { id: 'a2', action: 'Notice when your mind wanders and gently return to the present' },
                { id: 'a3', action: 'Appreciate the simple moments throughout your day' }
            ]
        }
    ],
    betweenThoughts: [
        { id: 'bt1', thought: 'Integrating mindful awareness...' }
    ],
    requiredActionViewTime: 8,
    displayTimeBetweenThoughts: 7
}}>
    <Reading 
        ideas={[
            {
                id: '1',
                message: 'Mindful living is not about perfection or achieving a constant state of zen. It\'s about bringing conscious awareness to the ordinary moments of our lives.\n\nWhen we wash dishes, we can feel the warm water and notice the sensation of cleaning. When we walk, we can feel our feet touching the ground. When we eat, we can taste and appreciate our food.\n\nThese simple acts of presence transform the mundane into the sacred. They remind us that life is not something that happens to us while we\'re busy making other plans - life is happening right now, in this very moment.\n\nThe invitation is simple: wherever you are, whatever you\'re doing, can you bring a little more awareness to this moment? Can you be here, now, with what is?',
                quote: {
                    id: 'q1',
                    quote: 'Life is what happens to you while you\'re busy making other plans.',
                    quote_author: 'John Lennon'
                },
                favorite: false,
                actionItems: [
                    { id: 'a1', action: 'Choose one daily activity to do mindfully today' },
                    { id: 'a2', action: 'Notice when your mind wanders and gently return to the present' },
                    { id: 'a3', action: 'Appreciate the simple moments throughout your day' }
                ]
            }
        ]}
        betweenThoughts={[
            { id: 'bt1', thought: 'Integrating mindful awareness...' }
        ]}
        requiredActionViewTime={8}
        displayTimeBetweenThoughts={7}
        on:update:favorite={() => {}}
    />
</Story>

<!-- No actions available -->
<Story name="Reading Without Actions" args={{
    ideas: [
        {
            id: '1',
            message: 'Sometimes the most powerful practice is simply to pause and reflect. No actions needed, just presence.',
            quote: {
                id: 'q1',
                quote: 'In the depth of silence is the voice of God.',
                quote_author: 'Sai Baba'
            },
            favorite: false,
            actionItems: []
        }
    ],
    betweenThoughts: [
        { id: 'bt1', thought: 'Resting in silence...' }
    ],
    requiredActionViewTime: 5,
    displayTimeBetweenThoughts: 8
}}>
    <Reading 
        ideas={[
            {
                id: '1',
                message: 'Sometimes the most powerful practice is simply to pause and reflect. No actions needed, just presence.',
                quote: {
                    id: 'q1',
                    quote: 'In the depth of silence is the voice of God.',
                    quote_author: 'Sai Baba'
                },
                favorite: false,
                actionItems: []
            }
        ]}
        betweenThoughts={[
            { id: 'bt1', thought: 'Resting in silence...' }
        ]}
        requiredActionViewTime={5}
        displayTimeBetweenThoughts={8}
        on:update:favorite={() => {}}
    />
</Story>

<!-- With audio guide -->
<Story name="Guided Reading with Audio" args={{
    ideas: [
        {
            id: '1',
            message: 'Listen to the audio guide while reading along. This combination of auditory and visual input can deepen your understanding and engagement with the material.',
            quote: {
                id: 'q1',
                quote: 'The quieter you become, the more you are able to hear.',
                quote_author: 'Rumi'
            },
            favorite: false,
            actionItems: [
                { id: 'a1', action: 'Listen to the audio guide' },
                { id: 'a2', action: 'Follow along with the reading' },
                { id: 'a3', action: 'Notice how the combination affects your experience' }
            ]
        }
    ],
    audioFile: {
        id: 'audio1',
        remoteUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
    },
    betweenThoughts: [
        { id: 'bt1', thought: 'Listening deeply...' }
    ],
    requiredActionViewTime: 6,
    displayTimeBetweenThoughts: 5
}}>
    <Reading 
        ideas={[
            {
                id: '1',
                message: 'Listen to the audio guide while reading along. This combination of auditory and visual input can deepen your understanding and engagement with the material.',
                quote: {
                    id: 'q1',
                    quote: 'The quieter you become, the more you are able to hear.',
                    quote_author: 'Rumi'
                },
                favorite: false,
                actionItems: [
                    { id: 'a1', action: 'Listen to the audio guide' },
                    { id: 'a2', action: 'Follow along with the reading' },
                    { id: 'a3', action: 'Notice how the combination affects your experience' }
                ]
            }
        ]}
        audioFile={{
            id: 'audio1',
            remoteUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
        }}
        betweenThoughts={[
            { id: 'bt1', thought: 'Listening deeply...' }
        ]}
        requiredActionViewTime={6}
        displayTimeBetweenThoughts={5}
        on:update:favorite={() => {}}
    />
</Story>

<!-- Empty state -->
<Story name="No Content Available" args={{
    ideas: [],
    betweenThoughts: [],
    requiredActionViewTime: 5,
    displayTimeBetweenThoughts: 8
}}>
    <Reading 
        ideas={[]}
        betweenThoughts={[]}
        requiredActionViewTime={5}
        displayTimeBetweenThoughts={8}
        on:update:favorite={() => {}}
    />
</Story>

<!-- Feature demonstration -->
<Story name="Feature Demonstration">
    <div class="p-4 space-y-4">
        <h3 class="text-lg font-semibold">Reading Component Features</h3>
        <div class="space-y-2 text-sm text-muted-foreground">
            <p>• <strong>Progressive reading:</strong> Navigate through multiple ideas sequentially</p>
            <p>• <strong>Action integration:</strong> Each idea can have associated actions</p>
            <p>• <strong>Time gating:</strong> Must spend minimum time viewing actions before proceeding</p>
            <p>• <strong>Between thoughts:</strong> Transitional messages between ideas</p>
            <p>• <strong>Favorites:</strong> Mark ideas as favorites with heart button</p>
            <p>• <strong>Audio support:</strong> Optional audio guides for enhanced experience</p>
            <p>• <strong>Responsive design:</strong> Adapts to different screen sizes</p>
            <p>• <strong>Scroll management:</strong> Auto-scrolls to top when changing ideas</p>
        </div>
    </div>
    <Reading 
        ideas={[
            {
                id: 'demo1',
                message: 'This demonstrates all the key features of the reading component. Try favoriting this idea, viewing the actions, and navigating if there are multiple ideas.',
                quote: {
                    id: 'demo_quote',
                    quote: 'The best way to learn is by doing.',
                    quote_author: 'Anonymous'
                },
                favorite: false,
                actionItems: [
                    { id: 'demo_a1', action: 'Click the heart to favorite this idea' },
                    { id: 'demo_a2', action: 'Notice the time requirement before proceeding' },
                    { id: 'demo_a3', action: 'Explore all the interactive elements' }
                ]
            }
        ]}
        betweenThoughts={[
            { id: 'demo_bt1', thought: 'Exploring the features...' }
        ]}
        requiredActionViewTime={5}
        displayTimeBetweenThoughts={6}
        on:update:favorite={() => {}}
    />
</Story>

<!-- Real-world usage examples -->
<Story name="Morning Routine Reading" args={{
    ideas: [
        {
            id: 'morning1',
            message: 'Begin your day with purpose. Before the rush of daily activities takes over, take a moment to set a clear intention for how you want to show up in the world today.',
            quote: {
                id: 'morning_quote',
                quote: 'How we start our day determines how we live our day.',
                quote_author: 'Robin Sharma'
            },
            favorite: false,
            actionItems: [
                { id: 'morning_a1', action: 'Take three deep breaths to center yourself' },
                { id: 'morning_a2', action: 'Ask yourself: How do I want to feel today?' },
                { id: 'morning_a3', action: 'Set one clear intention for your day' }
            ]
        },
        {
            id: 'morning2',
            message: 'Gratitude shifts our perspective from what we lack to what we have. It\'s a simple practice that can transform our entire outlook on life.',
            quote: {
                id: 'gratitude_quote',
                quote: 'Gratitude turns what we have into enough.',
                quote_author: 'Anonymous'
            },
            favorite: false,
            actionItems: [
                { id: 'gratitude_a1', action: 'Think of three things you\'re grateful for' },
                { id: 'gratitude_a2', action: 'Feel the appreciation in your body' },
                { id: 'gratitude_a3', action: 'Carry this feeling with you into your day' }
            ]
        }
    ],
    betweenThoughts: [
        { id: 'morning_bt1', thought: 'Setting the tone for your day...' },
        { id: 'morning_bt2', thought: 'Cultivating appreciation...' }
    ],
    requiredActionViewTime: 6,
    displayTimeBetweenThoughts: 5
}}>
    <Reading 
        ideas={[
            {
                id: 'morning1',
                message: 'Begin your day with purpose. Before the rush of daily activities takes over, take a moment to set a clear intention for how you want to show up in the world today.',
                quote: {
                    id: 'morning_quote',
                    quote: 'How we start our day determines how we live our day.',
                    quote_author: 'Robin Sharma'
                },
                favorite: false,
                actionItems: [
                    { id: 'morning_a1', action: 'Take three deep breaths to center yourself' },
                    { id: 'morning_a2', action: 'Ask yourself: How do I want to feel today?' },
                    { id: 'morning_a3', action: 'Set one clear intention for your day' }
                ]
            },
            {
                id: 'morning2',
                message: 'Gratitude shifts our perspective from what we lack to what we have. It\'s a simple practice that can transform our entire outlook on life.',
                quote: {
                    id: 'gratitude_quote',
                    quote: 'Gratitude turns what we have into enough.',
                    quote_author: 'Anonymous'
                },
                favorite: false,
                actionItems: [
                    { id: 'gratitude_a1', action: 'Think of three things you\'re grateful for' },
                    { id: 'gratitude_a2', action: 'Feel the appreciation in your body' },
                    { id: 'gratitude_a3', action: 'Carry this feeling with you into your day' }
                ]
            }
        ]}
        betweenThoughts={[
            { id: 'morning_bt1', thought: 'Setting the tone for your day...' },
            { id: 'morning_bt2', thought: 'Cultivating appreciation...' }
        ]}
        requiredActionViewTime={6}
        displayTimeBetweenThoughts={5}
        on:update:favorite={() => {}}
    />
</Story>