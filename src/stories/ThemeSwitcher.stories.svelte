<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import ThemeSwitcher from '../lib/components/ThemeSwitcher.svelte';

    const { Story } = defineMeta({
        title: 'Components/ThemeSwitcher',
        component: ThemeSwitcher,
        tags: ['autodocs'],
        argTypes: {},
        args: {}
    });
</script>

<!-- Basic theme switcher -->
<Story name="Default Theme Switcher">
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Theme Switcher</h3>
        <p class="text-sm text-muted-foreground mb-4">
            Select a color theme to customize the application appearance.
        </p>
        <ThemeSwitcher />
    </div>
</Story>

<!-- In different contexts -->
<Story name="In Header Context">
    <div class="border border-border rounded-lg overflow-hidden">
        <div class="flex items-center justify-between p-4 bg-background border-b border-border">
            <h1 class="text-xl font-semibold text-foreground">Application Settings</h1>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-muted-foreground">Theme:</span>
                <ThemeSwitcher />
            </div>
        </div>
        <div class="p-4 bg-muted/20">
            <p class="text-sm text-muted-foreground">
                The theme switcher in the header allows quick access to theme changes.
            </p>
        </div>
    </div>
</Story>

<Story name="In Settings Panel">
    <div class="max-w-md mx-auto p-6 bg-background border border-border rounded-lg shadow-sm">
        <h2 class="text-lg font-semibold text-foreground mb-6">Appearance Settings</h2>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <label for="dark-mode-toggle" class="text-sm font-medium text-foreground">Dark Mode</label>
                    <p class="text-xs text-muted-foreground">Toggle between light and dark themes</p>
                </div>
                <input id="dark-mode-toggle" type="checkbox" class="rounded" />
            </div>
      
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-sm font-medium text-foreground">Color Theme</span>
                    <p class="text-xs text-muted-foreground">Choose your preferred color scheme</p>
                </div>
                <ThemeSwitcher />
            </div>
      
            <div class="flex items-center justify-between">
                <div>
                    <label for="font-size-select" class="text-sm font-medium text-foreground">Font Size</label>
                    <p class="text-xs text-muted-foreground">Adjust text size for readability</p>
                </div>
                <select id="font-size-select" class="bg-background border border-input rounded-md px-3 py-1 text-sm">
                    <option>Small</option>
                    <option>Medium</option>
                    <option>Large</option>
                </select>
            </div>
        </div>
    </div>
</Story>

<Story name="In Toolbar">
    <div class="flex items-center justify-between p-3 bg-background border border-border rounded-lg">
        <div class="flex items-center space-x-2">
            <button class="p-2 hover:bg-accent rounded-md" aria-label="Add item">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>
            <button class="p-2 hover:bg-accent rounded-md" aria-label="Edit item">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
            </button>
        </div>
        <div class="flex items-center space-x-3">
            <span class="text-xs text-muted-foreground">Theme:</span>
            <ThemeSwitcher />
        </div>
    </div>
</Story>

<!-- Theme preview showcase -->
<Story name="Theme Preview">
    <div class="p-4 space-y-6">
        <h3 class="text-lg font-semibold">Theme Preview</h3>
        <p class="text-sm text-muted-foreground">
            The theme switcher includes a color indicator that shows the current theme's primary color.
        </p>
    
        <div class="space-y-4">
            <div class="flex items-center space-x-4">
                <span class="text-sm font-medium w-32">Current Theme:</span>
                <ThemeSwitcher />
            </div>
      
            <div class="p-4 bg-muted/20 rounded-lg">
                <h4 class="font-medium mb-2">Theme Colors Preview</h4>
                <div class="grid grid-cols-4 gap-3">
                    <div class="text-center">
                        <div class="w-8 h-8 bg-primary rounded-full mx-auto mb-1"></div>
                        <span class="text-xs text-muted-foreground">Primary</span>
                    </div>
                    <div class="text-center">
                        <div class="w-8 h-8 bg-secondary rounded-full mx-auto mb-1"></div>
                        <span class="text-xs text-muted-foreground">Secondary</span>
                    </div>
                    <div class="text-center">
                        <div class="w-8 h-8 bg-accent rounded-full mx-auto mb-1"></div>
                        <span class="text-xs text-muted-foreground">Accent</span>
                    </div>
                    <div class="text-center">
                        <div class="w-8 h-8 bg-muted rounded-full mx-auto mb-1"></div>
                        <span class="text-xs text-muted-foreground">Muted</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</Story>

<!-- Multiple theme switchers -->
<Story name="Multiple Theme Switchers">
    <div class="space-y-6">
        <div class="text-sm text-muted-foreground">
            Multiple theme switchers will all sync together since they use the same store:
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="p-4 bg-background border border-border rounded-lg">
                <h3 class="font-medium text-foreground mb-3">Header Switcher</h3>
                <ThemeSwitcher />
            </div>
            <div class="p-4 bg-background border border-border rounded-lg">
                <h3 class="font-medium text-foreground mb-3">Sidebar Switcher</h3>
                <ThemeSwitcher />
            </div>
            <div class="p-4 bg-background border border-border rounded-lg">
                <h3 class="font-medium text-foreground mb-3">Footer Switcher</h3>
                <ThemeSwitcher />
            </div>
        </div>
    </div>
</Story>

<!-- Accessibility demonstration -->
<Story name="Accessibility Features">
    <div class="space-y-4">
        <div class="text-sm text-muted-foreground">
            The theme switcher includes proper accessibility features:
        </div>
        <div class="p-4 bg-background border border-border rounded-lg">
            <ul class="text-sm space-y-2 text-foreground">
                <li>• <strong>aria-label:</strong> "Select color theme"</li>
                <li>• <strong>Keyboard accessible:</strong> Can be navigated with arrow keys</li>
                <li>• <strong>Screen reader friendly:</strong> Proper option labels</li>
                <li>• <strong>Focus visible:</strong> Shows focus ring when navigated with keyboard</li>
                <li>• <strong>Visual indicator:</strong> Color preview dot shows current theme</li>
            </ul>
        </div>
        <div class="flex items-center space-x-4">
            <span class="text-sm text-foreground">Try it:</span>
            <ThemeSwitcher />
        </div>
    </div>
</Story>

<!-- Different layouts -->
<Story name="Compact Layout">
    <div class="max-w-sm mx-auto p-4 bg-background border border-border rounded-lg">
        <div class="flex items-center justify-between mb-4">
            <h3 class="font-medium">Quick Settings</h3>
            <ThemeSwitcher />
        </div>
        <div class="space-y-3">
            <div class="flex items-center justify-between">
                <label for="notifications-toggle" class="text-sm">Notifications</label>
                <input id="notifications-toggle" type="checkbox" class="rounded" />
            </div>
            <div class="flex items-center justify-between">
                <label for="autosave-toggle" class="text-sm">Auto-save</label>
                <input id="autosave-toggle" type="checkbox" class="rounded" checked />
            </div>
        </div>
    </div>
</Story>

<Story name="Full Width Layout">
    <div class="w-full p-6 bg-background border border-border rounded-lg">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h2 class="text-xl font-semibold">Customization</h2>
                <p class="text-sm text-muted-foreground">Personalize your experience</p>
            </div>
            <ThemeSwitcher />
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="p-4 bg-muted/20 rounded-lg">
                <h3 class="font-medium mb-2">Appearance</h3>
                <p class="text-sm text-muted-foreground">Customize colors and themes</p>
            </div>
            <div class="p-4 bg-muted/20 rounded-lg">
                <h3 class="font-medium mb-2">Layout</h3>
                <p class="text-sm text-muted-foreground">Adjust interface layout</p>
            </div>
            <div class="p-4 bg-muted/20 rounded-lg">
                <h3 class="font-medium mb-2">Behavior</h3>
                <p class="text-sm text-muted-foreground">Configure app behavior</p>
            </div>
        </div>
    </div>
</Story>

<!-- Real-world usage examples -->
<Story name="User Profile Settings">
    <div class="max-w-2xl mx-auto">
        <div class="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
            <div class="p-6 border-b border-border">
                <h2 class="text-xl font-semibold">Profile Settings</h2>
                <p class="text-sm text-muted-foreground">Manage your account and preferences</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- Personal Information -->
                <div>
                    <h3 class="font-medium mb-4">Personal Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="first-name" class="block text-sm font-medium mb-1">First Name</label>
                            <input id="first-name" type="text" value="John" class="w-full p-2 border border-border rounded" />
                        </div>
                        <div>
                            <label for="last-name" class="block text-sm font-medium mb-1">Last Name</label>
                            <input id="last-name" type="text" value="Doe" class="w-full p-2 border border-border rounded" />
                        </div>
                    </div>
                </div>
        
                <!-- Preferences -->
                <div>
                    <h3 class="font-medium mb-4">Preferences</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="email-notifications" class="text-sm font-medium">Email Notifications</label>
                                <p class="text-xs text-muted-foreground">Receive updates via email</p>
                            </div>
                            <input id="email-notifications" type="checkbox" class="rounded" checked />
                        </div>
            
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-medium">Color Theme</span>
                                <p class="text-xs text-muted-foreground">Choose your preferred color scheme</p>
                            </div>
                            <ThemeSwitcher />
                        </div>
            
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="language-select" class="text-sm font-medium">Language</label>
                                <p class="text-xs text-muted-foreground">Select your language</p>
                            </div>
                            <select id="language-select" class="bg-background border border-input rounded-md px-3 py-2 text-sm min-w-[140px]">
                                <option>English</option>
                                <option>Spanish</option>
                                <option>French</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</Story>

<Story name="Dashboard Customization">
    <div class="p-4">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h2 class="text-2xl font-bold">Dashboard</h2>
                <p class="text-muted-foreground">Welcome back, John!</p>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-muted-foreground">Theme:</span>
                <ThemeSwitcher />
            </div>
        </div>
    
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="p-6 bg-background border border-border rounded-lg">
                <h3 class="font-semibold mb-2">Quick Stats</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-muted-foreground">Total Sessions</span>
                        <span class="font-medium">24</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-muted-foreground">This Week</span>
                        <span class="font-medium">5</span>
                    </div>
                </div>
            </div>
      
            <div class="p-6 bg-background border border-border rounded-lg">
                <h3 class="font-semibold mb-2">Recent Activity</h3>
                <div class="space-y-2">
                    <div class="text-sm">Morning meditation</div>
                    <div class="text-sm">Breathing exercise</div>
                    <div class="text-sm">Evening reflection</div>
                </div>
            </div>
      
            <div class="p-6 bg-background border border-border rounded-lg">
                <h3 class="font-semibold mb-2">Goals</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-muted-foreground">Daily Goal</span>
                        <span class="font-medium">3/5</span>
                    </div>
                    <div class="w-full bg-muted rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 60%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</Story>

<!-- Theme switching demonstration -->
<Story name="Theme Switching Demo">
    <div class="space-y-6">
        <div class="text-center">
            <h3 class="text-lg font-semibold mb-2">Interactive Theme Demo</h3>
            <p class="text-sm text-muted-foreground mb-4">
                Change the theme below to see how it affects the entire interface
            </p>
            <ThemeSwitcher />
        </div>
    
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div class="p-4 bg-primary text-primary-foreground rounded-lg">
                    <h4 class="font-semibold">Primary Color</h4>
                    <p class="text-sm opacity-90">This shows the primary theme color</p>
                </div>
                <div class="p-4 bg-secondary text-secondary-foreground rounded-lg">
                    <h4 class="font-semibold">Secondary Color</h4>
                    <p class="text-sm opacity-90">This shows the secondary theme color</p>
                </div>
            </div>
      
            <div class="space-y-4">
                <div class="p-4 bg-accent text-accent-foreground rounded-lg">
                    <h4 class="font-semibold">Accent Color</h4>
                    <p class="text-sm opacity-90">This shows the accent theme color</p>
                </div>
                <div class="p-4 bg-muted text-muted-foreground rounded-lg">
                    <h4 class="font-semibold">Muted Color</h4>
                    <p class="text-sm opacity-90">This shows the muted theme color</p>
                </div>
            </div>
        </div>
    </div>
</Story>