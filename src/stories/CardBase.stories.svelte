<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';

    const { Story } = defineMeta({
        title: 'Components/CardBase',
        component: CardBase,
        tags: ['autodocs'],
        parameters: {
            docs: {
                description: {
                    component: `
# CardBase Component

A flexible card component that can be used for displaying content with optional features. Automatically integrates with the application's theme system.

## Features
- **Title and Content**: Required props for the main card content
- **Icon Slot**: Optional named slot for displaying icons using Lucide Svelte or custom components
- **Count Badge**: Optional count display in the top-right corner (supports numbers and strings)
- **Clickable**: Optional destination prop makes the entire card clickable
- **Theme Integration**: Automatically adapts to theme store changes via CSS custom properties
- **Styling**: Supports custom CSS classes via the class prop

## Props
- \`title\` (string, required): The main heading text
- \`content\` (string, required): The description or body text
- \`count\` (number | string | undefined): Optional badge value
- \`destination\` (string | undefined): Optional URL for clickable cards
- \`iconName\` (string | undefined): Optional name of the Lucide icon to display (e.g., "Rocket", "Star")
- \`iconColor\` (string | undefined): Optional color for the icon (e.g., "red", "#FF0000", "text-blue-500")
- \`iconHeight\` (string | undefined): Optional height for the icon in pixels (e.g., "24", "32")
- \`class\` (string): Additional CSS classes

## Store Integration
The CardBase component automatically responds to theme changes through CSS custom properties:
- Uses \`--primary\`, \`--background\`, \`--foreground\`, \`--muted-foreground\`, etc.
- No direct store subscription needed - styling updates automatically
- Works with the theme store (\`currentTheme\`, \`darkMode\`) out of the box
- Parent components can use stores to provide dynamic content to props

## Usage Notes
- When \`destination\` is provided, the card becomes clickable and shows hover effects
- Icons are now passed via the \`iconName\` prop, which accepts the name of a Lucide icon.
- \`iconColor\` and \`iconHeight\` props can be used to customize the icon's appearance.
- Count badges automatically position in the top-right corner
- Cards without destinations are static display elements
- Theme changes are applied automatically without component re-rendering
          `
                }
            }
        },
        argTypes: {
            title: {
                control: 'text',
                description: 'Card title text',
            },
            content: {
                control: 'text',
                description: 'Card content/description text',
            },
            count: {
                control: 'text',
                description: 'Optional count badge (number or string)',
            },
            destination: {
                control: 'text',
                description: 'Optional URL to make card clickable',
            },
            class: {
                control: 'text',
                description: 'Additional CSS classes to apply to the card',
            },
            iconName: {
                control: 'text',
                description: 'Name of the Lucide icon (e.g., "Rocket", "Star")',
            },
            iconColor: {
                control: 'color',
                description: 'Color of the icon (e.g., "red", "#FF0000")',
            },
            iconHeight: {
                control: 'text',
                description: 'Height of the icon in pixels (e.g., "24", "32")',
            },
        },
        args: {
            title: 'Sample Card',
            content: 'This is a sample card description that shows how the content appears.',
        }
    });
</script>

<script>
    import CardBase from '../lib/components/CardBase.svelte';
// Icons are now handled via Lucide Svelte through the iconName prop
</script>

<!-- Basic card examples -->
<Story name="Basic Card" args={{ 
    title: 'Basic Card', 
    content: 'This is a basic card without any additional features.' 
}}>
    <CardBase 
        title="Basic Card" 
        content="This is a basic card without any additional features." 
    />
</Story>

<Story name="Card with Icon" args={{
    title: 'Card with Icon',
    content: 'This card includes an icon to enhance visual appeal.',
    iconName: 'Rocket'
}}>
    <CardBase
        title="Card with Icon"
        content="This card includes an icon to enhance visual appeal."
        iconName="Rocket"
    />
</Story>

<Story name="Card with Count Badge" args={{ 
    title: 'Card with Count', 
    content: 'This card shows a count badge in the top-right corner.',
    count: 5
}}>
    <CardBase 
        title="Card with Count" 
        content="This card shows a count badge in the top-right corner."
        count={5}
    />
</Story>

<Story name="Clickable Card" args={{ 
    title: 'Clickable Card', 
    content: 'This card is clickable and will navigate to a destination.',
    destination: '/example'
}}>
    <CardBase 
        title="Clickable Card" 
        content="This card is clickable and will navigate to a destination."
        destination="/example"
    />
</Story>

<!-- Complete feature examples -->
<Story name="Full Featured Card" args={{
    title: 'Full Featured Card',
    content: 'This card demonstrates all available features: icon, count badge, and clickable destination.',
    count: 12,
    destination: '/full-example'
}}>
    <CardBase
        title="Full Featured Card"
        content="This card demonstrates all available features: icon, count badge, and clickable destination."
        count={12}
        destination="/full-example"
    />
</Story>

<!-- Different content lengths -->
<Story name="Short Content" args={{ 
    title: 'Short', 
    content: 'Brief description.'
}}>
    <CardBase 
        title="Short" 
        content="Brief description."
    />
</Story>

<Story name="Long Content" args={{ 
    title: 'Long Content Card', 
    content: 'This card contains a much longer description to demonstrate how the component handles extended text content. The text should wrap naturally and maintain good readability while preserving the card\'s visual structure and spacing.'
}}>
    <CardBase 
        title="Long Content Card" 
        content="This card contains a much longer description to demonstrate how the component handles extended text content. The text should wrap naturally and maintain good readability while preserving the card's visual structure and spacing."
    />
</Story>

<!-- Different count types -->
<Story name="String Count" args={{ 
    title: 'String Count', 
    content: 'This card uses a string value for the count badge.',
    count: 'NEW'
}}>
    <CardBase 
        title="String Count" 
        content="This card uses a string value for the count badge."
        count="NEW"
    />
</Story>

<Story name="Large Number Count" args={{ 
    title: 'Large Count', 
    content: 'This card shows how larger numbers appear in the count badge.',
    count: 999
}}>
    <CardBase 
        title="Large Count" 
        content="This card shows how larger numbers appear in the count badge."
        count={999}
    />
</Story>

<!-- Use case examples -->
<Story name="Navigation Card" args={{
    title: 'Settings',
    content: 'Configure your application preferences and account settings.',
    destination: '/settings'
}}>
    <CardBase
        title="Settings"
        content="Configure your application preferences and account settings."
        destination="/settings"
    />
</Story>

<Story name="Notification Card" args={{
    title: 'Notifications',
    content: 'View your recent notifications and alerts.',
    count: 3,
    destination: '/notifications'
}}>
    <CardBase
        title="Notifications"
        content="View your recent notifications and alerts."
        count={3}
        destination="/notifications"
    />
</Story>

<Story name="Info Card" args={{
    title: 'Information',
    content: 'Important information about your account or recent updates.'
}}>
    <CardBase
        title="Information"
        content="Important information about your account or recent updates."
    />
</Story>

<!-- Hover states demonstration -->
<Story name="Interactive States">
    <div class="space-y-4">
        <div class="text-sm text-muted-foreground mb-4">
            Hover over the clickable cards to see the interactive states:
        </div>
        <CardBase 
            title="Non-clickable Card" 
            content="This card is not clickable and shows the default state."
        />
        <CardBase 
            title="Clickable Card" 
            content="This card is clickable - hover to see the interactive state."
            destination="/example"
        />
        <CardBase
            title="Featured Clickable Card"
            content="This featured card combines all elements with hover effects."
            count={5}
            destination="/featured"
        />
    </div>
</Story>