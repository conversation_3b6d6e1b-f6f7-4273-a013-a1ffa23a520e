import type { Meta, StoryObj } from '@storybook/svelte';
import HoldProgress from '$lib/components/FreeYourself/HoldProgress.svelte';

const meta = {
    title: 'Components/FreeYourself/HoldProgress',
    component: HoldProgress,
    parameters: {
        docs: {
            description: {
                component: 'Progress indicator for the hold-to-continue interaction with circular progress visualization.'
            }
        }
    },
    argTypes: {
        isHolding: {
            description: 'Whether the user is currently holding',
            control: 'boolean'
        },
        holdProgress: {
            description: 'Progress percentage (0-100)',
            control: { type: 'range', min: 0, max: 100, step: 5 }
        },
        isFading: {
            description: 'Whether the component is fading out',
            control: 'boolean'
        },
        releaseMessage: {
            description: 'Message to show when progress reaches 100%',
            control: 'text'
        }
    }
} satisfies Meta<HoldProgress>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Idle: Story = {
    args: {
        isHolding: false,
        holdProgress: 0,
        isFading: false,
        releaseMessage: 'Release to continue!'
    },
    parameters: {
        docs: {
            description: {
                story: 'Initial state showing the "Press & hold for more" prompt.'
            }
        }
    }
};

export const HoldingInProgress: Story = {
    args: {
        isHolding: true,
        holdProgress: 40,
        isFading: false,
        releaseMessage: 'Release to continue!'
    },
    parameters: {
        docs: {
            description: {
                story: 'User is holding with 40% progress.'
            }
        }
    }
};

export const AlmostComplete: Story = {
    args: {
        isHolding: true,
        holdProgress: 80,
        isFading: false,
        releaseMessage: 'Release to continue!'
    },
    parameters: {
        docs: {
            description: {
                story: 'Progress is at 80%, countdown shows 1 second remaining.'
            }
        }
    }
};

export const Complete: Story = {
    args: {
        isHolding: true,
        holdProgress: 100,
        isFading: false,
        releaseMessage: 'Release the past!'
    },
    parameters: {
        docs: {
            description: {
                story: 'Progress complete, showing the release message with bounce animation.'
            }
        }
    }
};

export const Fading: Story = {
    args: {
        isHolding: false,
        holdProgress: 100,
        isFading: true,
        releaseMessage: 'Let go of worry!'
    },
    parameters: {
        docs: {
            description: {
                story: 'Component fading out after completion.'
            }
        }
    }
};

export const AnimatedProgress: Story = {
    args: {
        isHolding: true,
        holdProgress: 0,
        isFading: false,
        releaseMessage: 'Embrace this moment!'
    },
    render: (args) => ({
        Component: HoldProgress,
        props: args
    }),
    play: async ({ args, updateArgs }) => {
        // Simulate progress animation
        for (let i = 0; i <= 100; i += 5) {
            await new Promise(resolve => setTimeout(resolve, 250));
            updateArgs({ ...args, holdProgress: i });
        }
    },
    parameters: {
        docs: {
            description: {
                story: 'Animated demonstration of the progress indicator from 0% to 100%.'
            }
        }
    }
};