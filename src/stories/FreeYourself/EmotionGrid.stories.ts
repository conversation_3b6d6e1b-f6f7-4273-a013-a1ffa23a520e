import type { Meta, StoryObj } from '@storybook/svelte';
import EmotionGrid from '$lib/components/FreeYourself/EmotionGrid.svelte';
import { emotions } from '$lib/components/FreeYourself/emotionsData';

const meta = {
    title: 'Components/FreeYourself/EmotionGrid',
    component: EmotionGrid,
    parameters: {
        docs: {
            description: {
                component: 'Grid component for displaying emotion selection options.'
            }
        }
    },
    argTypes: {
        emotions: {
            description: 'Array of emotion options to display',
            control: 'object'
        },
        onSelect: {
            description: 'Callback function when an emotion is selected',
            action: 'emotion-selected'
        }
    }
} satisfies Meta<EmotionGrid>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        emotions: emotions,
        onSelect: (emotion: string) => console.log('Selected:', emotion)
    }
};

export const SingleColumn: Story = {
    args: {
        emotions: emotions.slice(0, 2),
        onSelect: (emotion: string) => console.log('Selected:', emotion)
    },
    parameters: {
        docs: {
            description: {
                story: 'Emotion grid with only two items for single column display.'
            }
        }
    }
};

export const CustomEmotions: Story = {
    args: {
        emotions: [
            {
                label: 'Joy',
                emoji: '😊',
                description: 'Celebrate and amplify positive moments',
                responses: []
            },
            {
                label: 'Gratitude',
                emoji: '🙏',
                description: 'Focus on appreciation and abundance',
                responses: []
            },
            {
                label: 'Peace',
                emoji: '🕊️',
                description: 'Find calm in the present moment',
                responses: []
            }
        ],
        onSelect: (emotion: string) => console.log('Selected:', emotion)
    },
    parameters: {
        docs: {
            description: {
                story: 'Emotion grid with custom positive emotions.'
            }
        }
    }
};