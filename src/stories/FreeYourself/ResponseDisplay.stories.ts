import type { Meta, StoryObj } from '@storybook/svelte';
import ResponseDisplay from '$lib/components/FreeYourself/ResponseDisplay.svelte';
import type { Response } from '$lib/components/FreeYourself/emotionsData';

const meta = {
    title: 'Components/FreeYourself/ResponseDisplay',
    component: ResponseDisplay,
    parameters: {
        layout: 'fullscreen',
        docs: {
            description: {
                component: 'Full-screen response display component that shows when users select an emotion.'
            }
        }
    },
    argTypes: {
        currentResponse: {
            description: 'The current response object to display',
            control: 'object'
        },
        isHolding: {
            description: 'Whether the user is currently holding',
            control: 'boolean'
        },
        holdProgress: {
            description: 'Progress percentage (0-100)',
            control: { type: 'range', min: 0, max: 100, step: 5 }
        },
        isFading: {
            description: 'Whether the component is fading out',
            control: 'boolean'
        },
        releaseMessage: {
            description: 'Message to show when progress reaches 100%',
            control: 'text'
        },
        onMouseDown: {
            description: 'Handler for mouse down/touch start',
            action: 'mouse-down'
        },
        onMouseUp: {
            description: 'Handler for mouse up/touch end',
            action: 'mouse-up'
        },
        onReset: {
            description: 'Handler for reset/choose another button',
            action: 'reset'
        }
    }
} satisfies Meta<ResponseDisplay>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleThought: Response = {
    responseType: 'thought',
    content: 'Anger often comes from unmet expectations or feeling powerless. Take a deep breath. You have the power to choose your response.'
};

const sampleAffirmation: Response = {
    responseType: 'affirmation',
    content: 'I choose peace over being right. My inner calm is stronger than any external storm.'
};

const sampleAction: Response = {
    responseType: 'action',
    content: 'Write down three things you\'re grateful for right now. Then take 5 deep breaths, releasing tension with each exhale.'
};

const sampleQuestion: Response = {
    responseType: 'question',
    content: 'What would it feel like to let go of this anger right now? What might become possible if you released this energy?'
};

export const ThoughtResponse: Story = {
    args: {
        currentResponse: sampleThought,
        isHolding: false,
        holdProgress: 0,
        isFading: false,
        releaseMessage: 'Release the past!',
        onMouseDown: () => console.log('Started holding'),
        onMouseUp: () => console.log('Released'),
        onReset: () => console.log('Reset')
    },
    parameters: {
        docs: {
            description: {
                story: 'Display showing a thought-type response.'
            }
        }
    }
};

export const AffirmationResponse: Story = {
    args: {
        currentResponse: sampleAffirmation,
        isHolding: false,
        holdProgress: 0,
        isFading: false,
        releaseMessage: 'Let go of worry!',
        onMouseDown: () => console.log('Started holding'),
        onMouseUp: () => console.log('Released'),
        onReset: () => console.log('Reset')
    },
    parameters: {
        docs: {
            description: {
                story: 'Display showing an affirmation-type response.'
            }
        }
    }
};

export const ActionResponse: Story = {
    args: {
        currentResponse: sampleAction,
        isHolding: false,
        holdProgress: 0,
        isFading: false,
        releaseMessage: 'Embrace this moment!',
        onMouseDown: () => console.log('Started holding'),
        onMouseUp: () => console.log('Released'),
        onReset: () => console.log('Reset')
    },
    parameters: {
        docs: {
            description: {
                story: 'Display showing an action-type response.'
            }
        }
    }
};

export const QuestionResponse: Story = {
    args: {
        currentResponse: sampleQuestion,
        isHolding: false,
        holdProgress: 0,
        isFading: false,
        releaseMessage: 'Let go of painful focus!',
        onMouseDown: () => console.log('Started holding'),
        onMouseUp: () => console.log('Released'),
        onReset: () => console.log('Reset')
    },
    parameters: {
        docs: {
            description: {
                story: 'Display showing a question-type response.'
            }
        }
    }
};

export const HoldingState: Story = {
    args: {
        currentResponse: sampleThought,
        isHolding: true,
        holdProgress: 60,
        isFading: false,
        releaseMessage: 'Release the past!',
        onMouseDown: () => console.log('Started holding'),
        onMouseUp: () => console.log('Released'),
        onReset: () => console.log('Reset')
    },
    parameters: {
        docs: {
            description: {
                story: 'User is holding to see another response, progress at 60%.'
            }
        }
    }
};

export const CompleteHold: Story = {
    args: {
        currentResponse: sampleAffirmation,
        isHolding: true,
        holdProgress: 100,
        isFading: false,
        releaseMessage: 'Release the past!',
        onMouseDown: () => console.log('Started holding'),
        onMouseUp: () => console.log('Released'),
        onReset: () => console.log('Reset')
    },
    parameters: {
        docs: {
            description: {
                story: 'Hold complete, showing release message.'
            }
        }
    }
};

export const FadingTransition: Story = {
    args: {
        currentResponse: sampleAction,
        isHolding: false,
        holdProgress: 100,
        isFading: true,
        releaseMessage: 'Let go of worry!',
        onMouseDown: () => console.log('Started holding'),
        onMouseUp: () => console.log('Released'),
        onReset: () => console.log('Reset')
    },
    parameters: {
        docs: {
            description: {
                story: 'Component in fading transition state.'
            }
        }
    }
};

export const InteractiveDemo: Story = {
    args: {
        currentResponse: sampleThought,
        isHolding: false,
        holdProgress: 0,
        isFading: false,
        releaseMessage: 'Release the past!',
        onMouseDown: () => console.log('Started holding'),
        onMouseUp: () => console.log('Released'),
        onReset: () => console.log('Reset')
    },
    render: (args) => {
        let isHolding = false;
        let holdProgress = 0;
        let animationFrame: number | null = null;
        let startTime: number | null = null;

        const startHold = () => {
            isHolding = true;
            holdProgress = 0;
            startTime = Date.now();
            
            const animate = () => {
                if (!isHolding) return;
                
                const elapsed = Date.now() - (startTime || 0);
                holdProgress = Math.min((elapsed / 5000) * 100, 100);
                
                // Update the component props
                args.holdProgress = holdProgress;
                args.isHolding = isHolding;
                
                if (holdProgress < 100) {
                    animationFrame = requestAnimationFrame(animate);
                }
            };
            
            animate();
        };

        const endHold = () => {
            isHolding = false;
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }
            args.isHolding = false;
        };

        return {
            Component: ResponseDisplay,
            props: {
                ...args,
                onMouseDown: startHold,
                onMouseUp: endHold
            }
        };
    },
    parameters: {
        docs: {
            description: {
                story: 'Interactive demo - click and hold to see the progress animation.'
            }
        }
    }
};