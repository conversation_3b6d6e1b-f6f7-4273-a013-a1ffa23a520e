import type { Meta, StoryObj } from '@storybook/svelte';
import ResponseHeader from '$lib/components/FreeYourself/ResponseHeader.svelte';

const meta = {
    title: 'Components/FreeYourself/ResponseHeader',
    component: ResponseHeader,
    parameters: {
        docs: {
            description: {
                component: 'Header component that displays the response type with an emoji and label.'
            }
        }
    },
    argTypes: {
        responseType: {
            description: 'Type of response to display',
            control: 'select',
            options: ['thought', 'affirmation', 'action', 'question']
        }
    }
} satisfies Meta<ResponseHeader>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Thought: Story = {
    args: {
        responseType: 'thought'
    },
    parameters: {
        docs: {
            description: {
                story: 'Response header showing a thought type.'
            }
        }
    }
};

export const Affirmation: Story = {
    args: {
        responseType: 'affirmation'
    },
    parameters: {
        docs: {
            description: {
                story: 'Response header showing an affirmation type.'
            }
        }
    }
};

export const Action: Story = {
    args: {
        responseType: 'action'
    },
    parameters: {
        docs: {
            description: {
                story: 'Response header showing an action type.'
            }
        }
    }
};

export const Question: Story = {
    args: {
        responseType: 'question'
    },
    parameters: {
        docs: {
            description: {
                story: 'Response header showing a question type.'
            }
        }
    }
};

export const AllTypes: Story = {
    render: () => ({
        Component: ResponseHeader,
        props: {}
    }),
    decorators: [
        () => ({
            template: `
                <div class="space-y-4">
                    <div class="p-4 bg-gray-100 rounded">
                        <ResponseHeader responseType="thought" />
                    </div>
                    <div class="p-4 bg-gray-100 rounded">
                        <ResponseHeader responseType="affirmation" />
                    </div>
                    <div class="p-4 bg-gray-100 rounded">
                        <ResponseHeader responseType="action" />
                    </div>
                    <div class="p-4 bg-gray-100 rounded">
                        <ResponseHeader responseType="question" />
                    </div>
                </div>
            `
        })
    ],
    parameters: {
        docs: {
            description: {
                story: 'All response header types displayed together for comparison.'
            }
        }
    }
};