<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import NavTabBar from '../lib/components/NavTabBar.svelte';

    const { Story } = defineMeta({
        title: 'Components/NavTabBar',
        component: NavTabBar,
        tags: ['autodocs'],
        argTypes: {},
        args: {}
    });
</script>

<!-- Basic navigation bar -->
<Story name="Default Navigation">
    <div class="border border-border rounded-lg overflow-hidden">
        <div class="h-96 bg-muted/20 flex items-center justify-center">
            <p class="text-muted-foreground">Main content area</p>
        </div>
        <NavTabBar />
    </div>
</Story>

<!-- Mobile app layout -->
<Story name="Mobile App Layout">
    <div class="max-w-sm mx-auto border border-border rounded-lg overflow-hidden shadow-lg">
        <!-- Status bar mockup -->
        <div class="h-6 bg-background border-b border-border flex items-center justify-between px-4 text-xs">
            <span>9:41</span>
            <span>100%</span>
        </div>
    
        <!-- Main content -->
        <div class="h-96 bg-background flex flex-col">
            <div class="flex-1 p-4">
                <h1 class="text-xl font-bold mb-4">Your Best Days</h1>
                <div class="space-y-4">
                    <div class="p-4 bg-muted/20 rounded-lg">
                        <h3 class="font-semibold mb-2">Today's Focus</h3>
                        <p class="text-sm text-muted-foreground">
                            Take a moment to breathe and center yourself.
                        </p>
                    </div>
                    <div class="p-4 bg-muted/20 rounded-lg">
                        <h3 class="font-semibold mb-2">Quick Actions</h3>
                        <div class="space-y-2">
                            <button class="w-full p-2 bg-primary text-primary-foreground rounded">
                                Start Meditation
                            </button>
                            <button class="w-full p-2 bg-secondary text-secondary-foreground rounded">
                                Breathing Exercise
                            </button>
                        </div>
                    </div>
                </div>
            </div>
      
            <!-- Navigation bar -->
            <NavTabBar />
        </div>
    </div>
</Story>

<!-- Different page states -->
<Story name="YBD Page Active">
    <div class="max-w-sm mx-auto border border-border rounded-lg overflow-hidden">
        <div class="h-80 bg-background p-4">
            <h2 class="text-lg font-semibold mb-4">Your Best Days</h2>
            <div class="space-y-3">
                <div class="p-3 bg-primary/10 rounded-lg">
                    <h3 class="font-medium text-primary">Daily Reflection</h3>
                    <p class="text-sm text-muted-foreground">How are you feeling today?</p>
                </div>
                <div class="p-3 bg-muted/20 rounded-lg">
                    <h3 class="font-medium">Mindfulness Practice</h3>
                    <p class="text-sm text-muted-foreground">5-minute breathing exercise</p>
                </div>
            </div>
        </div>
        <NavTabBar />
    </div>
</Story>

<Story name="Relief Page Active">
    <div class="max-w-sm mx-auto border border-border rounded-lg overflow-hidden">
        <div class="h-80 bg-background p-4">
            <h2 class="text-lg font-semibold mb-4">Relief</h2>
            <div class="space-y-3">
                <div class="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                    <h3 class="font-medium text-orange-700 dark:text-orange-300">Quick Relief</h3>
                    <p class="text-sm text-muted-foreground">Immediate stress relief techniques</p>
                </div>
                <div class="p-3 bg-muted/20 rounded-lg">
                    <h3 class="font-medium">Emergency Contacts</h3>
                    <p class="text-sm text-muted-foreground">Crisis support resources</p>
                </div>
            </div>
        </div>
        <NavTabBar />
    </div>
</Story>

<Story name="More Page Active">
    <div class="max-w-sm mx-auto border border-border rounded-lg overflow-hidden">
        <div class="h-80 bg-background p-4">
            <h2 class="text-lg font-semibold mb-4">More</h2>
            <div class="space-y-3">
                <div class="p-3 bg-muted/20 rounded-lg">
                    <h3 class="font-medium">Settings</h3>
                    <p class="text-sm text-muted-foreground">App preferences and configuration</p>
                </div>
                <div class="p-3 bg-muted/20 rounded-lg">
                    <h3 class="font-medium">About</h3>
                    <p class="text-sm text-muted-foreground">Learn more about the app</p>
                </div>
                <div class="p-3 bg-muted/20 rounded-lg">
                    <h3 class="font-medium">Contact</h3>
                    <p class="text-sm text-muted-foreground">Get in touch with support</p>
                </div>
            </div>
        </div>
        <NavTabBar />
    </div>
</Story>

<!-- Different screen sizes -->
<Story name="Tablet Layout">
    <div class="max-w-md mx-auto border border-border rounded-lg overflow-hidden">
        <div class="h-96 bg-background p-6">
            <h1 class="text-2xl font-bold mb-6">Tablet View</h1>
            <div class="grid grid-cols-2 gap-4">
                <div class="p-4 bg-muted/20 rounded-lg">
                    <h3 class="font-semibold mb-2">Morning Routine</h3>
                    <p class="text-sm text-muted-foreground">
                        Start your day with intention
                    </p>
                </div>
                <div class="p-4 bg-muted/20 rounded-lg">
                    <h3 class="font-semibold mb-2">Evening Reflection</h3>
                    <p class="text-sm text-muted-foreground">
                        Wind down and reflect
                    </p>
                </div>
                <div class="p-4 bg-muted/20 rounded-lg">
                    <h3 class="font-semibold mb-2">Quick Meditation</h3>
                    <p class="text-sm text-muted-foreground">
                        5-minute mindfulness break
                    </p>
                </div>
                <div class="p-4 bg-muted/20 rounded-lg">
                    <h3 class="font-semibold mb-2">Progress Tracking</h3>
                    <p class="text-sm text-muted-foreground">
                        See your journey
                    </p>
                </div>
            </div>
        </div>
        <NavTabBar />
    </div>
</Story>

<!-- Navigation behavior demonstration -->
<Story name="Navigation States">
    <div class="space-y-6">
        <div class="text-sm text-muted-foreground">
            The navigation bar shows different states based on the current page:
        </div>
    
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- YBD active -->
            <div class="border border-border rounded-lg overflow-hidden">
                <div class="p-3 bg-background border-b border-border">
                    <h4 class="font-medium text-center">YBD Active</h4>
                </div>
                <div class="h-32 bg-muted/20"></div>
                <NavTabBar />
            </div>
      
            <!-- Relief active -->
            <div class="border border-border rounded-lg overflow-hidden">
                <div class="p-3 bg-background border-b border-border">
                    <h4 class="font-medium text-center">Relief Active</h4>
                </div>
                <div class="h-32 bg-muted/20"></div>
                <NavTabBar />
            </div>
      
            <!-- More active -->
            <div class="border border-border rounded-lg overflow-hidden">
                <div class="p-3 bg-background border-b border-border">
                    <h4 class="font-medium text-center">More Active</h4>
                </div>
                <div class="h-32 bg-muted/20"></div>
                <NavTabBar />
            </div>
        </div>
    </div>
</Story>

<!-- Icon showcase -->
<Story name="Icon Showcase">
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">Navigation Icons</h3>
        <div class="space-y-4">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div class="p-4 bg-muted/20 rounded-lg">
                    <div class="flex justify-center mb-2">
                        <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17,8C8,10 5.9,16.17 3.82,21.34L5.71,22L6.66,19.7C7.14,19.87 7.64,20 8,20C19,20 22,3 22,3C21,5 14,5.25 9,6.25C4,7.25 2,11.5 2,13.5C2,15.5 3.75,17.25 3.75,17.25C7,8 17,8 17,8Z"/>
                        </svg>
                    </div>
                    <h4 class="font-medium">YBD</h4>
                    <p class="text-xs text-muted-foreground">Leaf icon</p>
                </div>
        
                <div class="p-4 bg-muted/20 rounded-lg">
                    <div class="flex justify-center mb-2">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h4 class="font-medium">Relief</h4>
                    <p class="text-xs text-muted-foreground">Alert circle</p>
                </div>
        
                <div class="p-4 bg-muted/20 rounded-lg">
                    <div class="flex justify-center mb-2">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </div>
                    <h4 class="font-medium">More</h4>
                    <p class="text-xs text-muted-foreground">Settings cog</p>
                </div>
            </div>
        </div>
    </div>
</Story>

<!-- Accessibility features -->
<Story name="Accessibility Features">
    <div class="max-w-md mx-auto">
        <div class="border border-border rounded-lg overflow-hidden">
            <div class="p-4 bg-background">
                <h3 class="font-semibold mb-4">Accessibility Features</h3>
                <ul class="text-sm space-y-2 text-muted-foreground">
                    <li>• <strong>Semantic navigation:</strong> Uses proper nav element</li>
                    <li>• <strong>Keyboard accessible:</strong> All links are focusable</li>
                    <li>• <strong>Screen reader friendly:</strong> Clear labels and structure</li>
                    <li>• <strong>Visual feedback:</strong> Active states and hover effects</li>
                    <li>• <strong>Touch friendly:</strong> Large tap targets for mobile</li>
                </ul>
            </div>
            <NavTabBar />
        </div>
    </div>
</Story>

<!-- Real-world usage -->
<Story name="Complete App Interface">
    <div class="max-w-sm mx-auto border border-border rounded-lg overflow-hidden shadow-xl">
        <!-- Header -->
        <div class="h-12 bg-primary text-primary-foreground flex items-center justify-center">
            <h1 class="font-semibold">Your Best Days</h1>
        </div>
    
        <!-- Main content -->
        <div class="h-96 bg-background overflow-y-auto">
            <div class="p-4 space-y-4">
                <!-- Welcome section -->
                <div class="text-center py-6">
                    <div class="w-20 h-20 bg-primary/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-10 h-10 text-primary" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17,8C8,10 5.9,16.17 3.82,21.34L5.71,22L6.66,19.7C7.14,19.87 7.64,20 8,20C19,20 22,3 22,3C21,5 14,5.25 9,6.25C4,7.25 2,11.5 2,13.5C2,15.5 3.75,17.25 3.75,17.25C7,8 17,8 17,8Z"/>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold mb-2">Welcome Back!</h2>
                    <p class="text-muted-foreground">Ready to make today your best day?</p>
                </div>
        
                <!-- Quick actions -->
                <div class="space-y-3">
                    <button class="w-full p-4 bg-primary text-primary-foreground rounded-lg text-left">
                        <h3 class="font-semibold">Start Daily Practice</h3>
                        <p class="text-sm opacity-90">5-minute mindfulness session</p>
                    </button>
          
                    <button class="w-full p-4 bg-secondary text-secondary-foreground rounded-lg text-left">
                        <h3 class="font-semibold">Breathing Exercise</h3>
                        <p class="text-sm opacity-90">Quick stress relief technique</p>
                    </button>
          
                    <button class="w-full p-4 bg-muted text-muted-foreground rounded-lg text-left">
                        <h3 class="font-semibold">View Progress</h3>
                        <p class="text-sm opacity-90">See your mindfulness journey</p>
                    </button>
                </div>
        
                <!-- Recent activity -->
                <div class="pt-4">
                    <h3 class="font-semibold mb-3">Recent Activity</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                            <span class="text-sm">Morning meditation</span>
                            <span class="text-xs text-muted-foreground">Today</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                            <span class="text-sm">Breathing exercise</span>
                            <span class="text-xs text-muted-foreground">Yesterday</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Navigation -->
        <NavTabBar />
    </div>
</Story>

<!-- Responsive behavior -->
<Story name="Responsive Behavior">
    <div class="space-y-6">
        <div class="text-sm text-muted-foreground">
            The navigation bar adapts to different screen sizes:
        </div>
    
        <!-- Mobile -->
        <div>
            <h4 class="font-medium mb-2">Mobile (320px)</h4>
            <div class="w-80 border border-border rounded-lg overflow-hidden">
                <div class="h-32 bg-muted/20"></div>
                <NavTabBar />
            </div>
        </div>
    
        <!-- Tablet -->
        <div>
            <h4 class="font-medium mb-2">Tablet (768px)</h4>
            <div class="w-full max-w-md border border-border rounded-lg overflow-hidden">
                <div class="h-32 bg-muted/20"></div>
                <NavTabBar />
            </div>
        </div>
    </div>
</Story>