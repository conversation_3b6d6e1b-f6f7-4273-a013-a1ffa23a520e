<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import DarkModeToggle from '../lib/components/DarkModeToggle.svelte';
    import { fn } from 'storybook/test';

    const { Story } = defineMeta({
        title: 'Components/DarkModeToggle',
        component: DarkModeToggle,
        tags: ['autodocs'],
        argTypes: {},
        args: {
            onclick: fn(),
        }
    });
</script>

<!-- Basic toggle -->
<Story name="Default Toggle">
    <DarkModeToggle />
</Story>

<!-- In different contexts -->
<Story name="In Header Context">
    <div class="flex items-center justify-between p-4 bg-background border-b border-border">
        <h1 class="text-xl font-semibold text-foreground">Application Header</h1>
        <div class="flex items-center space-x-4">
            <span class="text-sm text-muted-foreground">Theme:</span>
            <DarkModeToggle />
        </div>
    </div>
</Story>

<Story name="In Navigation Bar">
    <nav class="flex items-center justify-between p-4 bg-background shadow-sm">
        <div class="flex items-center space-x-6">
            <div class="text-lg font-bold text-foreground">Logo</div>
            <div class="hidden md:flex space-x-4">
                <a href="/" class="text-foreground hover:text-primary">Home</a>
                <a href="/about" class="text-foreground hover:text-primary">About</a>
                <a href="/contact" class="text-foreground hover:text-primary">Contact</a>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <button class="text-foreground hover:text-primary">Profile</button>
            <DarkModeToggle />
        </div>
    </nav>
</Story>

<Story name="In Settings Panel">
    <div class="max-w-md mx-auto p-6 bg-background border border-border rounded-lg shadow-sm">
        <h2 class="text-lg font-semibold text-foreground mb-4">Settings</h2>
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <div>
                    <label for="notifications-toggle" class="text-sm font-medium text-foreground">Notifications</label>
                    <p class="text-xs text-muted-foreground">Receive email notifications</p>
                </div>
                <input id="notifications-toggle" type="checkbox" class="rounded" />
            </div>
            <div class="flex items-center justify-between">
                <div>
                    <label for="dark-mode-setting" class="text-sm font-medium text-foreground">Dark Mode</label>
                    <p class="text-xs text-muted-foreground">Toggle between light and dark themes</p>
                </div>
                <DarkModeToggle />
            </div>
            <div class="flex items-center justify-between">
                <div>
                    <label for="auto-save-toggle" class="text-sm font-medium text-foreground">Auto-save</label>
                    <p class="text-xs text-muted-foreground">Automatically save changes</p>
                </div>
                <input id="auto-save-toggle" type="checkbox" class="rounded" checked />
            </div>
        </div>
    </div>
</Story>

<Story name="In Toolbar">
    <div class="flex items-center justify-between p-3 bg-background border border-border rounded-lg">
        <div class="flex items-center space-x-2">
            <button class="p-2 hover:bg-accent rounded-md" aria-label="Add item">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>
            <button class="p-2 hover:bg-accent rounded-md" aria-label="Edit item">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
            </button>
            <button class="p-2 hover:bg-accent rounded-md" aria-label="Delete item">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>
        <div class="flex items-center space-x-2">
            <span class="text-xs text-muted-foreground">Theme</span>
            <DarkModeToggle />
        </div>
    </div>
</Story>

<!-- Multiple toggles demonstration -->
<Story name="Multiple Toggles">
    <div class="space-y-6">
        <div class="text-sm text-muted-foreground">
            Multiple dark mode toggles will all sync together since they use the same store:
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="p-4 bg-background border border-border rounded-lg">
                <h3 class="font-medium text-foreground mb-2">Header Toggle</h3>
                <DarkModeToggle />
            </div>
            <div class="p-4 bg-background border border-border rounded-lg">
                <h3 class="font-medium text-foreground mb-2">Sidebar Toggle</h3>
                <DarkModeToggle />
            </div>
            <div class="p-4 bg-background border border-border rounded-lg">
                <h3 class="font-medium text-foreground mb-2">Footer Toggle</h3>
                <DarkModeToggle />
            </div>
        </div>
    </div>
</Story>

<!-- Accessibility demonstration -->
<Story name="Accessibility Features">
    <div class="space-y-4">
        <div class="text-sm text-muted-foreground">
            The toggle includes proper accessibility features:
        </div>
        <div class="p-4 bg-background border border-border rounded-lg">
            <ul class="text-sm space-y-2 text-foreground">
                <li>• <strong>aria-label:</strong> "Toggle dark mode"</li>
                <li>• <strong>title:</strong> Tooltip on hover</li>
                <li>• <strong>Keyboard accessible:</strong> Can be activated with Enter/Space</li>
                <li>• <strong>Focus visible:</strong> Shows focus ring when navigated with keyboard</li>
                <li>• <strong>Visual feedback:</strong> Icon changes based on current theme</li>
            </ul>
        </div>
        <div class="flex items-center space-x-4">
            <span class="text-sm text-foreground">Try it:</span>
            <DarkModeToggle />
        </div>
    </div>
</Story>

<!-- Size variations -->
<Story name="Custom Styling">
    <div class="space-y-6">
        <div class="text-sm text-muted-foreground">
            The toggle can be customized with additional CSS classes:
        </div>
    
        <!-- Default size -->
        <div class="flex items-center space-x-4">
            <span class="text-sm text-foreground w-20">Default:</span>
            <DarkModeToggle />
        </div>
    
        <!-- Custom styled versions -->
        <div class="flex items-center space-x-4">
            <span class="text-sm text-foreground w-20">Compact:</span>
            <div class="scale-75">
                <DarkModeToggle />
            </div>
        </div>
    
        <div class="flex items-center space-x-4">
            <span class="text-sm text-foreground w-20">Large:</span>
            <div class="scale-125">
                <DarkModeToggle />
            </div>
        </div>
    </div>
</Story>

<!-- Usage examples -->
<Story name="Real-world Usage Examples">
    <div class="space-y-8">
        <!-- Mobile app header -->
        <div class="bg-background border border-border rounded-lg overflow-hidden">
            <div class="p-4 border-b border-border">
                <h3 class="font-medium text-foreground">Mobile App Header</h3>
            </div>
            <div class="p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary rounded-full"></div>
                        <span class="font-medium text-foreground">My App</span>
                    </div>
                    <DarkModeToggle />
                </div>
            </div>
        </div>

        <!-- Dashboard widget -->
        <div class="bg-background border border-border rounded-lg overflow-hidden">
            <div class="p-4 border-b border-border">
                <h3 class="font-medium text-foreground">Dashboard Widget</h3>
            </div>
            <div class="p-4">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-semibold text-foreground">User Preferences</h4>
                    <DarkModeToggle />
                </div>
                <div class="text-sm text-muted-foreground">
                    Customize your experience with theme preferences.
                </div>
            </div>
        </div>

        <!-- Floating action -->
        <div class="bg-background border border-border rounded-lg overflow-hidden">
            <div class="p-4 border-b border-border">
                <h3 class="font-medium text-foreground">Floating Controls</h3>
            </div>
            <div class="p-4 relative h-32">
                <div class="absolute bottom-4 right-4">
                    <DarkModeToggle />
                </div>
                <div class="text-sm text-muted-foreground">
                    Content area with floating theme toggle in bottom-right corner.
                </div>
            </div>
        </div>
    </div>
</Story>