<script lang="ts">
    import type { ComponentType } from 'svelte';
    export let IconComponent: ComponentType;
    export let label: string;
    export let size: string = '24px';
    export let color: string = 'currentColor';
</script>

<div style="display: flex; flex-direction: column; align-items: center; margin: 10px; border: 1px solid #eee; padding: 10px; border-radius: 5px;">
    <div style="width: {size}; height: {size}; color: {color};">
        <svelte:component this={IconComponent} />
    </div>
    <p style="margin-top: 5px; font-size: 0.8em; color: #555;">{label}</p>
</div>

