<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import Action from '../lib/components/YBD/Action.svelte';

    const { Story } = defineMeta({
        title: 'Components/YBD/Action',
        component: Action,
        tags: ['autodocs'],
        argTypes: {
            actions: {
                control: 'object',
                description: 'Array of YBD actions to display',
            },
            betweenActionsDisplayTime: {
                control: { type: 'range', min: 1, max: 10, step: 0.5 },
                description: 'Time to display between-action thoughts in seconds',
            },
        },
        args: {
            actions: [
                {
                    id: '1',
                    action: 'Take three deep breaths and notice how your body feels.',
                    category: 'breathing'
                },
                {
                    id: '2',
                    action: 'Look around and name five things you can see.',
                    category: 'grounding'
                },
                {
                    id: '3',
                    action: 'Place your hand on your heart and feel it beating.',
                    category: 'mindfulness'
                }
            ],
            betweenActionsDisplayTime: 3.0,
        }
    });
</script>

<!-- Basic action modal -->
<Story name="Basic Action Modal" args={{
    actions: [
        {
            id: '1',
            action: 'Take three deep breaths and notice how your body feels.'
        },
        {
            id: '2',
            action: 'Look around and name five things you can see.'
        },
        {
            id: '3',
            action: 'Place your hand on your heart and feel it beating.'
        }
    ],
    betweenActionsDisplayTime: 3.0
}}>
    <div class="p-4">
        <p class="text-sm text-muted-foreground mb-4">
            Click the button below to open the action modal:
        </p>
        <button class="px-4 py-2 bg-primary text-primary-foreground rounded">
            Open Actions
        </button>
    </div>
    <Action 
        actions={[
            {
                id: '1',
                action: 'Take three deep breaths and notice how your body feels.'
            },
            {
                id: '2',
                action: 'Look around and name five things you can see.'
            },
            {
                id: '3',
                action: 'Place your hand on your heart and feel it beating.'
            }
        ]}
        betweenActionsDisplayTime={3.0}
        on:close={() => {}}
    />
</Story>

<!-- Single action -->
<Story name="Single Action" args={{
    actions: [
        {
            id: '1',
            action: 'Take a moment to breathe deeply and center yourself.',
            category: 'breathing'
        }
    ],
    betweenActionsDisplayTime: 3.0
}}>
    <Action 
        actions={[
            {
                id: '1',
                action: 'Take a moment to breathe deeply and center yourself.'
            }
        ]}
        betweenActionsDisplayTime={3.0}
        on:close={() => {}}
    />
</Story>

<!-- Breathing exercises -->
<Story name="Breathing Exercises" args={{
    actions: [
        {
            id: 'breath1',
            action: 'Inhale slowly through your nose for 4 counts.',
            category: 'breathing'
        },
        {
            id: 'breath2',
            action: 'Hold your breath gently for 4 counts.',
            category: 'breathing'
        },
        {
            id: 'breath3',
            action: 'Exhale slowly through your mouth for 6 counts.',
            category: 'breathing'
        },
        {
            id: 'breath4',
            action: 'Repeat this cycle three more times.',
            category: 'breathing'
        }
    ],
    betweenActionsDisplayTime: 2.0
}}>
    <Action 
        actions={[
            {
                id: 'breath1',
                action: 'Inhale slowly through your nose for 4 counts.'
            },
            {
                id: 'breath2',
                action: 'Hold your breath gently for 4 counts.'
            },
            {
                id: 'breath3',
                action: 'Exhale slowly through your mouth for 6 counts.'
            },
            {
                id: 'breath4',
                action: 'Repeat this cycle three more times.'
            }
        ]}
        betweenActionsDisplayTime={2.0}
        on:close={() => {}}
    />
</Story>

<!-- Grounding exercises -->
<Story name="Grounding Exercises" args={{
    actions: [
        {
            id: 'ground1',
            action: 'Name 5 things you can see around you.',
            category: 'grounding'
        },
        {
            id: 'ground2',
            action: 'Name 4 things you can touch.',
            category: 'grounding'
        },
        {
            id: 'ground3',
            action: 'Name 3 things you can hear.',
            category: 'grounding'
        },
        {
            id: 'ground4',
            action: 'Name 2 things you can smell.',
            category: 'grounding'
        },
        {
            id: 'ground5',
            action: 'Name 1 thing you can taste.',
            category: 'grounding'
        }
    ],
    betweenActionsDisplayTime: 4.0
}}>
    <Action 
        actions={[
            {
                id: 'ground1',
                action: 'Name 5 things you can see around you.'
            },
            {
                id: 'ground2',
                action: 'Name 4 things you can touch.'
            },
            {
                id: 'ground3',
                action: 'Name 3 things you can hear.'
            },
            {
                id: 'ground4',
                action: 'Name 2 things you can smell.'
            },
            {
                id: 'ground5',
                action: 'Name 1 thing you can taste.'
            }
        ]}
        betweenActionsDisplayTime={4.0}
        on:close={() => {}}
    />
</Story>

<!-- Mindfulness exercises -->
<Story name="Mindfulness Exercises" args={{
    actions: [
        {
            id: 'mind1',
            action: 'Place your hand on your heart and feel it beating.',
            category: 'mindfulness'
        },
        {
            id: 'mind2',
            action: 'Notice the temperature of the air on your skin.',
            category: 'mindfulness'
        },
        {
            id: 'mind3',
            action: 'Feel your feet connected to the ground.',
            category: 'mindfulness'
        },
        {
            id: 'mind4',
            action: 'Observe your thoughts without judgment.',
            category: 'mindfulness'
        },
        {
            id: 'mind5',
            action: 'Send yourself a message of kindness.',
            category: 'mindfulness'
        }
    ],
    betweenActionsDisplayTime: 3.5
}}>
    <Action 
        actions={[
            {
                id: 'mind1',
                action: 'Place your hand on your heart and feel it beating.'
            },
            {
                id: 'mind2',
                action: 'Notice the temperature of the air on your skin.'
            },
            {
                id: 'mind3',
                action: 'Feel your feet connected to the ground.'
            },
            {
                id: 'mind4',
                action: 'Observe your thoughts without judgment.'
            },
            {
                id: 'mind5',
                action: 'Send yourself a message of kindness.'
            }
        ]}
        betweenActionsDisplayTime={3.5}
        on:close={() => {}}
    />
</Story>

<!-- Quick relief actions -->
<Story name="Quick Relief Actions" args={{
    actions: [
        {
            id: 'relief1',
            action: 'Splash cold water on your face.',
            category: 'relief'
        },
        {
            id: 'relief2',
            action: 'Step outside and feel the fresh air.',
            category: 'relief'
        },
        {
            id: 'relief3',
            action: 'Do 10 jumping jacks to release tension.',
            category: 'relief'
        },
        {
            id: 'relief4',
            action: 'Call someone you trust.',
            category: 'relief'
        }
    ],
    betweenActionsDisplayTime: 2.5
}}>
    <Action 
        actions={[
            {
                id: 'relief1',
                action: 'Splash cold water on your face.'
            },
            {
                id: 'relief2',
                action: 'Step outside and feel the fresh air.'
            },
            {
                id: 'relief3',
                action: 'Do 10 jumping jacks to release tension.'
            },
            {
                id: 'relief4',
                action: 'Call someone you trust.'
            }
        ]}
        betweenActionsDisplayTime={2.5}
        on:close={() => {}}
    />
</Story>

<!-- Long action text -->
<Story name="Long Action Text" args={{
    actions: [
        {
            id: 'long1',
            action: 'Find a comfortable position, either sitting or lying down. Close your eyes gently and begin to notice your natural breathing pattern without trying to change it.',
            category: 'meditation'
        },
        {
            id: 'long2',
            action: 'Slowly scan your body from the top of your head down to your toes, noticing any areas of tension or discomfort without trying to fix or change anything.',
            category: 'meditation'
        },
        {
            id: 'long3',
            action: 'Bring your attention to your heart center and imagine breathing in and out through your heart, allowing feelings of gratitude and compassion to arise naturally.',
            category: 'meditation'
        }
    ],
    betweenActionsDisplayTime: 4.0
}}>
    <Action 
        actions={[
            {
                id: 'long1',
                action: 'Find a comfortable position, either sitting or lying down. Close your eyes gently and begin to notice your natural breathing pattern without trying to change it.'
            },
            {
                id: 'long2',
                action: 'Slowly scan your body from the top of your head down to your toes, noticing any areas of tension or discomfort without trying to fix or change anything.'
            },
            {
                id: 'long3',
                action: 'Bring your attention to your heart center and imagine breathing in and out through your heart, allowing feelings of gratitude and compassion to arise naturally.'
            }
        ]}
        betweenActionsDisplayTime={4.0}
        on:close={() => {}}
    />
</Story>

<!-- Fast between-action timing -->
<Story name="Fast Between Actions" args={{
    actions: [
        {
            id: 'fast1',
            action: 'Take a quick deep breath.',
            category: 'breathing'
        },
        {
            id: 'fast2',
            action: 'Stretch your arms above your head.',
            category: 'movement'
        },
        {
            id: 'fast3',
            action: 'Smile and relax your face.',
            category: 'mindfulness'
        }
    ],
    betweenActionsDisplayTime: 1.0
}}>
    <Action 
        actions={[
            {
                id: 'fast1',
                action: 'Take a quick deep breath.'
            },
            {
                id: 'fast2',
                action: 'Stretch your arms above your head.'
            },
            {
                id: 'fast3',
                action: 'Smile and relax your face.'
            }
        ]}
        betweenActionsDisplayTime={1.0}
        on:close={() => {}}
    />
</Story>

<!-- Slow between-action timing -->
<Story name="Slow Between Actions" args={{
    actions: [
        {
            id: 'slow1',
            action: 'Sit quietly and observe your surroundings.',
            category: 'mindfulness'
        },
        {
            id: 'slow2',
            action: 'Notice the sounds around you without labeling them.',
            category: 'mindfulness'
        },
        {
            id: 'slow3',
            action: 'Feel gratitude for this moment of peace.',
            category: 'mindfulness'
        }
    ],
    betweenActionsDisplayTime: 6.0
}}>
    <Action 
        actions={[
            {
                id: 'slow1',
                action: 'Sit quietly and observe your surroundings.'
            },
            {
                id: 'slow2',
                action: 'Notice the sounds around you without labeling them.'
            },
            {
                id: 'slow3',
                action: 'Feel gratitude for this moment of peace.'
            }
        ]}
        betweenActionsDisplayTime={6.0}
        on:close={() => {}}
    />
</Story>

<!-- Empty actions array -->
<Story name="No Actions Available" args={{
    actions: [],
    betweenActionsDisplayTime: 3.0
}}>
    <Action 
        actions={[]}
        betweenActionsDisplayTime={3.0}
        on:close={() => {}}
    />
</Story>

<!-- Feature demonstration -->
<Story name="Feature Demonstration">
    <div class="p-4 space-y-4">
        <h3 class="text-lg font-semibold">Action Modal Features</h3>
        <div class="space-y-2 text-sm text-muted-foreground">
            <p>• <strong>Star button:</strong> Mark actions as favorites</p>
            <p>• <strong>Checkmark button:</strong> Mark actions as completed (shows celebration animation)</p>
            <p>• <strong>How button:</strong> Get additional guidance (placeholder)</p>
            <p>• <strong>Navigation:</strong> Previous/Next buttons to move between actions</p>
            <p>• <strong>Between thoughts:</strong> Transitional messages between actions</p>
            <p>• <strong>Progress indicator:</strong> Shows current position (e.g., "2 / 5")</p>
            <p>• <strong>Modal overlay:</strong> Full-screen focus on current action</p>
        </div>
        <Action 
            actions={[
                {
                    id: 'demo1',
                    action: 'Try clicking the star to favorite this action.'
                },
                {
                    id: 'demo2',
                    action: 'Try clicking the checkmark to complete this action.'
                },
                {
                    id: 'demo3',
                    action: 'Use the navigation buttons to move between actions.'
                }
            ]}
            betweenActionsDisplayTime={3.0}
            on:close={() => {}}
        />
    </div>
</Story>

<!-- Real-world usage examples -->
<Story name="Morning Routine" args={{
    actions: [
        {
            id: 'morning1',
            action: 'Before getting out of bed, take three deep breaths.',
            category: 'breathing'
        },
        {
            id: 'morning2',
            action: 'Set an intention for your day.',
            category: 'mindfulness'
        },
        {
            id: 'morning3',
            action: 'Stretch your body gently.',
            category: 'movement'
        },
        {
            id: 'morning4',
            action: 'Drink a glass of water mindfully.',
            category: 'mindfulness'
        },
        {
            id: 'morning5',
            action: 'Write down three things you\'re grateful for.',
            category: 'gratitude'
        }
    ],
    betweenActionsDisplayTime: 3.0
}}>
    <Action 
        actions={[
            {
                id: 'morning1',
                action: 'Before getting out of bed, take three deep breaths.'
            },
            {
                id: 'morning2',
                action: 'Set an intention for your day.'
            },
            {
                id: 'morning3',
                action: 'Stretch your body gently.'
            },
            {
                id: 'morning4',
                action: 'Drink a glass of water mindfully.'
            },
            {
                id: 'morning5',
                action: 'Write down three things you\'re grateful for.'
            }
        ]}
        betweenActionsDisplayTime={3.0}
        on:close={() => {}}
    />
</Story>

<Story name="Stress Relief Sequence" args={{
    actions: [
        {
            id: 'stress1',
            action: 'Stop what you\'re doing and pause.',
            category: 'mindfulness'
        },
        {
            id: 'stress2',
            action: 'Take five slow, deep breaths.',
            category: 'breathing'
        },
        {
            id: 'stress3',
            action: 'Relax your shoulders and jaw.',
            category: 'relaxation'
        },
        {
            id: 'stress4',
            action: 'Ask yourself: "What do I need right now?"',
            category: 'reflection'
        },
        {
            id: 'stress5',
            action: 'Take one small step toward what you need.',
            category: 'action'
        }
    ],
    betweenActionsDisplayTime: 2.5
}}>
    <Action 
        actions={[
            {
                id: 'stress1',
                action: 'Stop what you\'re doing and pause.'
            },
            {
                id: 'stress2',
                action: 'Take five slow, deep breaths.'
            },
            {
                id: 'stress3',
                action: 'Relax your shoulders and jaw.'
            },
            {
                id: 'stress4',
                action: 'Ask yourself: "What do I need right now?"'
            },
            {
                id: 'stress5',
                action: 'Take one small step toward what you need.'
            }
        ]}
        betweenActionsDisplayTime={2.5}
        on:close={() => {}}
    />
</Story>