<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import BetweenThought from '../lib/components/YBD/BetweenThought.svelte';

    const { Story } = defineMeta({
        title: 'Components/YBD/BetweenThought',
        component: BetweenThought,
        tags: ['autodocs'],
        argTypes: {
            thoughts: {
                control: 'object',
                description: 'Array of random thoughts to display',
            },
            displayTime: {
                control: { type: 'range', min: 1, max: 15, step: 0.5 },
                description: 'Time to display the thought in seconds',
            },
        },
        args: {
            thoughts: [
                { id: '1', thought: 'Taking a deep breath...' },
                { id: '2', thought: 'Finding your center...' },
                { id: '3', thought: "You're doing great..." }
            ],
            displayTime: 8.0,
        }
    });
</script>

<!-- Basic between thought -->
<Story name="Basic Between Thought" args={{
    thoughts: [
        { id: '1', thought: 'Taking a deep breath...' },
        { id: '2', thought: 'Finding your center...' },
        { id: '3', thought: "You're doing great..." }
    ],
    displayTime: 8.0
}}>
    <div class="p-4">
        <p class="text-sm text-muted-foreground mb-4">
            This modal appears between actions to provide transitional thoughts:
        </p>
    </div>
    <BetweenThought 
        thoughts={[
            { id: '1', thought: 'Taking a deep breath...' },
            { id: '2', thought: 'Finding your center...' },
            { id: '3', thought: "You're doing great..." }
        ]}
        displayTime={8.0}
        on:done={() => {}}
    />
</Story>

<!-- Short display time -->
<Story name="Quick Transition" args={{
    thoughts: [
        { id: '1', thought: 'Moving forward...' },
        { id: '2', thought: 'Keep going...' },
        { id: '3', thought: 'Almost there...' }
    ],
    displayTime: 3.0
}}>
    <BetweenThought 
        thoughts={[
            { id: '1', thought: 'Moving forward...' },
            { id: '2', thought: 'Keep going...' },
            { id: '3', thought: 'Almost there...' }
        ]}
        displayTime={3.0}
        on:done={() => {}}
    />
</Story>

<!-- Long display time -->
<Story name="Extended Reflection" args={{
    thoughts: [
        { id: '1', thought: 'Take your time to reflect on this moment...' },
        { id: '2', thought: 'Allow yourself to feel whatever comes up...' },
        { id: '3', thought: 'There is no rush, breathe at your own pace...' }
    ],
    displayTime: 12.0
}}>
    <BetweenThought 
        thoughts={[
            { id: '1', thought: 'Take your time to reflect on this moment...' },
            { id: '2', thought: 'Allow yourself to feel whatever comes up...' },
            { id: '3', thought: 'There is no rush, breathe at your own pace...' }
        ]}
        displayTime={12.0}
        on:done={() => {}}
    />
</Story>

<!-- Breathing-focused thoughts -->
<Story name="Breathing Thoughts" args={{
    thoughts: [
        { id: 'breath1', thought: 'Breathe in peace...' },
        { id: 'breath2', thought: 'Breathe out tension...' },
        { id: 'breath3', thought: 'Let your breath guide you...' },
        { id: 'breath4', thought: 'Feel the rhythm of your breathing...' }
    ],
    displayTime: 6.0
}}>
    <BetweenThought 
        thoughts={[
            { id: 'breath1', thought: 'Breathe in peace...' },
            { id: 'breath2', thought: 'Breathe out tension...' },
            { id: 'breath3', thought: 'Let your breath guide you...' },
            { id: 'breath4', thought: 'Feel the rhythm of your breathing...' }
        ]}
        displayTime={6.0}
        on:done={() => {}}
    />
</Story>

<!-- Mindfulness thoughts -->
<Story name="Mindfulness Thoughts" args={{
    thoughts: [
        { id: 'mind1', thought: 'Notice this present moment...' },
        { id: 'mind2', thought: 'You are exactly where you need to be...' },
        { id: 'mind3', thought: 'Awareness is your natural state...' },
        { id: 'mind4', thought: 'Simply observe without judgment...' }
    ],
    displayTime: 7.0
}}>
    <BetweenThought 
        thoughts={[
            { id: 'mind1', thought: 'Notice this present moment...' },
            { id: 'mind2', thought: 'You are exactly where you need to be...' },
            { id: 'mind3', thought: 'Awareness is your natural state...' },
            { id: 'mind4', thought: 'Simply observe without judgment...' }
        ]}
        displayTime={7.0}
        on:done={() => {}}
    />
</Story>

<!-- Encouraging thoughts -->
<Story name="Encouraging Thoughts" args={{
    thoughts: [
        { id: 'encourage1', thought: "You're doing wonderfully..." },
        { id: 'encourage2', thought: 'Every step matters...' },
        { id: 'encourage3', thought: 'Trust in your journey...' },
        { id: 'encourage4', thought: 'You have everything you need within you...' }
    ],
    displayTime: 5.0
}}>
    <BetweenThought 
        thoughts={[
            { id: 'encourage1', thought: "You're doing wonderfully..." },
            { id: 'encourage2', thought: 'Every step matters...' },
            { id: 'encourage3', thought: 'Trust in your journey...' },
            { id: 'encourage4', thought: 'You have everything you need within you...' }
        ]}
        displayTime={5.0}
        on:done={() => {}}
    />
</Story>

<!-- Grounding thoughts -->
<Story name="Grounding Thoughts" args={{
    thoughts: [
        { id: 'ground1', thought: 'Feel your feet on the ground...' },
        { id: 'ground2', thought: 'You are safe in this moment...' },
        { id: 'ground3', thought: 'Connect with your body...' },
        { id: 'ground4', thought: 'Notice the stability beneath you...' }
    ],
    displayTime: 6.5
}}>
    <BetweenThought 
        thoughts={[
            { id: 'ground1', thought: 'Feel your feet on the ground...' },
            { id: 'ground2', thought: 'You are safe in this moment...' },
            { id: 'ground3', thought: 'Connect with your body...' },
            { id: 'ground4', thought: 'Notice the stability beneath you...' }
        ]}
        displayTime={6.5}
        on:done={() => {}}
    />
</Story>

<!-- Single thought -->
<Story name="Single Thought" args={{
    thoughts: [
        { id: 'single', thought: 'Take a moment to pause and breathe...' }
    ],
    displayTime: 5.0
}}>
    <BetweenThought 
        thoughts={[
            { id: 'single', thought: 'Take a moment to pause and breathe...' }
        ]}
        displayTime={5.0}
        on:done={() => {}}
    />
</Story>

<!-- Long thought text -->
<Story name="Long Thought Text" args={{
    thoughts: [
        { 
            id: 'long1', 
            thought: 'Allow yourself to settle into this moment of transition, knowing that each pause between actions is an opportunity to reconnect with your inner wisdom and strength...' 
        },
        { 
            id: 'long2', 
            thought: 'In the space between one breath and the next, between one action and another, lies infinite possibility and the gentle reminder that you are exactly where you need to be...' 
        }
    ],
    displayTime: 10.0
}}>
    <BetweenThought 
        thoughts={[
            { 
                id: 'long1', 
                thought: 'Allow yourself to settle into this moment of transition, knowing that each pause between actions is an opportunity to reconnect with your inner wisdom and strength...' 
            },
            { 
                id: 'long2', 
                thought: 'In the space between one breath and the next, between one action and another, lies infinite possibility and the gentle reminder that you are exactly where you need to be...' 
            }
        ]}
        displayTime={10.0}
        on:done={() => {}}
    />
</Story>

<!-- No countdown version -->
<Story name="No Timer" args={{
    thoughts: [
        { id: '1', thought: 'This thought will stay until manually dismissed...' },
        { id: '2', thought: 'No timer, just presence...' }
    ],
    displayTime: 0
}}>
    <BetweenThought 
        thoughts={[
            { id: '1', thought: 'This thought will stay until manually dismissed...' },
            { id: '2', thought: 'No timer, just presence...' }
        ]}
        displayTime={0}
        on:done={() => {}}
    />
</Story>

<!-- Empty thoughts array -->
<Story name="No Thoughts Available" args={{
    thoughts: [],
    displayTime: 5.0
}}>
    <BetweenThought 
        thoughts={[]}
        displayTime={5.0}
        on:done={() => {}}
    />
</Story>

<!-- Meditation transition thoughts -->
<Story name="Meditation Transitions" args={{
    thoughts: [
        { id: 'med1', thought: 'Settling into stillness...' },
        { id: 'med2', thought: 'Letting go of the day...' },
        { id: 'med3', thought: 'Opening to what is...' },
        { id: 'med4', thought: 'Resting in awareness...' },
        { id: 'med5', thought: 'Simply being...' }
    ],
    displayTime: 8.0
}}>
    <BetweenThought 
        thoughts={[
            { id: 'med1', thought: 'Settling into stillness...' },
            { id: 'med2', thought: 'Letting go of the day...' },
            { id: 'med3', thought: 'Opening to what is...' },
            { id: 'med4', thought: 'Resting in awareness...' },
            { id: 'med5', thought: 'Simply being...' }
        ]}
        displayTime={8.0}
        on:done={() => {}}
    />
</Story>

<!-- Stress relief thoughts -->
<Story name="Stress Relief Transitions" args={{
    thoughts: [
        { id: 'stress1', thought: 'Releasing what no longer serves you...' },
        { id: 'stress2', thought: 'Finding calm in the storm...' },
        { id: 'stress3', thought: 'You have the strength to handle this...' },
        { id: 'stress4', thought: 'This too shall pass...' },
        { id: 'stress5', thought: 'Peace is always available to you...' }
    ],
    displayTime: 6.0
}}>
    <BetweenThought 
        thoughts={[
            { id: 'stress1', thought: 'Releasing what no longer serves you...' },
            { id: 'stress2', thought: 'Finding calm in the storm...' },
            { id: 'stress3', thought: 'You have the strength to handle this...' },
            { id: 'stress4', thought: 'This too shall pass...' },
            { id: 'stress5', thought: 'Peace is always available to you...' }
        ]}
        displayTime={6.0}
        on:done={() => {}}
    />
</Story>

<!-- Progress indicator demonstration -->
<Story name="Progress Indicator Demo" args={{
    thoughts: [
        { id: 'progress', thought: 'Watch the progress circle complete as time passes...' }
    ],
    displayTime: 10.0
}}>
    <div class="p-4 space-y-4">
        <h3 class="text-lg font-semibold">Progress Indicator Features</h3>
        <div class="space-y-2 text-sm text-muted-foreground">
            <p>• <strong>Circular progress:</strong> Visual countdown showing time remaining</p>
            <p>• <strong>Smooth animation:</strong> Updates every 100ms for smooth progress</p>
            <p>• <strong>Auto-dismiss:</strong> Automatically closes when timer completes</p>
            <p>• <strong>Random selection:</strong> Randomly picks from available thoughts</p>
            <p>• <strong>Fade-in effect:</strong> Gentle opacity transition on appearance</p>
        </div>
    </div>
    <BetweenThought 
        thoughts={[
            { id: 'progress', thought: 'Watch the progress circle complete as time passes...' }
        ]}
        displayTime={10.0}
        on:done={() => {}}
    />
</Story>

<!-- Real-world usage context -->
<Story name="Usage Context Example">
    <div class="p-4 space-y-4">
        <h3 class="text-lg font-semibold">Between Thought Usage</h3>
        <div class="space-y-2 text-sm text-muted-foreground">
            <p>Between thoughts appear during action sequences to:</p>
            <p>• Provide gentle transitions between activities</p>
            <p>• Give users time to process and integrate</p>
            <p>• Maintain mindful awareness during practice</p>
            <p>• Create natural pauses in guided experiences</p>
            <p>• Offer encouragement and support</p>
        </div>
        <div class="p-4 bg-muted/20 rounded-lg">
            <h4 class="font-medium mb-2">Example Flow:</h4>
            <div class="text-sm space-y-1">
                <div>1. Action: "Take three deep breaths"</div>
                <div>2. Between Thought: "Finding your center..." (3 seconds)</div>
                <div>3. Action: "Notice your surroundings"</div>
                <div>4. Between Thought: "You're doing great..." (3 seconds)</div>
                <div>5. Action: "Set an intention"</div>
            </div>
        </div>
    </div>
    <BetweenThought 
        thoughts={[
            { id: 'context1', thought: 'This creates natural flow between actions...' },
            { id: 'context2', thought: 'Allowing space for integration...' },
            { id: 'context3', thought: 'Supporting your mindful practice...' }
        ]}
        displayTime={7.0}
        on:done={() => {}}
    />
</Story>