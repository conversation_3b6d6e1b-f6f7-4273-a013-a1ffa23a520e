<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import BreathingAnimation from '../lib/components/BreathingAnimation.svelte';

    const { Story } = defineMeta({
        title: 'Components/BreathingAnimation',
        component: BreathingAnimation,
        tags: ['autodocs'],
        argTypes: {
            inhaleDuration: {
                control: { type: 'range', min: 1, max: 10, step: 0.5 },
                description: 'Duration of inhale phase in seconds',
            },
            holdDuration: {
                control: { type: 'range', min: 0, max: 10, step: 0.5 },
                description: 'Duration of hold phase in seconds',
            },
            exhaleDuration: {
                control: { type: 'range', min: 1, max: 10, step: 0.5 },
                description: 'Duration of exhale phase in seconds',
            },
            relaxDuration: {
                control: { type: 'range', min: 0, max: 5, step: 0.5 },
                description: 'Duration of relax phase in seconds',
            },
            minSize: {
                control: { type: 'range', min: 50, max: 200, step: 10 },
                description: 'Minimum circle size in pixels',
            },
            maxSize: {
                control: { type: 'range', min: 200, max: 400, step: 10 },
                description: 'Maximum circle size in pixels',
            },
            countdown: {
                control: { type: 'range', min: 0, max: 10, step: 1 },
                description: 'Countdown before animation starts',
            },
            focusWords: {
                control: 'object',
                description: 'Array of focus words to display',
            },
        },
        args: {
            inhaleDuration: 4,
            holdDuration: 2,
            exhaleDuration: 6,
            relaxDuration: 1,
            minSize: 100,
            maxSize: 250,
            countdown: 3,
            focusWords: ['Breathe', 'Relax', 'Focus', 'Calm'],
        }
    });
</script>

<!-- Basic breathing patterns -->
<Story name="Four Seven Eight Breathing" args={{
    inhaleDuration: 4,
    holdDuration: 7,
    exhaleDuration: 8,
    relaxDuration: 0,
    minSize: 120,
    maxSize: 280,
    countdown: 3,
    focusWords: ['Inhale', 'Hold', 'Exhale', 'Rest']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={4}
            holdDuration={7}
            exhaleDuration={8}
            relaxDuration={0}
            minSize={120}
            maxSize={280}
            countdown={3}
            focusWords={['Inhale', 'Hold', 'Exhale', 'Rest']}
        />
    </div>
</Story>

<Story name="Box Breathing" args={{
    inhaleDuration: 4,
    holdDuration: 4,
    exhaleDuration: 4,
    relaxDuration: 4,
    minSize: 100,
    maxSize: 250,
    countdown: 3,
    focusWords: ['In', 'Hold', 'Out', 'Hold']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={4}
            holdDuration={4}
            exhaleDuration={4}
            relaxDuration={4}
            minSize={100}
            maxSize={250}
            countdown={3}
            focusWords={['In', 'Hold', 'Out', 'Hold']}
        />
    </div>
</Story>

<Story name="Simple Breathing" args={{
    inhaleDuration: 3,
    holdDuration: 0,
    exhaleDuration: 3,
    relaxDuration: 1,
    minSize: 80,
    maxSize: 200,
    countdown: 2,
    focusWords: ['Breathe In', 'Breathe Out']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={3}
            holdDuration={0}
            exhaleDuration={3}
            relaxDuration={1}
            minSize={80}
            maxSize={200}
            countdown={2}
            focusWords={['Breathe In', 'Breathe Out']}
        />
    </div>
</Story>

<!-- Different timing variations -->
<Story name="Quick Breathing" args={{
    inhaleDuration: 2,
    holdDuration: 1,
    exhaleDuration: 2,
    relaxDuration: 0.5,
    minSize: 90,
    maxSize: 180,
    countdown: 1,
    focusWords: ['Quick', 'In', 'Quick', 'Out']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={2}
            holdDuration={1}
            exhaleDuration={2}
            relaxDuration={0.5}
            minSize={90}
            maxSize={180}
            countdown={1}
            focusWords={['Quick', 'In', 'Quick', 'Out']}
        />
    </div>
</Story>

<Story name="Slow Deep Breathing" args={{
    inhaleDuration: 6,
    holdDuration: 3,
    exhaleDuration: 8,
    relaxDuration: 2,
    minSize: 150,
    maxSize: 350,
    countdown: 5,
    focusWords: ['Deep In', 'Hold Steady', 'Slow Out', 'Rest']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={6}
            holdDuration={3}
            exhaleDuration={8}
            relaxDuration={2}
            minSize={150}
            maxSize={350}
            countdown={5}
            focusWords={['Deep In', 'Hold Steady', 'Slow Out', 'Rest']}
        />
    </div>
</Story>

<!-- Different size variations -->
<Story name="Small Circle" args={{
    inhaleDuration: 4,
    holdDuration: 2,
    exhaleDuration: 4,
    relaxDuration: 1,
    minSize: 60,
    maxSize: 140,
    countdown: 3,
    focusWords: ['Small', 'Gentle', 'Calm', 'Peace']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={4}
            holdDuration={2}
            exhaleDuration={4}
            relaxDuration={1}
            minSize={60}
            maxSize={140}
            countdown={3}
            focusWords={['Small', 'Gentle', 'Calm', 'Peace']}
        />
    </div>
</Story>

<Story name="Large Circle" args={{
    inhaleDuration: 5,
    holdDuration: 3,
    exhaleDuration: 7,
    relaxDuration: 2,
    minSize: 200,
    maxSize: 400,
    countdown: 3,
    focusWords: ['Expand', 'Fill', 'Release', 'Flow']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={5}
            holdDuration={3}
            exhaleDuration={7}
            relaxDuration={2}
            minSize={200}
            maxSize={400}
            countdown={3}
            focusWords={['Expand', 'Fill', 'Release', 'Flow']}
        />
    </div>
</Story>

<!-- No countdown variation -->
<Story name="No Countdown" args={{
    inhaleDuration: 4,
    holdDuration: 2,
    exhaleDuration: 6,
    relaxDuration: 1,
    minSize: 100,
    maxSize: 250,
    countdown: 0,
    focusWords: ['Start', 'Now', 'Immediate', 'Begin']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={4}
            holdDuration={2}
            exhaleDuration={6}
            relaxDuration={1}
            minSize={100}
            maxSize={250}
            countdown={0}
            focusWords={['Start', 'Now', 'Immediate', 'Begin']}
        />
    </div>
</Story>

<!-- Different focus word themes -->
<Story name="Mindfulness Words" args={{
    inhaleDuration: 4,
    holdDuration: 3,
    exhaleDuration: 6,
    relaxDuration: 2,
    minSize: 120,
    maxSize: 280,
    countdown: 3,
    focusWords: ['Present', 'Aware', 'Mindful', 'Centered']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={4}
            holdDuration={3}
            exhaleDuration={6}
            relaxDuration={2}
            minSize={120}
            maxSize={280}
            countdown={3}
            focusWords={['Present', 'Aware', 'Mindful', 'Centered']}
        />
    </div>
</Story>

<Story name="Positive Affirmations" args={{
    inhaleDuration: 4,
    holdDuration: 2,
    exhaleDuration: 6,
    relaxDuration: 1,
    minSize: 100,
    maxSize: 250,
    countdown: 3,
    focusWords: ['I am calm', 'I am strong', 'I am peaceful', 'I am centered']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={4}
            holdDuration={2}
            exhaleDuration={6}
            relaxDuration={1}
            minSize={100}
            maxSize={250}
            countdown={3}
            focusWords={['I am calm', 'I am strong', 'I am peaceful', 'I am centered']}
        />
    </div>
</Story>

<Story name="Nature Words" args={{
    inhaleDuration: 5,
    holdDuration: 2,
    exhaleDuration: 7,
    relaxDuration: 1,
    minSize: 110,
    maxSize: 270,
    countdown: 3,
    focusWords: ['Ocean', 'Mountain', 'Forest', 'Sky']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={5}
            holdDuration={2}
            exhaleDuration={7}
            relaxDuration={1}
            minSize={110}
            maxSize={270}
            countdown={3}
            focusWords={['Ocean', 'Mountain', 'Forest', 'Sky']}
        />
    </div>
</Story>

<!-- Therapeutic patterns -->
<Story name="Anxiety Relief" args={{
    inhaleDuration: 4,
    holdDuration: 4,
    exhaleDuration: 8,
    relaxDuration: 2,
    minSize: 100,
    maxSize: 240,
    countdown: 5,
    focusWords: ['Safe', 'Calm', 'Grounded', 'Peaceful']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={4}
            holdDuration={4}
            exhaleDuration={8}
            relaxDuration={2}
            minSize={100}
            maxSize={240}
            countdown={5}
            focusWords={['Safe', 'Calm', 'Grounded', 'Peaceful']}
        />
    </div>
</Story>

<Story name="Sleep Preparation" args={{
    inhaleDuration: 6,
    holdDuration: 2,
    exhaleDuration: 10,
    relaxDuration: 3,
    minSize: 120,
    maxSize: 300,
    countdown: 3,
    focusWords: ['Sleepy', 'Drowsy', 'Relaxed', 'Peaceful']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={6}
            holdDuration={2}
            exhaleDuration={10}
            relaxDuration={3}
            minSize={120}
            maxSize={300}
            countdown={3}
            focusWords={['Sleepy', 'Drowsy', 'Relaxed', 'Peaceful']}
        />
    </div>
</Story>

<Story name="Energy Boost" args={{
    inhaleDuration: 3,
    holdDuration: 1,
    exhaleDuration: 2,
    relaxDuration: 0.5,
    minSize: 80,
    maxSize: 200,
    countdown: 2,
    focusWords: ['Energy', 'Vitality', 'Awake', 'Alert']
}}>
    <div class="h-screen bg-background">
        <BreathingAnimation 
            inhaleDuration={3}
            holdDuration={1}
            exhaleDuration={2}
            relaxDuration={0.5}
            minSize={80}
            maxSize={200}
            countdown={2}
            focusWords={['Energy', 'Vitality', 'Awake', 'Alert']}
        />
    </div>
</Story>

<!-- Compact view for smaller spaces -->
<Story name="Compact View" args={{
    inhaleDuration: 4,
    holdDuration: 2,
    exhaleDuration: 6,
    relaxDuration: 1,
    minSize: 60,
    maxSize: 120,
    countdown: 2,
    focusWords: ['In', 'Hold', 'Out', 'Rest']
}}>
    <div class="h-96 bg-background border border-border rounded-lg">
        <BreathingAnimation 
            inhaleDuration={4}
            holdDuration={2}
            exhaleDuration={6}
            relaxDuration={1}
            minSize={60}
            maxSize={120}
            countdown={2}
            focusWords={['In', 'Hold', 'Out', 'Rest']}
        />
    </div>
</Story>