<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" type="image/x-icon" href="%sveltekit.assets%/favicon.ico" />
		<link rel="icon" type="image/png" sizes="32x32" href="%sveltekit.assets%/favicon-32x32.png" />
		<link rel="icon" type="image/png" sizes="16x16" href="%sveltekit.assets%/favicon-16x16.png" />
		<link rel="icon" type="image/png" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
		<link rel="manifest" href="%sveltekit.assets%/manifest.json" />
		%sveltekit.head%

		<style>
		/* full‐screen black splash */
		#splash {
			position: fixed;
			inset: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #000;
			z-index: 9999;
		}

		/* logo starts at 50px */
		#splash .logo {
			width: 50px;
			animation: scaleLogo 2s ease-out forwards;
		}

		/* after 2s, fade out splash */
		#splash.fade-out {
			animation: fadeSplash 0.5s ease-in forwards;
		}

		@keyframes scaleLogo {
			to { width: 80vw; }
		}
		@keyframes fadeSplash {
			to {
			opacity: 0;
			visibility: hidden;
			}
		}
		</style>
	</head>
	<body data-sveltekit-preload-data="hover">
		<div id="splash">
		<img src="/Icon-Dark-1024x1024.png" alt="Logo" class="logo" />
		</div>

		<div style="display: contents">%sveltekit.body%</div>
	</body>
	<script>
		// Once the page is fully parsed, start the fade-out timer
		window.addEventListener('DOMContentLoaded', () => {
			// Give the scaleLogo 2s to finish
			setTimeout(() => {
			document.getElementById('splash').classList.add('fade-out');
			// remove it from the DOM after fade completes
			setTimeout(() => {
				const s = document.getElementById('splash');
				s && s.remove();
			}, 500);
			}, 2000);
		});

		if ('serviceWorker' in navigator) {
			window.addEventListener('load', () => {
				navigator.serviceWorker.register('%sveltekit.assets%/sw.js')
					.then(registration => {
						console.log('Service Worker registered: ', registration);
					})
					.catch(error => {
						console.error('Service Worker registration failed: ', error);
					});
			});
		}
	</script>
</html>
