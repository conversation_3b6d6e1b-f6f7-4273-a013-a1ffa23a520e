@import "tailwindcss";

@import "tw-animate-css";

@import "@fontsource-variable/playfair-display";

@import "@fontsource-variable/nunito-sans";


/* Custom font utilities */
.font-primary {
  font-family: var(--font-primary);
}

.font-secondary {
  font-family: var(--font-secondary);
}

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;

  --font-primary: "Playfair Display Variable", ui-serif, Georgia, Cambria,
    "Times New Roman", Times, serif;

  --font-secondary: "Nunito Sans Variable", ui-sans-serif, system-ui,
    -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans",
    sans-serif;
}

/* Base semantic colors - same for all themes */
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.1 0.05 240);
  --border: oklch(0.9 0.02 240);
  --input: oklch(0.95 0.01 240);
  --ring: var(--primary-500);
  --destructive: oklch(0.577 0.245 27.325);
  
  /* Semantic colors - consistent across light/dark modes */
  --muted: var(--primary-100);
  --muted-foreground: oklch(0.5 0.05 240);
  --primary: var(--primary-500);
  --primary-foreground: oklch(0.95 0.01 240);
  --secondary: var(--primary-200);
  --secondary-foreground: oklch(0.2 0.05 240);
  --accent: var(--primary-300);
  --accent-foreground: oklch(0.1 0.05 240);
}

.dark {
  --background: oklch(0.25 0.03 240);
  --foreground: oklch(0.92 0.01 240);
  --border: oklch(0.35 0.05 240);
  --input: oklch(0.3 0.04 240);
  --destructive: oklch(0.704 0.191 22.216);
  
  /* Keep semantic colors consistent - only adjust foreground colors for contrast */
  --muted-foreground: oklch(0.68 0.05 240);
  --primary-foreground: oklch(0.12 0.05 240);
  --secondary-foreground: oklch(0.88 0.02 240);
  --accent-foreground: oklch(0.92 0.01 240);
}

/* Blue Theme */
:root,
[data-theme="blue"] {
  /* Primary color scale - Blue */
  --primary-100: oklch(0.95 0.05 240);
  --primary-200: oklch(0.9 0.08 240);
  --primary-300: oklch(0.8 0.12 240);
  --primary-400: oklch(0.7 0.16 240);
  --primary-500: oklch(0.6 0.2 240);
  --primary-600: oklch(0.5 0.2 240);
  --primary-700: oklch(0.4 0.18 240);
  --primary-800: oklch(0.3 0.15 240);
  --primary-900: oklch(0.2 0.1 240);
}

/* Hunter Green Theme */
[data-theme="hunter-green"] {
  /* Primary color scale - Hunter Green */
  --primary-100: oklch(0.95 0.05 120);
  --primary-200: oklch(0.9 0.08 120);
  --primary-300: oklch(0.8 0.12 120);
  --primary-400: oklch(0.7 0.16 120);
  --primary-500: oklch(0.6 0.2 120);
  --primary-600: oklch(0.5 0.2 120);
  --primary-700: oklch(0.4 0.18 120);
  --primary-800: oklch(0.3 0.15 120);
  --primary-900: oklch(0.2 0.1 120);
}

/* Forest Green Theme */
[data-theme="forest-green"] {
  /* Primary color scale - Forest Green */
  --primary-100: oklch(0.95 0.05 140);
  --primary-200: oklch(0.9 0.08 140);
  --primary-300: oklch(0.8 0.12 140);
  --primary-400: oklch(0.7 0.16 140);
  --primary-500: oklch(0.6 0.2 140);
  --primary-600: oklch(0.5 0.2 140);
  --primary-700: oklch(0.4 0.18 140);
  --primary-800: oklch(0.3 0.15 140);
  --primary-900: oklch(0.2 0.1 140);
}

/* Red Theme */
[data-theme="red"] {
  /* Primary color scale - Red (CMYK 0,100,100,0 base) */
  --primary-100: oklch(0.95 0.05 29);
  --primary-200: oklch(0.9 0.1 29);
  --primary-300: oklch(0.8 0.15 29);
  --primary-400: oklch(0.7 0.2 29);
  --primary-500: oklch(0.628 0.257 29);
  --primary-600: oklch(0.55 0.25 29);
  --primary-700: oklch(0.45 0.22 29);
  --primary-800: oklch(0.35 0.18 29);
  --primary-900: oklch(0.25 0.12 29);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  
  /* Font families */
  --font-family-primary: var(--font-primary);
  --font-family-secondary: var(--font-secondary);
  
  /* Map to Tailwind color system */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-destructive: var(--destructive);
  
  /* Semantic colors */
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  
  /* Primary colors */
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-secondary);
    font-size: clamp(1.125rem, 2.5vw, 1.25rem); /* Responsive base size: 18px-20px */
    line-height: 1.5;
  }
  
  /* Input field styling - ensure proper contrast and theme support */
  input[type="email"],
  input[type="text"],
  input[type="password"],
  input[type="number"],
  textarea,
  select {
    @apply bg-input text-foreground border-border;
    color: var(--foreground) !important;
    background-color: var(--input) !important;
    border-color: var(--border) !important;
  }
  
  input[type="email"]:focus,
  input[type="text"]:focus,
  input[type="password"]:focus,
  input[type="number"]:focus,
  textarea:focus,
  select:focus {
    @apply ring-ring border-ring;
    border-color: var(--ring) !important;
    box-shadow: 0 0 0 2px var(--ring) !important;
  }
  
  /* Placeholder text styling */
  input::placeholder,
  textarea::placeholder {
    color: var(--muted-foreground) !important;
    opacity: 0.7;
  }
  
  /* Typography Scale - Responsive with clamp() - WCAG Compliant */
  h1 {
    font-family: var(--font-primary);
    font-size: clamp(3rem, 8vw, 4.77rem); /* Mobile: 48px, Desktop: 76.29px */
    line-height: 1.1;
    font-weight: 700;
    margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
  }
  
  h2 {
    font-family: var(--font-primary);
    font-size: clamp(2.375rem, 6.5vw, 3.82rem); /* Mobile: 38px, Desktop: 61.03px */
    line-height: 1.2;
    font-weight: 600;
    margin-bottom: clamp(1.25rem, 3vw, 1.875rem);
  }
  
  h3 {
    font-family: var(--font-primary);
    font-size: clamp(2rem, 5.5vw, 3.05rem); /* Mobile: 32px, Desktop: 48.82px */
    line-height: 1.3;
    font-weight: 600;
    margin-bottom: clamp(1rem, 2.5vw, 1.25rem);
  }
  
  h4 {
    font-family: var(--font-primary);
    font-size: clamp(1.75rem, 4.5vw, 2.44rem); /* Mobile: 28px, Desktop: 39.06px */
    line-height: 1.4;
    font-weight: 600;
  }
  
  h5 {
    font-family: var(--font-primary);
    font-size: clamp(1.5rem, 3.5vw, 1.95rem); /* Mobile: 24px, Desktop: 31.25px */
    line-height: 1.4;
    font-weight: 600;
  }
  
  h6 {
    font-family: var(--font-primary);
    font-size: clamp(1.25rem, 3vw, 1.56rem); /* Mobile: 20px, Desktop: 25px */
    line-height: 1.4;
    font-weight: 600;
  }
  
  p {
    font-family: var(--font-secondary);
    font-size: clamp(1.125rem, 2.5vw, 1.25rem); /* Mobile: 18px, Desktop: 20px - WCAG compliant */
    line-height: 1.6;
    margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
  }
  
  small, .text-small {
    font-family: var(--font-secondary);
    font-size: clamp(0.875rem, 2vw, 1rem); /* Mobile: 14px, Desktop: 16px - WCAG compliant */
    line-height: 1.5;
  }
  
  /* Button text sizing - responsive and proportional to button size */
  button, .btn {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem); /* Mobile: 14px, Desktop: 18px */
    line-height: 1.2;
  }
  
  /* Override Tailwind text utilities to maintain 14px minimum */
  .text-xs {
    font-size: clamp(0.875rem, 2vw, 0.875rem) !important; /* Minimum 14px */
  }
  
  .text-sm {
    font-size: clamp(0.875rem, 2vw, 1rem) !important; /* Minimum 14px, max 16px */
  }
}