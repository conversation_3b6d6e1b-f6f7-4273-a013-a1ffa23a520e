// Note: In a real implementation, you might want to use a more lightweight UUID generator
// or generate UUIDs at runtime. For now, we'll use crypto.randomUUID() which is available in modern browsers
function generateUUID(): string {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
        return crypto.randomUUID();
    }
    // Fallback for environments without crypto.randomUUID
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Import types from the existing system
import type { YBDMessage, YBDTag, ActionItem } from '$lib/types/notifications';

// Available MWE (Most Wanted Experience) tags from wizard
export const AVAILABLE_MWE_TAGS = [
    'Peace', 'Joy', 'Confidence', 'Connection', 'Purpose', 'Focus',
    'Self-worth', 'Calm', 'Contentment', 'Courage', 'Hope', 'Clarity',
    'Stability', 'Freedom', 'Security', 'Strength'
] as const;

// Available obstacle tags from wizard
export const AVAILABLE_OBSTACLE_TAGS = [
    'Fear', 'Anger', 'Doubt', 'Overwhelmed', 'Exhaustion', 'Pressure',
    'Isolation', 'Uncertainty', 'Distractions', 'Criticism', 'Perfectionism',
    'Anxiety', 'Negative self-talk', 'People', 'Procrastination', 'Myself',
    'Life', 'Unknown'
] as const;

// Helper function to create YBDTag objects
function createMweTag(tagName: string, id: number): YBDTag {
    return {
        id,
        tag_name: tagName,
        status: 'active',
        date_updated: '2025-06-30T07:00:00Z'
    };
}

function createObstacleTag(tagName: string, id: number): YBDTag {
    return {
        id,
        tag_name: tagName,
        status: 'active',
        date_updated: '2025-06-30T07:00:00Z'
    };
}

// Helper function to convert string arrays to YBDTag arrays
function createMweTags(tagNames: string[]): YBDTag[] {
    return tagNames.map((name, index) => createMweTag(name, 1000 + index));
}

function createObstacleTags(tagNames: string[]): YBDTag[] {
    return tagNames.map((name, index) => createObstacleTag(name, 2000 + index));
}

// Helper function to create complete YBDMessage objects
function createYBDMessage(
    message: string,
    actionItems: ActionItem[],
    mweTags: string[] = [],
    obstacleTags: string[] = []
): YBDMessage {
    return {
        id: generateUUID(),
        message,
        status: 'active',
        date_updated: new Date().toISOString(),
        dateLastViewed: null,
        favorite: false,
        actionItems: actionItems.map(action => ({
            ...action,
            dateLastTaken: action.dateLastTaken || null,
            favorite: action.favorite || false
        })),
        quote: {
            id: generateUUID(),
            quote: '',
            quote_author: ''
        },
        tags: [...createMweTags(mweTags), ...createObstacleTags(obstacleTags)]
    };
}

// Default YBD message objects for MVP without database
export const DEFAULT_YBD_MESSAGES: YBDMessage[] = [
    createYBDMessage(
        "Look for opportunities to be happy daily. It's very easy to focus on problems, but a shift in focus brings a happier life.",
        [
            {
                id: generateUUID(),
                action: "Reflect on the challenges you've overcome.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Avoid comparing yourself to others.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Practice gratitude for your progress.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Joy', 'Contentment', 'Hope'],
        ['Negative self-talk', 'Doubt']
    ),

    createYBDMessage(
        "Change can be daunting, but embracing it leads to growth. Look for the lessons in every change.",
        [
            {
                id: generateUUID(),
                action: "Identify one change you can welcome today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Reflect on how past changes led to positive outcomes.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Plan a small step towards a new opportunity.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Courage', 'Strength', 'Hope'],
        ['Fear', 'Uncertainty', 'Anxiety']
    ),

    createYBDMessage(
        "Take a few minutes today to sit quietly and be present. Mindfulness can bring clarity and calm.",
        [
            {
                id: generateUUID(),
                action: "Spend 5 minutes in silent meditation.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Focus on your breathing.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Notice the sensations in your body.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Calm', 'Peace', 'Clarity'],
        ['Anxiety', 'Overwhelmed', 'Distractions']
    ),

    createYBDMessage(
        "Positivity is a choice. Focus on the bright side even when challenges arise.",
        [
            {
                id: generateUUID(),
                action: "List three positive things about today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Smile at someone.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Remember past successes.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Joy', 'Hope', 'Strength'],
        ['Negative self-talk', 'Doubt', 'Overwhelmed']
    ),

    createYBDMessage(
        "No matter the obstacles, keep moving forward. Every step builds momentum towards your goals.",
        [
            {
                id: generateUUID(),
                action: "Set one small goal for today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Review progress on your long-term goals.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Take a brisk walk to clear your mind.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Purpose', 'Strength', 'Focus'],
        ['Procrastination', 'Doubt', 'Overwhelmed']
    ),

    createYBDMessage(
        "Take time to appreciate the present. Life is a collection of moments—cherish each one.",
        [
            {
                id: generateUUID(),
                action: "Pause and take a deep breath.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Observe your surroundings.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Smile at a simple pleasure.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Contentment', 'Peace', 'Joy'],
        ['Anxiety', 'Pressure', 'Distractions']
    ),

    createYBDMessage(
        "Every setback carries the seeds of a comeback. Embrace your failures as lessons on the road to success.",
        [
            {
                id: generateUUID(),
                action: "Write down one lesson from a past failure.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Discuss your insights with a friend.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Plan how to apply this lesson in the future.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Strength', 'Courage', 'Hope'],
        ['Fear', 'Doubt', 'Criticism']
    ),

    createYBDMessage(
        "Self-compassion is key to personal growth. Treat yourself with the same kindness you offer others.",
        [
            {
                id: generateUUID(),
                action: "Take a break and relax without guilt.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Write a note of self-encouragement.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Do one thing that makes you feel good.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Self-worth', 'Peace', 'Contentment'],
        ['Criticism', 'Perfectionism', 'Negative self-talk']
    ),

    createYBDMessage(
        "Instead of fixating on perfection, concentrate on the progress you make every day, no matter how small.",
        [
            {
                id: generateUUID(),
                action: "Write down one win from today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "List improvements compared to yesterday.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Reflect on lessons learned.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Confidence', 'Hope', 'Strength'],
        ['Perfectionism', 'Doubt', 'Criticism']
    ),

    createYBDMessage(
        "Self-belief is the foundation of achievement. Trust your abilities and take bold steps forward.",
        [
            {
                id: generateUUID(),
                action: "List three strengths you possess.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Write a positive affirmation for yourself.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Recall a past success that made you proud.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Confidence', 'Self-worth', 'Courage'],
        ['Doubt', 'Fear', 'Negative self-talk']
    ),

    createYBDMessage(
        "Discover what ignites your spirit and pursue it relentlessly. Passion fuels creativity and success.",
        [
            {
                id: generateUUID(),
                action: "Identify an interest that excites you.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Research how you can develop this interest.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Take the first small step towards pursuing it.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Purpose', 'Joy', 'Freedom'],
        ['Uncertainty', 'Fear', 'Procrastination']
    ),

    createYBDMessage(
        "Take time to be grateful for the little things. Gratitude transforms your perspective and life.",
        [
            {
                id: generateUUID(),
                action: "Write down three things you are grateful for today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Share gratitude with a friend.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Reflect on a positive memory.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Contentment', 'Joy', 'Peace'],
        ['Negative self-talk', 'Overwhelmed', 'Isolation']
    ),

    createYBDMessage(
        "Push your limits and try something new today. Challenges help you grow and discover your potential.",
        [
            {
                id: generateUUID(),
                action: "Set a small challenge for the day.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Take one step outside your comfort zone.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Reflect on the outcome and learn from it.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Courage', 'Strength', 'Confidence'],
        ['Fear', 'Doubt', 'Anxiety']
    ),

    createYBDMessage(
        "Define your purpose and let it guide your actions. A purpose-driven life is fulfilling and inspiring.",
        [
            {
                id: generateUUID(),
                action: "Reflect on what gives your life meaning.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Write down your personal mission statement.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Plan one action that aligns with your purpose.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Purpose', 'Clarity', 'Focus'],
        ['Uncertainty', 'Overwhelmed', 'Isolation']
    ),

    createYBDMessage(
        "Resilience helps you bounce back from setbacks. Keep pushing forward, even in the face of adversity.",
        [
            {
                id: generateUUID(),
                action: "Recall a time you overcame a difficulty.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Identify the strength that helped you through.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Commit to learning from challenges.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Strength', 'Courage', 'Hope'],
        ['Overwhelmed', 'Exhaustion', 'Doubt']
    ),

    createYBDMessage(
        "Value the people in your life. Nurture relationships and express your appreciation regularly.",
        [
            {
                id: generateUUID(),
                action: "Reach out to a friend you haven't spoken to in a while.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Express gratitude for someone special.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Plan a get-together or call.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Connection', 'Joy', 'Contentment'],
        ['Isolation', 'People', 'Anxiety']
    ),

    createYBDMessage(
        "Allow yourself to be creative. Whether it's art, writing, or problem-solving, creativity fuels innovation.",
        [
            {
                id: generateUUID(),
                action: "Spend 10 minutes doodling or brainstorming.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Experiment with a new idea or medium.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Share your creative work with someone.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Freedom', 'Joy', 'Confidence'],
        ['Perfectionism', 'Criticism', 'Doubt']
    ),

    createYBDMessage(
        "Strive for excellence in every task. Quality in your work is a reflection of your dedication.",
        [
            {
                id: generateUUID(),
                action: "Set a high standard for a task today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Review your work and seek improvements.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Celebrate a job well done.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Purpose', 'Confidence', 'Self-worth'],
        ['Perfectionism', 'Pressure', 'Doubt']
    ),

    createYBDMessage(
        "Set ambitious goals and allow your dreams to guide you. Big dreams create big opportunities.",
        [
            {
                id: generateUUID(),
                action: "Write down a dream or goal that excites you.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Break it down into actionable steps.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Visualize the success of achieving this dream.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Hope', 'Purpose', 'Courage'],
        ['Fear', 'Doubt', 'Uncertainty']
    ),

    createYBDMessage(
        "A smile can change your day and the day of those around you. Let your smile be contagious.",
        [
            {
                id: generateUUID(),
                action: "Smile at yourself in the mirror.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Share a smile with a stranger.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Think of something that makes you happy.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Joy', 'Connection', 'Contentment'],
        ['Isolation', 'Negative self-talk', 'Overwhelmed']
    ),

    // Messages with only MWE tags
    createYBDMessage(
        "Find moments of peace in your daily routine. Even small pauses can restore your energy.",
        [
            {
                id: generateUUID(),
                action: "Take three deep breaths before starting your next task.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Find a quiet spot for 2 minutes of silence.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Peace'],
        []
    ),

    createYBDMessage(
        "Celebrate your unique strengths and abilities. You have something special to offer the world.",
        [
            {
                id: generateUUID(),
                action: "Write down one thing you're naturally good at.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Use this strength to help someone today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Acknowledge yourself for this gift.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Confidence', 'Self-worth'],
        []
    ),

    createYBDMessage(
        "Focus brings clarity to your goals. When you concentrate on what matters, everything else becomes background noise.",
        [
            {
                id: generateUUID(),
                action: "Choose one priority for the next hour.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Remove distractions from your workspace.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Focus'],
        []
    ),

    createYBDMessage(
        "True security comes from within. Build your inner foundation of self-trust and resilience.",
        [
            {
                id: generateUUID(),
                action: "Reflect on a time you handled uncertainty well.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Identify one skill that makes you feel capable.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Practice this skill for 10 minutes today.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Security', 'Strength'],
        []
    ),

    createYBDMessage(
        "Freedom is the ability to choose your response to any situation. You always have choices.",
        [
            {
                id: generateUUID(),
                action: "Identify one area where you can make a different choice today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Take one small action that reflects your values.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Freedom'],
        []
    ),

    createYBDMessage(
        "Stability grows from consistent small actions. Build your foundation one day at a time.",
        [
            {
                id: generateUUID(),
                action: "Maintain one healthy routine today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Complete one task you've been putting off.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Organize one small area of your space.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Stability', 'Clarity'],
        []
    ),

    // Messages with only obstacle tags
    createYBDMessage(
        "When fear whispers 'what if,' answer with 'so what.' Most fears never materialize, and you're stronger than you think.",
        [
            {
                id: generateUUID(),
                action: "Name one fear that's been holding you back.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Ask yourself: 'What's the worst that could really happen?'",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Take one small step despite the fear.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Fear']
    ),

    createYBDMessage(
        "Anger is information. It tells you something matters to you. Listen to its message, then choose your response.",
        [
            {
                id: generateUUID(),
                action: "Pause and take five deep breaths when anger arises.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Ask: 'What boundary or value is being challenged?'",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Choose one constructive action to address the real issue.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Anger']
    ),

    createYBDMessage(
        "Overwhelm is often a sign you're trying to do too much at once. Break it down into smaller, manageable pieces.",
        [
            {
                id: generateUUID(),
                action: "List everything on your mind right now.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Choose the three most important items.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Focus on just one of these for the next 30 minutes.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Overwhelmed', 'Pressure']
    ),

    createYBDMessage(
        "Perfectionism is fear wearing a mask of high standards. Done is better than perfect.",
        [
            {
                id: generateUUID(),
                action: "Set a 'good enough' standard for one task today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Complete something at 80% instead of 100%.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Perfectionism']
    ),

    createYBDMessage(
        "Procrastination often stems from fear or feeling overwhelmed. Start with the smallest possible step.",
        [
            {
                id: generateUUID(),
                action: "Choose one task you've been avoiding.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Commit to working on it for just 5 minutes.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Set a timer and begin now.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Procrastination']
    ),

    createYBDMessage(
        "Exhaustion is your body and mind asking for rest. Honor this need without guilt.",
        [
            {
                id: generateUUID(),
                action: "Take a 10-minute break right now.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Do something that genuinely restores you.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Plan for better rest tonight.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Exhaustion']
    ),

    createYBDMessage(
        "Criticism, whether from others or yourself, doesn't define your worth. You are more than any single opinion.",
        [
            {
                id: generateUUID(),
                action: "Identify one piece of criticism that's been bothering you.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Ask: 'Is there any truth I can learn from?'",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Release what doesn't serve your growth.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Criticism', 'Negative self-talk']
    ),

    createYBDMessage(
        "Uncertainty is the only certainty in life. Learning to be comfortable with not knowing is a superpower.",
        [
            {
                id: generateUUID(),
                action: "Identify one area of uncertainty in your life.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Focus on what you can control in this situation.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Uncertainty']
    ),

    createYBDMessage(
        "Isolation often feels safer than connection, but humans are wired for community. You don't have to face everything alone.",
        [
            {
                id: generateUUID(),
                action: "Reach out to one person today, even with a simple message.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Share one thing you're going through with someone you trust.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Accept help when it's offered.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        [],
        ['Isolation']
    ),

    // Mixed messages with fewer tags
    createYBDMessage(
        "Clarity comes when you stop trying to figure everything out at once. Focus on the next right step.",
        [
            {
                id: generateUUID(),
                action: "Ask yourself: 'What's the very next thing I need to do?'",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Do that one thing without worrying about step two.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Clarity'],
        ['Overwhelmed']
    ),

    createYBDMessage(
        "Courage isn't the absence of fear—it's feeling the fear and moving forward anyway.",
        [
            {
                id: generateUUID(),
                action: "Acknowledge your fear without judgment.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Take one brave action today, however small.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Courage'],
        ['Fear']
    ),

    createYBDMessage(
        "Connection starts with being authentic. When you show up as yourself, you invite others to do the same.",
        [
            {
                id: generateUUID(),
                action: "Share something genuine about yourself with someone today.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Listen deeply to someone without trying to fix or advise.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Connection'],
        ['Isolation']
    ),

    createYBDMessage(
        "Hope is a choice you make every day. Even in darkness, you can choose to believe in possibility.",
        [
            {
                id: generateUUID(),
                action: "Write down one thing you're hopeful about.",
                dateLastTaken: null,
                favorite: false
            },
            {
                id: generateUUID(),
                action: "Take one action that moves you toward that hope.",
                dateLastTaken: null,
                favorite: false
            }
        ],
        ['Hope'],
        ['Doubt']
    )
];

// Helper function to get messages by tag
export function getMessagesByTag(tag: string): YBDMessage[] {
    return DEFAULT_YBD_MESSAGES.filter(message => 
        message.tags.some(messageTag => messageTag.tag_name.toLowerCase() === tag.toLowerCase())
    );
}

// Helper function to get messages by multiple tags (OR logic)
export function getMessagesByTags(tags: string[] = []): YBDMessage[] {
    if (tags.length === 0) return DEFAULT_YBD_MESSAGES;
  
    return DEFAULT_YBD_MESSAGES.filter(message => 
        tags.some(tag => 
            message.tags.some(messageTag => 
                messageTag.tag_name.toLowerCase() === tag.toLowerCase()
            )
        )
    );
}

// Helper function to get a random message
export function getRandomMessage(): YBDMessage {
    const randomIndex = Math.floor(Math.random() * DEFAULT_YBD_MESSAGES.length);
    return DEFAULT_YBD_MESSAGES[randomIndex];
}

// Helper function to get messages for specific user profile
export function getMessagesForProfile(userExperiences: string[], userObstacles: string[]): YBDMessage[] {
    const allUserTags = [...userExperiences, ...userObstacles];
    return getMessagesByTags(allUserTags);
}
        