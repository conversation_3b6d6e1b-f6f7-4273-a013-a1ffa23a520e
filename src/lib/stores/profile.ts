import { writable, get } from 'svelte/store';
import { browser } from '$app/environment';
import { currentTheme, type Theme } from './theme';

export interface Goal {
  id: string;
  name: string;
  description?: string;
  why?: string; // For Step 5 reflection
}

export interface TimeRange {
  id: number;
  startHour: number;
  endHour: number;
  maxPerHour: number;
}

export interface Schedule {
  id: number;
  name: string;
  selectedDays: string[];
  timeRanges: TimeRange[];
}

export interface Profile {
  personal: { preferredName: string; ageRange: string; genderIdentity: string };
  preferences: { theme: Theme };
  experiences: { name: string; summary: string }[];
  obstacles: { name: string; summary: string }[];
  goals: Goal[];
  schedules: Schedule[];
  wizardCompleted: boolean;
}

// Migrate schedule data to ensure it has required fields
function migrateSchedules(schedules: unknown[]): Schedule[] {
    console.log('[MIGRATION] Input schedules:', schedules);
  
    if (!Array.isArray(schedules)) {
        console.log('[MIGRATION] Schedules is not an array, returning empty array');
        return [];
    }
  
    const migrated = schedules.map((schedule, index) => {
        console.log(`[MIGRATION] Processing schedule ${index}:`, schedule);
        
        const scheduleObj = schedule as Record<string, unknown>;
    
        // Always migrate to ensure all fields are present and valid
        const migratedSchedule = {
            id: scheduleObj.id || index + 1,
            name: scheduleObj.name || `Schedule ${index + 1}`,
            selectedDays: Array.isArray(scheduleObj.selectedDays) ? scheduleObj.selectedDays : [],
            timeRanges: Array.isArray(scheduleObj.timeRanges)
                ? scheduleObj.timeRanges.map((tr: unknown, trIndex: number) => {
                    const timeRange = tr as Record<string, unknown>;
                    return {
                        id: timeRange.id !== undefined ? Number(timeRange.id) : trIndex + 1,
                        startHour: timeRange.startHour !== undefined ? Number(timeRange.startHour) : 9,
                        endHour: timeRange.endHour !== undefined ? Number(timeRange.endHour) : 10,
                        maxPerHour: timeRange.maxPerHour !== undefined ? Number(timeRange.maxPerHour) : 1
                    };
                })
                : []
        } as Schedule;
    
        console.log(`[MIGRATION] Migrated schedule ${index}:`, migratedSchedule);
        return migratedSchedule;
    });
  
    console.log('[MIGRATION] Final migrated schedules:', migrated);
    return migrated;
}

// Get initial profile from localStorage or default
function getInitialProfile(): Profile {
    console.log('[PROFILE INIT] Starting profile initialization');
    if (browser) {
        try {
            const stored = localStorage.getItem('profile');
            console.log('[PROFILE INIT] Raw localStorage data length:', stored?.length || 0);
            if (stored) {
                const parsedProfile = JSON.parse(stored) as Profile;
                console.log('[PROFILE INIT] Parsed profile:', {
                    experiences: parsedProfile.experiences?.length || 0,
                    obstacles: parsedProfile.obstacles?.length || 0,
                    schedules: parsedProfile.schedules?.length || 0,
                    wizardCompleted: parsedProfile.wizardCompleted
                });
        
                // Ensure the profile has all required fields with defaults
                const migratedProfile = {
                    personal: parsedProfile.personal || { preferredName: '', ageRange: '', genderIdentity: '' },
                    preferences: parsedProfile.preferences || { theme: get(currentTheme) },
                    experiences: parsedProfile.experiences || [],
                    obstacles: parsedProfile.obstacles || [],
                    goals: parsedProfile.goals || [],
                    schedules: migrateSchedules(parsedProfile.schedules || []),
                    wizardCompleted: parsedProfile.wizardCompleted || false
                };
        
                console.log('[PROFILE INIT] Migrated profile:', {
                    experiences: migratedProfile.experiences?.length || 0,
                    obstacles: migratedProfile.obstacles?.length || 0,
                    schedules: migratedProfile.schedules?.length || 0,
                    wizardCompleted: migratedProfile.wizardCompleted
                });
        
                // If we migrated schedules, save the updated profile back to localStorage
                if (parsedProfile.schedules && parsedProfile.schedules.length > 0) {
                    console.log('Migrating schedule data with required fields');
                    localStorage.setItem('profile', JSON.stringify(migratedProfile));
                }
        
                return migratedProfile;
            }
        } catch (error) {
            console.warn('Failed to load profile from localStorage:', error);
        }
    }
  
    console.log('[PROFILE INIT] Returning default profile');
    // Return default profile
    return {
        personal: { preferredName: '', ageRange: '', genderIdentity: '' },
        preferences: { theme: get(currentTheme) },
        experiences: [],
        obstacles: [],
        goals: [],
        schedules: [],
        wizardCompleted: false
    };
}

export const profile = writable<Profile>(getInitialProfile());

// Save profile to localStorage whenever it changes
if (browser) {
    profile.subscribe((profileData) => {
        try {
            localStorage.setItem('profile', JSON.stringify(profileData));
            console.log('Profile saved to localStorage:', profileData);
        } catch (error) {
            console.warn('Failed to save profile to localStorage:', error);
        }
    });
}

// Subscribe to currentTheme changes and update profile preferences
currentTheme.subscribe((theme) => {
    profile.update((p) => {
        p.preferences.theme = theme;
        return p;
    });
});

export const resetProfile = () => {
    const defaultProfile = {
        personal: { preferredName: '', ageRange: '', genderIdentity: '' },
        preferences: { theme: get(currentTheme) }, // Reset with the current theme
        experiences: [],
        obstacles: [],
        goals: [],
        schedules: [],
        wizardCompleted: false
    };
  
    profile.set(defaultProfile);
  
    // Also clear from localStorage
    if (browser) {
        try {
            localStorage.removeItem('profile');
            console.log('Profile reset and cleared from localStorage.');
        } catch (error) {
            console.warn('Failed to clear profile from localStorage:', error);
        }
    }
};