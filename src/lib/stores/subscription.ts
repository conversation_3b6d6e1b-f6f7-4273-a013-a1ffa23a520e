import { writable, derived } from 'svelte/store';
import { page } from '$app/stores';
import type { SubscriptionStatus } from '$lib/services/subscriptionService';

// Create a derived store from page data
export const subscription = derived(
    page,
    ($page) => $page.data?.subscription as SubscriptionStatus | undefined
);

// Derived stores for common checks
export const hasActiveSubscription = derived(
    subscription,
    ($subscription) => $subscription?.isActive || false
);

export const isInGracePeriod = derived(
    subscription,
    ($subscription) => $subscription?.isInGracePeriod || false
);

export const subscriptionStatus = derived(
    subscription,
    ($subscription) => $subscription?.status || null
);

// Helper store for managing upgrade prompts
export const showUpgradePrompt = writable(false);

export function promptUpgrade() {
    showUpgradePrompt.set(true);
}

export function dismissUpgradePrompt() {
    showUpgradePrompt.set(false);
}