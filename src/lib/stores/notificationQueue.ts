import { writable, derived, get } from 'svelte/store';
import { profile } from './profile';
import type { NotificationItem, NotificationStats } from '$lib/types/notifications';
import { notificationService } from '$lib/services/ybdmessage/notificationService';

// Core notification queue store
export const notificationQueue = writable<NotificationItem[]>([]);

// Status of the notification queue
export const queueStatus = writable<'loading' | 'ready' | 'error' | 'empty'>('loading');

// Last time the queue was generated
export const lastGenerated = writable<Date | null>(null);

// Error message if queue generation fails
export const queueError = writable<string | null>(null);

// Statistics about the notification queue
export const queueStats = writable<NotificationStats | null>(null);

// Derived store for upcoming notifications (next 6 hours)
export const upcomingNotifications = derived(
    notificationQueue,
    ($queue) => {
        const sixHoursFromNow = new Date(Date.now() + 6 * 60 * 60 * 1000);
        return $queue.filter(notification => 
            notification.scheduledTime <= sixHoursFromNow && !notification.sent
        );
    }
);

// Derived store for today's notifications
export const todaysNotifications = derived(
    notificationQueue,
    ($queue) => {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);
    
        return $queue.filter(notification => 
            notification.scheduledTime >= startOfDay && 
      notification.scheduledTime < endOfDay
        );
    }
);

// Derived store for unread notifications
export const unreadNotifications = derived(
    notificationQueue,
    ($queue) => $queue.filter(notification => !notification.read)
);

// Derived store for sent but unread notifications
export const sentUnreadNotifications = derived(
    notificationQueue,
    ($queue) => $queue.filter(notification => notification.sent && !notification.read)
);

/**
 * Initialize the notification queue system
 */
export async function initializeNotificationQueue(): Promise<void> {
    try {
        queueStatus.set('loading');
        queueError.set(null);
    
        const currentProfile = get(profile);
        const notifications = await notificationService.ensureValidQueue(currentProfile);
    
        notificationQueue.set(notifications);
        lastGenerated.set(new Date());
    
        if (notifications.length === 0) {
            queueStatus.set('empty');
        } else {
            queueStatus.set('ready');
        }

        // Update statistics
        const stats = await notificationService.getNotificationStats(currentProfile);
        queueStats.set(stats);

    } catch (error) {
        console.error('Failed to initialize notification queue:', error);
        queueStatus.set('error');
        queueError.set(error instanceof Error ? error.message : 'Unknown error');
    }
}

/**
 * Regenerate the notification queue
 */
export async function regenerateNotificationQueue(): Promise<void> {
    try {
        queueStatus.set('loading');
        queueError.set(null);
    
        const currentProfile = get(profile);
        // Clear cache first to ensure fresh generation
        notificationService.clearCache();
        const notifications = await notificationService.regenerateQueue(currentProfile);
    
        notificationQueue.set(notifications);
        lastGenerated.set(new Date());
    
        if (notifications.length === 0) {
            queueStatus.set('empty');
        } else {
            queueStatus.set('ready');
        }

        // Update statistics
        const stats = await notificationService.getNotificationStats(currentProfile);
        queueStats.set(stats);

    } catch (error) {
        console.error('Failed to regenerate notification queue:', error);
        queueStatus.set('error');
        queueError.set(error instanceof Error ? error.message : 'Unknown error');
    }
}

/**
 * Handle profile changes by regenerating the queue if necessary
 */
export async function handleProfileChange(): Promise<void> {
    try {
        const currentProfile = get(profile);
        // Clear cache first to ensure fresh generation
        notificationService.clearCache();
        const notifications = await notificationService.handleProfileChange(currentProfile);
    
        notificationQueue.set(notifications);
        lastGenerated.set(new Date());
    
        if (notifications.length === 0) {
            queueStatus.set('empty');
        } else {
            queueStatus.set('ready');
        }

        // Update statistics
        const stats = await notificationService.getNotificationStats(currentProfile);
        queueStats.set(stats);

    } catch (error) {
        console.error('Failed to handle profile change:', error);
        queueStatus.set('error');
        queueError.set(error instanceof Error ? error.message : 'Unknown error');
    }
}

/**
 * Mark a notification as sent
 */
export function markNotificationSent(notificationId: string): void {
    notificationService.markNotificationSent(notificationId);
  
    // Update the store
    notificationQueue.update(queue => 
        queue.map(notification => 
            notification.id === notificationId 
                ? { ...notification, sent: true }
                : notification
        )
    );
}

/**
 * Mark a notification as read
 */
export function markNotificationRead(notificationId: string): void {
    notificationService.markNotificationRead(notificationId);
  
    // Update the store
    notificationQueue.update(queue => 
        queue.map(notification => 
            notification.id === notificationId 
                ? { ...notification, read: true }
                : notification
        )
    );
}

/**
 * Get upcoming notifications for a specific time window
 */
export async function getUpcomingNotifications(hours: number = 6): Promise<NotificationItem[]> {
    const currentProfile = get(profile);
    return await notificationService.getUpcomingNotifications(currentProfile, hours);
}

/**
 * Clear the notification cache and queue
 */
export function clearNotificationQueue(): void {
    notificationService.clearCache();
    notificationQueue.set([]);
    queueStatus.set('empty');
    lastGenerated.set(null);
    queueError.set(null);
    queueStats.set(null);
}

/**
 * Get debug information about the notification system
 */
export async function getNotificationDebugInfo(): Promise<{
  profileHash: string;
  scheduleStats: Record<string, unknown>;
  matchingStats: Record<string, unknown>;
  cacheInfo: Record<string, unknown>;
  upcomingSlots: Array<{
    time: string;
    priority: 'high' | 'medium' | 'low';
    scheduleId: number;
  }>;
}> {
    const currentProfile = get(profile);
    return await notificationService.getDebugInfo(currentProfile);
}

/**
 * Force regeneration of the notification queue (for testing)
 */
export async function forceRegenerateQueue(): Promise<void> {
    try {
        queueStatus.set('loading');
        queueError.set(null);
    
        const currentProfile = get(profile);
        const notifications = await notificationService.forceRegeneration(currentProfile);
    
        notificationQueue.set(notifications);
        lastGenerated.set(new Date());
    
        if (notifications.length === 0) {
            queueStatus.set('empty');
        } else {
            queueStatus.set('ready');
        }

        // Update statistics
        const stats = await notificationService.getNotificationStats(currentProfile);
        queueStats.set(stats);

    } catch (error) {
        console.error('Failed to force regenerate queue:', error);
        queueStatus.set('error');
        queueError.set(error instanceof Error ? error.message : 'Unknown error');
    }
}

// Subscribe to profile changes and automatically handle them
let profileUnsubscribe: (() => void) | null = null;

/**
 * Start automatic profile change monitoring
 */
export function startProfileMonitoring(): void {
    if (profileUnsubscribe) {
        profileUnsubscribe();
    }

    let previousProfileHash = '';
  
    profileUnsubscribe = profile.subscribe(async (currentProfile) => {
    // Skip initial subscription call
        if (!currentProfile.wizardCompleted) {
            return;
        }

        // Generate hash to detect meaningful changes
        const currentHash = JSON.stringify({
            experiences: currentProfile.experiences,
            obstacles: currentProfile.obstacles,
            schedules: currentProfile.schedules
        });

        if (previousProfileHash && currentHash !== previousProfileHash) {
            console.log('Profile change detected, updating notification queue');
            await handleProfileChange();
        }

        previousProfileHash = currentHash;
    });
}

/**
 * Stop automatic profile change monitoring
 */
export function stopProfileMonitoring(): void {
    if (profileUnsubscribe) {
        profileUnsubscribe();
        profileUnsubscribe = null;
    }
}

// Auto-start monitoring when this module is imported
if (typeof window !== 'undefined') {
    startProfileMonitoring();
}

// Cleanup on page unload
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        stopProfileMonitoring();
    });
}