import { writable, get } from 'svelte/store';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { supabase } from '$lib/services/supabase/supabaseClient';
import type { User, Session } from '@supabase/supabase-js';
import { initializeUserVerification, cleanupUserVerification } from '$lib/services/userVerificationService';

export interface AuthState {
	user: User | null;
	session: Session | null;
	loading: boolean;
	initialized: boolean;
}

const initialState: AuthState = {
    user: null,
    session: null,
    loading: true,
    initialized: false
};

export const authStore = writable<AuthState>(initialState);

// Initialize auth state when the store is created
if (browser) {
    initializeAuth();
}

async function initializeAuth() {
    try {
        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession();
		
        if (error) {
            console.error('Error getting initial session:', error);
        }

        authStore.set({
            user: session?.user || null,
            session: session || null,
            loading: false,
            initialized: true
        });

        // Initialize user verification if user is logged in
        if (session?.user) {
            initializeUserVerification();
        }

        // Listen for auth changes
        supabase.auth.onAuthStateChange((event, session) => {
            console.log('Auth state changed:', event, session);
			
            authStore.update(state => ({
                ...state,
                user: session?.user || null,
                session: session || null,
                loading: false
            }));

            // Handle sign out - redirect to signin page
            if (event === 'SIGNED_OUT') {
                cleanupUserVerification();
                goto('/auth/signin');
            }
   
            // Handle sign in - redirect to home if on auth page
            if (event === 'SIGNED_IN' && (window.location.pathname.startsWith('/auth/'))) {
                initializeUserVerification();
                goto('/app');
            }
        });
    } catch (error) {
        console.error('Error initializing auth:', error);
        authStore.set({
            user: null,
            session: null,
            loading: false,
            initialized: true
        });
    }
}

// Helper function to check if user is authenticated
export function isAuthenticated(): Promise<boolean> {
    return new Promise((resolve) => {
        const currentState = get(authStore);
        if (currentState.initialized) {
            resolve(!!currentState.session);
            return;
        }
        
        const unsubscribe = authStore.subscribe((state) => {
            if (state.initialized) {
                unsubscribe();
                resolve(!!state.session);
            }
        });
    });
}

// Helper function to wait for auth initialization
export function waitForAuthInit(): Promise<AuthState> {
    return new Promise((resolve) => {
        const currentState = get(authStore);
        if (currentState.initialized) {
            resolve(currentState);
            return;
        }
        
        const unsubscribe = authStore.subscribe((state) => {
            if (state.initialized) {
                unsubscribe();
                resolve(state);
            }
        });
    });
}