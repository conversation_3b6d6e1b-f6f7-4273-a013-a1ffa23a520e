import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type Theme = 'blue' | 'hunter-green' | 'forest-green' | 'red';
export type DarkMode = 'light' | 'dark' | 'system';

export const themes: { value: Theme; label: string }[] = [
    { value: 'blue', label: 'Blue' },
    { value: 'hunter-green', label: 'Hunter Green' },
    { value: 'forest-green', label: 'Forest Green' },
    { value: 'red', label: 'Red' }
];

// Get initial theme from localStorage or default to 'blue'
function getInitialTheme(): Theme {
    if (browser) {
        const stored = localStorage.getItem('theme') as Theme;
        if (stored && themes.some(t => t.value === stored)) {
            return stored;
        }
    }
    return 'blue';
}

// Get initial dark mode from localStorage or default to 'light'
function getInitialDarkMode(): DarkMode {
    if (browser) {
        const stored = localStorage.getItem('darkMode') as DarkMode;
        if (stored && (stored === 'light' || stored === 'dark' || stored === 'system')) {
            return stored;
        }
    }
    return 'system'; // Default to system
}

// Create the stores
export const currentTheme = writable<Theme>(getInitialTheme());
export const darkMode = writable<DarkMode>(getInitialDarkMode());

// Apply theme to document
export function applyTheme(theme: Theme) {
    if (browser) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        console.log('Theme applied:', theme);
    }
}

// Apply dark mode to document
export function applyDarkMode(mode: DarkMode) {
    if (browser) {
        if (mode === 'system') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (prefersDark) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        } else if (mode === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        localStorage.setItem('darkMode', mode);
        console.log('Dark mode applied:', mode);
    }
}

// Subscribe to theme changes and apply them
if (browser) {
    currentTheme.subscribe((theme) => {
        console.log('currentTheme store updated:', theme);
        applyTheme(theme);
    });
	
    darkMode.subscribe((mode) => {
        console.log('darkMode store updated:', mode);
        applyDarkMode(mode);
    });
	
    // Apply initial theme and dark mode
    applyTheme(getInitialTheme());
    applyDarkMode(getInitialDarkMode());

    // Listen for system theme changes if in system mode
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
        if (getInitialDarkMode() === 'system') {
            applyDarkMode('system'); // Re-apply system mode to update
        }
    });
}