import { writable, derived } from 'svelte/store';
import type { BeliefExplorerState, BeliefExplorerData, Goal, SessionHistory } from '../types/beliefs';

// Generate unique session ID
function generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Initial state for the beliefs explorer
const initialState: BeliefExplorerState = {
    // Current session data
    currentStepIndex: 0,
    selectedDynamic: null,
    selectedBeliefs: [],
    selectedTheme: null,
    goals: [],
    beliefAlignments: [],
	
    // UI state
    starredFlashcards: [],
    currentFlashcards: [],
	
    // Session management
    sessionId: null,
    sessionHistory: [],
    autoSaveEnabled: true
};

// Main beliefs explorer store
export const beliefsExplorerState = writable<BeliefExplorerState>(initialState);

// Store for the beliefs explorer data (loaded from JSON)
export const beliefsExplorerData = writable<BeliefExplorerData | null>(null);

// Derived store for the current step
export const currentStep = derived(
    [beliefsExplorerState, beliefsExplorerData],
    ([$state, $data]) => {
        if (!$data || $state.currentStepIndex >= 6) { // 6 steps total (0-5)
            return null;
        }
        return {
            index: $state.currentStepIndex,
            total: 6,
            ...getStepConfig($state.currentStepIndex)
        };
    }
);

// Step configurations for the new 6-step wizard
function getStepConfig(stepIndex: number) {
    const steps = [
        {
            id: 'step-1-notice-discomfort',
            name: 'Notice Discomfort & Identify Core Dynamic',
            description: 'Often we feel uneasy without noticing the exact thoughts behind it. By observing what you\'re thinking right now, you can begin to understand and address your discomfort. Let\'s identify the main issue or core dynamic affecting you.',
            prompt: 'What are you thinking right now?',
            type: 'core-dynamic-selection'
        },
        {
            id: 'step-2-identify-beliefs',
            name: 'Identify / Name Beliefs at Play',
            description: 'Our beliefs influence how we interpret events. Let\'s explore the thoughts or beliefs that come up for you right now. Identifying these can help you see patterns in how you\'re thinking.',
            prompt: 'Which thoughts or beliefs are coming up?',
            type: 'belief-selection'
        },
        {
            id: 'step-3-choose-theme',
            name: 'Choose Your Theme',
            description: 'Themes can set a tone or goal for this process. Browse through and select the theme that best matches how you want to approach this moment.',
            prompt: 'Slide to select a theme for this session',
            type: 'theme-carousel'
        },
        {
            id: 'step-4-define-goals',
            name: 'Define Your Goals',
            description: 'Setting clear goals helps focus your mind. Write down up to 5 goals or outcomes you\'d like to accomplish through this process.',
            prompt: 'Add a goal or outcome you want to achieve',
            type: 'goal-input'
        },
        {
            id: 'step-5-discover-why',
            name: 'Discover the "Why" Behind Your Goals',
            description: 'Let\'s explore why each goal matters to you. Asking "why" multiple times can reveal the deeper reasons behind your aspirations.',
            prompt: 'Why is this goal important to you?',
            type: 'why-exploration'
        },
        {
            id: 'step-6-review-alignment',
            name: 'Review Alignment – Goals vs Beliefs',
            description: 'Now let\'s see how your beliefs relate to each goal. A belief can either support a goal (help achieve it) or hinder it. Identifying conflicts can show what you might want to work on.',
            prompt: 'Tap a belief below to evaluate it against your goal',
            type: 'alignment-review'
        }
    ];
	
    return steps[stepIndex] || null;
}

// Derived store for progress calculation
export const progress = derived(
    [beliefsExplorerState],
    ([$state]) => {
        return {
            current: $state.currentStepIndex + 1,
            total: 6,
            percentage: Math.round((($state.currentStepIndex + 1) / 6) * 100)
        };
    }
);

// Auto-save functionality
function autoSave(state: BeliefExplorerState) {
    if (!state.autoSaveEnabled || !state.sessionId) return;
	
    try {
        localStorage.setItem(`beliefs_session_${state.sessionId}`, JSON.stringify({
            sessionId: state.sessionId,
            currentStepIndex: state.currentStepIndex,
            selectedDynamic: state.selectedDynamic,
            selectedBeliefs: state.selectedBeliefs,
            selectedTheme: state.selectedTheme,
            goals: state.goals,
            beliefAlignments: state.beliefAlignments,
            timestamp: new Date().toISOString()
        }));
    } catch (error) {
        console.warn('Failed to auto-save session:', error);
    }
}

// Load session from localStorage
function loadSession(sessionId: string): Partial<BeliefExplorerState> | null {
    try {
        const saved = localStorage.getItem(`beliefs_session_${sessionId}`);
        if (saved) {
            return JSON.parse(saved);
        }
    } catch (error) {
        console.warn('Failed to load session:', error);
    }
    return null;
}

// Actions for the beliefs explorer
export const beliefsExplorerActions = {
    // Load beliefs explorer data
    loadData: (data: BeliefExplorerData) => {
        beliefsExplorerData.set(data);
    },

    // Start a new session
    startNewSession: () => {
        const sessionId = generateSessionId();
        beliefsExplorerState.update(state => ({
            ...initialState,
            sessionId,
            sessionHistory: state.sessionHistory,
            autoSaveEnabled: state.autoSaveEnabled
        }));
    },

    // Resume an existing session
    resumeSession: (sessionId: string) => {
        const savedSession = loadSession(sessionId);
        if (savedSession) {
            beliefsExplorerState.update(state => ({
                ...state,
                ...savedSession,
                sessionId
            }));
        }
    },

    // Navigate to next step
    nextStep: () => {
        beliefsExplorerState.update(state => {
            const newState = {
                ...state,
                currentStepIndex: Math.min(state.currentStepIndex + 1, 5)
            };
            autoSave(newState);
            return newState;
        });
    },

    // Navigate to previous step
    previousStep: () => {
        beliefsExplorerState.update(state => {
            const newState = {
                ...state,
                currentStepIndex: Math.max(state.currentStepIndex - 1, 0)
            };
            autoSave(newState);
            return newState;
        });
    },

    // Go to specific step
    goToStep: (stepIndex: number) => {
        beliefsExplorerState.update(state => {
            const newState = {
                ...state,
                currentStepIndex: Math.max(0, Math.min(stepIndex, 5))
            };
            autoSave(newState);
            return newState;
        });
    },

    // Select a core dynamic (Step 1)
    selectDynamic: (dynamic: string) => {
        beliefsExplorerState.update(state => {
            const newState = {
                ...state,
                selectedDynamic: dynamic
            };
            autoSave(newState);
            return newState;
        });
    },

    // Toggle belief selection (Step 2)
    toggleBelief: (belief: string) => {
        beliefsExplorerState.update(state => {
            const isSelected = state.selectedBeliefs.includes(belief);
            const newBeliefs = isSelected
                ? state.selectedBeliefs.filter(b => b !== belief)
                : [...state.selectedBeliefs, belief];
			
            const newState = {
                ...state,
                selectedBeliefs: newBeliefs
            };
            autoSave(newState);
            return newState;
        });
    },

    // Select theme (Step 3)
    selectTheme: (theme: string) => {
        beliefsExplorerState.update(state => {
            const newState = {
                ...state,
                selectedTheme: theme
            };
            autoSave(newState);
            return newState;
        });
    },

    // Add goal (Step 4)
    addGoal: (goalText: string) => {
        beliefsExplorerState.update(state => {
            if (state.goals.length >= 5) return state; // Max 5 goals
			
            const newGoal: Goal = {
                id: `goal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                text: goalText,
                whyChain: []
            };
			
            const newState = {
                ...state,
                goals: [...state.goals, newGoal]
            };
            autoSave(newState);
            return newState;
        });
    },

    // Update goal (Step 4)
    updateGoal: (goalId: string, goalText: string) => {
        beliefsExplorerState.update(state => {
            const newState = {
                ...state,
                goals: state.goals.map(goal => 
                    goal.id === goalId ? { ...goal, text: goalText } : goal
                )
            };
            autoSave(newState);
            return newState;
        });
    },

    // Remove goal (Step 4)
    removeGoal: (goalId: string) => {
        beliefsExplorerState.update(state => {
            const newState = {
                ...state,
                goals: state.goals.filter(goal => goal.id !== goalId),
                beliefAlignments: state.beliefAlignments.filter(alignment => alignment.goalId !== goalId)
            };
            autoSave(newState);
            return newState;
        });
    },

    // Add why reason to goal (Step 5)
    addWhyReason: (goalId: string, reason: string) => {
        beliefsExplorerState.update(state => {
            const newState = {
                ...state,
                goals: state.goals.map(goal => 
                    goal.id === goalId 
                        ? { ...goal, whyChain: [...goal.whyChain, reason] }
                        : goal
                )
            };
            autoSave(newState);
            return newState;
        });
    },

    // Set belief alignment (Step 6)
    setBeliefAlignment: (beliefId: string, goalId: string, supports: boolean) => {
        beliefsExplorerState.update(state => {
            const existingIndex = state.beliefAlignments.findIndex(
                alignment => alignment.beliefId === beliefId && alignment.goalId === goalId
            );
			
            let newAlignments;
            if (existingIndex >= 0) {
                newAlignments = state.beliefAlignments.map((alignment, index) =>
                    index === existingIndex ? { ...alignment, supports } : alignment
                );
            } else {
                newAlignments = [...state.beliefAlignments, { beliefId, goalId, supports }];
            }
			
            const newState = {
                ...state,
                beliefAlignments: newAlignments
            };
            autoSave(newState);
            return newState;
        });
    },

    // Complete session and save to history
    completeSession: () => {
        beliefsExplorerState.update(state => {
            if (!state.sessionId) return state;
			
            const completedSession: SessionHistory = {
                id: state.sessionId,
                timestamp: new Date(),
                coreDynamic: state.selectedDynamic,
                selectedBeliefs: state.selectedBeliefs,
                selectedTheme: state.selectedTheme,
                goals: state.goals,
                beliefAlignments: state.beliefAlignments,
                completed: true
            };
			
            // Save to history
            const newHistory = [...state.sessionHistory, completedSession];
			
            // Save to localStorage
            try {
                localStorage.setItem('beliefs_session_history', JSON.stringify(newHistory));
            } catch (error) {
                console.warn('Failed to save session history:', error);
            }
			
            return {
                ...state,
                sessionHistory: newHistory
            };
        });
    },

    // Load session history from localStorage
    loadSessionHistory: () => {
        try {
            const saved = localStorage.getItem('beliefs_session_history');
            if (saved) {
                const history: SessionHistory[] = JSON.parse(saved);
                beliefsExplorerState.update(state => ({
                    ...state,
                    sessionHistory: history
                }));
            }
        } catch (error) {
            console.warn('Failed to load session history:', error);
        }
    },

    // Toggle favorite status of a flashcard (legacy support)
    toggleFavorite: (flashcardId: string) => {
        beliefsExplorerState.update(state => {
            const isStarred = state.starredFlashcards.includes(flashcardId);
            const newStarredFlashcards = isStarred
                ? state.starredFlashcards.filter(id => id !== flashcardId)
                : [...state.starredFlashcards, flashcardId];
			
            return {
                ...state,
                starredFlashcards: newStarredFlashcards
            };
        });
    },

    // Reset the explorer to initial state
    reset: () => {
        beliefsExplorerState.set(initialState);
    }
};

// Subscribe to state changes for debugging
beliefsExplorerState.subscribe(state => {
    console.log('Beliefs Explorer state updated:', state);
});