import { supabase } from './services/supabase/supabaseClient';
import { goto } from '$app/navigation';
import { waitForAuthInit } from './stores/auth';
import type { User, Session } from '@supabase/supabase-js';

export interface AuthState {
	user: User | null;
	session: Session | null;
	loading: boolean;
}

/**
 * Get the current authentication session
 */
export async function getAuthSession(): Promise<{ user: User | null; session: Session | null }> {
    const { data, error } = await supabase.auth.getSession();
	
    if (error) {
        console.error('Error getting auth session:', error);
        return { user: null, session: null };
    }
	
    return {
        user: data.session?.user || null,
        session: data.session || null
    };
}

/**
 * Sign out the current user
 * Note: Redirect is handled automatically by the auth store's onAuthStateChange listener
 */
export async function signOut(): Promise<void> {
    const { error } = await supabase.auth.signOut();
	
    if (error) {
        console.error('Error signing out:', error);
        throw error;
    }
	
    // No manual redirect needed - the auth store will handle this automatically
    // when it detects the SIGNED_OUT event
}

/**
 * Check if user is authenticated and redirect if not
 */
export async function requireAuth(): Promise<{ user: User; session: Session } | null> {
    const authState = await waitForAuthInit();
	
    if (!authState.user || !authState.session) {
        await goto('/auth/signin');
        return null;
    }
	
    return { user: authState.user, session: authState.session };
}

/**
 * Send OTP for signup (creates new users)
 */
export async function sendSignupOtp(email: string): Promise<void> {
    const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
            shouldCreateUser: true // Allow creating new users
            // No emailRedirectTo - this sends only OTP code, no magic link
        }
    });

    if (error) {
        throw error;
    }
}

/**
 * Send OTP for signin (existing users only)
 */
export async function sendSigninOtp(email: string): Promise<void> {
    const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
            shouldCreateUser: false // Only allow existing users to sign in
            // No emailRedirectTo - this sends only OTP code, no magic link
        }
    });

    if (error) {
        // Handle the specific case where user doesn't exist
        if (error.message?.includes('Signups not allowed for otp')) {
            const customError = new Error('No account found with that email. Please confirm you entered the correct email address or sign up for a new account.');
            throw customError;
        }
        throw error;
    }
}

/**
 * Send OTP for authentication (signup or signin) - deprecated, use specific functions
 * @deprecated Use sendSignupOtp() or sendSigninOtp() instead
 */
export async function sendOtp(email: string): Promise<void> {
    return sendSignupOtp(email);
}

/**
 * Verify OTP token
 */
export async function verifyOtp(email: string, token: string): Promise<void> {
    const { error } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email'
    });

    if (error) {
        throw error;
    }
}

/**
 * Listen to auth state changes
 */
export function onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
}