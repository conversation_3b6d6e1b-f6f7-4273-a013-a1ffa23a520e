<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import BreathingFocusWord from './BreathingFocusWord.svelte';

    export let inhaleDuration: number;
    export let holdDuration: number;
    export let exhaleDuration: number;
    export let relaxDuration: number;
    export let minSize: number;
    export let maxSize: number;
    export let animationStartTime: number | undefined = undefined;
    export let focusWords: string[];
    export let countdown: number;
    // Internal countdown state that will be decremented
    let countdownValue = countdown;
    // Rename internal animation start ref to avoid collision with prop
    let animationStart: number | undefined = animationStartTime;

    // Full breathing cycle duration
    $: breathingCycleDuration = inhaleDuration + holdDuration + exhaleDuration + relaxDuration;

    // Time tracking via requestAnimationFrame
    let now = performance.now() / 1000;
    let rafId: number;
    let countdownInterval: number | null = null;

    function tick(): void {
        now = performance.now() / 1000;
        rafId = requestAnimationFrame(tick);
    }

    function startCountdown(): void {
        if (countdownInterval !== null) clearInterval(countdownInterval);

        countdownInterval = window.setInterval(() => {
            if (countdownValue > 1) {
                countdownValue--;
            } else {
                countdownValue = 0;
                clearInterval(countdownInterval as number);
                countdownInterval = null;

                // Use nullish coalescing assignment
                animationStart ??= performance.now() / 1000;
            }
        }, 1000);
    }

    onMount(() => {
        rafId = requestAnimationFrame(tick);

        if (countdownValue > 0) {
            startCountdown();
        }
    });

    onDestroy(() => {
        cancelAnimationFrame(rafId);
        if (countdownInterval !== null) clearInterval(countdownInterval);
    });

    // Compute elapsed and cycle time using the renamed ref
    $: elapsed = now - (animationStart ?? now);
    $: cycleTime = (() => {
        const mod = elapsed % breathingCycleDuration;
        return mod < 0 ? mod + breathingCycleDuration : mod;
    })();

    // Determine current phase label
    $: phase = (() => {
        const t = cycleTime;
        if (t < inhaleDuration) return 'Inhale';
        if (t < inhaleDuration + holdDuration) return 'Hold';
        if (t < inhaleDuration + holdDuration + exhaleDuration) return 'Exhale';
        return 'Breathe normally';
    })();

    // Circle size interpolation
    $: size = (() => {
        const t = cycleTime;
        if (t < inhaleDuration) {
            const f = t / inhaleDuration;
            return minSize + (maxSize - minSize) * f;
        }
        if (t < inhaleDuration + holdDuration) return maxSize;
        if (t < inhaleDuration + holdDuration + exhaleDuration) {
            const f = (t - inhaleDuration - holdDuration) / exhaleDuration;
            return maxSize - (maxSize - minSize) * f;
        }
        return minSize;
    })();

    // Remaining time in current phase
    $: remaining = (() => {
        const t = cycleTime;
        if (t < inhaleDuration) return inhaleDuration - t;
        if (t < inhaleDuration + holdDuration) return inhaleDuration + holdDuration - t;
        if (t < inhaleDuration + holdDuration + exhaleDuration)
            return inhaleDuration + holdDuration + exhaleDuration - t;
        return breathingCycleDuration - t;
    })();

    // Radial gradient style using CSS custom properties for theming
    const gradientStyle = `background: radial-gradient(circle at center, var(--primary-500) 0%, var(--primary-300) 100%); border-radius: 9999px; opacity: 0.8;`;
</script>

<div class="flex flex-col w-full h-fulls">
    <!-- Top section: fixed half-screen height, circle always centered here -->
    <div class="h-[50vh] flex items-center justify-center">
        <!-- Countdown Phase -->
        {#if countdownValue > 0}
            <div class="flex items-center justify-center">
                <span class="text-6xl text-foreground">{countdownValue}</span>
            </div>
        {:else}
            <!-- Circle + Phase Text -->
            <div
                class="relative"
                style="width: {size}px; height: {size}px"
            >
                <div class="absolute inset-0" style={gradientStyle}></div>
                <div class="absolute inset-0 z-10 flex flex-col items-center justify-center text-center">
                    <p class="text-primary-foreground font-semibold">{phase}</p>
                    <p class="text-primary-foreground text-sm">{Math.ceil(remaining)}</p>
                </div>
            </div>
        {/if}
    </div>

    <!-- Bottom section: focus words (and anything else) -->
    <div class="w-full flex justify-center py-4">
        <BreathingFocusWord
            {focusWords}
            {cycleTime}
            {inhaleDuration}
            {breathingCycleDuration}
            {elapsed}
        />
    </div>
</div>