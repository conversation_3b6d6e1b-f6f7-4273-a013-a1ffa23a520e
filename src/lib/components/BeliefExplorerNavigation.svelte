<script lang="ts">
    import { beliefsExplorerActions } from '../stores/beliefsExplorer';
    import type { BeliefExplorerStep } from '../types/beliefs';

    export let steps: BeliefExplorerStep[];
    export let currentStepIndex: number;
    export let canProceed: boolean = true;

    $: isFirstStep = currentStepIndex === 0;
    $: isLastStep = currentStepIndex === steps.length - 1;
    $: currentStepName = steps[currentStepIndex]?.name || '';

    function handlePrevious() {
        if (!isFirstStep) {
            beliefsExplorerActions.previousStep();
        }
    }

    function handleNext() {
        if (!isLastStep && canProceed) {
            beliefsExplorerActions.nextStep();
        }
    }
</script>

<div class="py-5 border-t border-border mt-6">
    <div class="flex justify-between items-center gap-4 md:flex-row flex-col">
        <button
            type="button"
            on:click={handlePrevious}
            disabled={isFirstStep}
            class="flex items-center gap-2 px-5 py-3 border border-border rounded-lg bg-background text-foreground text-sm font-medium cursor-pointer transition-all duration-200 min-w-[100px] hover:bg-muted hover:border-primary disabled:opacity-50 disabled:cursor-not-allowed md:w-auto w-full justify-center"
        >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="15,18 9,12 15,6"></polyline>
            </svg>
            Previous
        </button>

        <div class="flex flex-col items-center text-center flex-1 max-w-[200px] md:order-none -order-1 md:mb-0 mb-2">
            <span class="text-xs text-muted-foreground font-medium">{currentStepIndex + 1} of {steps.length}</span>
            <span class="text-base text-foreground font-semibold mt-1">{currentStepName}</span>
        </div>

        <button
            type="button"
            on:click={handleNext}
            disabled={isLastStep || !canProceed}
            class="flex items-center gap-2 px-5 py-3 border rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 min-w-[100px] md:w-auto w-full justify-center {(isLastStep || !canProceed) ? 'opacity-50 cursor-not-allowed border-border bg-background text-foreground' : 'bg-primary text-primary-foreground border-primary hover:bg-primary/90'}"
        >
            {isLastStep ? 'Complete' : 'Next'}
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
        </button>
    </div>
</div>