<script lang="ts">
    import { tick } from 'svelte';

    export let focusWords: string[];
    export let cycleTime: number;
    export let inhaleDuration: number;
    export let breathingCycleDuration: number;
    export let elapsed: number;

    // State
    let currentWordIndex = 0;
    let wordOpacity = 0;
    let transitionDuration = '0s';

    // seconds before end-of-cycle to start fading out
    const fadeOutOffset = 8;

    // Reactive statement to watch cycleTime changes
    $: updateWordAnimation(cycleTime);

    async function updateWordAnimation(t: number) {
        const total = breathingCycleDuration;
        const fadeStart = total - fadeOutOffset;
        const cycles = Math.floor(elapsed / total);

        // advance word on each inhale
        currentWordIndex = cycles % focusWords.length;

        if (t < inhaleDuration) {
            // 1️⃣ fade-in over the full inhaleDuration
            transitionDuration = `${inhaleDuration}s`;
            await tick();
            wordOpacity = 1;
        } else if (t < fadeStart) {
            // 2️⃣ hold fully visible (no transition)
            transitionDuration = '0s';
            await tick();
            wordOpacity = 1;
        } else {
            // 3️⃣ fade-out over fadeOutOffset seconds
            transitionDuration = `${fadeOutOffset}s`;
            await tick();
            wordOpacity = 0;
        }
    }

    $: wordStyle = `opacity: ${wordOpacity}; transition: opacity ${transitionDuration} ease-in-out`;
</script>

<p
    class="text-4xl font-medium text-foreground text-center break-words"
    style={wordStyle}
>
    {focusWords[currentWordIndex]}
</p>