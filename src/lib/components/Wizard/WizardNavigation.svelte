<script lang="ts">
    import { fade } from 'svelte/transition';
	
    export let currentStep: number = 0;
    export let totalSteps: number = 6;
    export let canProceed: boolean = false;
    export let canGoBack: boolean = true;
    export let isComplete: boolean = false;

    // Callback props to replace createEventDispatcher
    export let onNext: (() => void) | undefined = undefined;
    export let onBack: (() => void) | undefined = undefined;
    export let onComplete: (() => void) | undefined = undefined;
	
    $: isFirstStep = currentStep === 0;
    $: isLastStep = currentStep === totalSteps - 1;
    $: showBackButton = canGoBack && !isFirstStep;
    $: showNextButton = !isLastStep && canProceed;
    $: showCompleteButton = isLastStep && canProceed;
	
    function handleNext() {
        if (canProceed) {
            onNext?.();
        }
    }
 
    function handleBack() {
        if (canGoBack && !isFirstStep) {
            onBack?.();
        }
    }
 
    function handleComplete() {
        if (canProceed) {
            onComplete?.();
        }
    }
	
    function handleKeydown(e: KeyboardEvent) {
        if (e.key === 'Enter' && canProceed) {
            if (isLastStep) {
                handleComplete();
            } else {
                handleNext();
            }
        } else if (e.key === 'Escape' && showBackButton) {
            handleBack();
        }
    }
</script>

<svelte:window on:keydown={handleKeydown} />

<div class="wizard-navigation">
    <div class="nav-container">
        <!-- Back button -->
        {#if showBackButton}
            <button 
                class="nav-button back"
                on:click={handleBack}
                transition:fade={{ duration: 200 }}
            >
                <div class="button-icon">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.5 15L7.5 10L12.5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="button-text">Back</span>
            </button>
        {:else}
            <div class="nav-spacer"></div>
        {/if}
		
        <!-- Step indicator -->
        <div class="step-info">
            <span class="step-text">Step {currentStep + 1} of {totalSteps}</span>
        </div>
		
        <!-- Next/Complete button -->
        {#if showNextButton}
            <button 
                class="nav-button next"
                class:disabled={!canProceed}
                on:click={handleNext}
                disabled={!canProceed}
                transition:fade={{ duration: 200 }}
            >
                <span class="button-text">Next</span>
                <div class="button-icon">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7.5 15L12.5 10L7.5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </button>
        {:else if showCompleteButton}
            <button 
                class="nav-button complete"
                class:disabled={!canProceed}
                on:click={handleComplete}
                disabled={!canProceed}
                transition:fade={{ duration: 200 }}
            >
                <span class="button-text">Complete</span>
                <div class="button-icon">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.6667 5L7.50004 14.1667L3.33337 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </button>
        {:else}
            <div class="nav-spacer"></div>
        {/if}
    </div>
	
    <!-- Progress hint -->
    {#if !canProceed && !isComplete}
        <div class="progress-hint" transition:fade={{ duration: 300 }}>
            <p class="hint-text">
                {#if currentStep === 0}
                    Select a core dynamic to continue
                {:else if currentStep === 1}
                    Select at least one belief to continue
                {:else if currentStep === 2}
                    Choose a theme to continue
                {:else if currentStep === 3}
                    Add at least one goal to continue
                {:else if currentStep === 4}
                    Complete the why exploration for your goals
                {:else if currentStep === 5}
                    Evaluate all beliefs against your goals
                {/if}
            </p>
        </div>
    {/if}
</div>

