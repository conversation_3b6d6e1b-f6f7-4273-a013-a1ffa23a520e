<script lang="ts">
    import { fade, slide, scale } from 'svelte/transition';
    import { flip } from 'svelte/animate';
    import type { Goal } from '$lib/types/beliefs';
	
    export let goals: Goal[] = [];

    // Callback props to replace createEventDispatcher
    export let onAdd: ((data: { goalText: string }) => void) | undefined = undefined;
    export let onUpdate: ((data: { goalId: string; goalText: string }) => void) | undefined = undefined;
    export let onRemove: ((data: { goalId: string }) => void) | undefined = undefined;
	
    let newGoalText = '';
    let editingGoalId: string | null = null;
    let editingText = '';
	
    $: canAddMore = goals.length < 5;
    $: hasGoals = goals.length > 0;
	
    function addGoal() {
        if (newGoalText.trim() && canAddMore) {
            onAdd?.({ goalText: newGoalText.trim() });
            newGoalText = '';
        }
    }
	
    function startEditing(goal: Goal) {
        editingGoalId = goal.id;
        editingText = goal.text;
    }
	
    function saveEdit() {
        if (editingGoalId && editingText.trim()) {
            onUpdate?.({ goalId: editingGoalId, goalText: editingText.trim() });
        }
        cancelEdit();
    }
	
    function cancelEdit() {
        editingGoalId = null;
        editingText = '';
    }
	
    function removeGoal(goalId: string) {
        onRemove?.({ goalId });
    }
	
    function handleKeydown(e: KeyboardEvent, action: 'add' | 'edit') {
        if (e.key === 'Enter') {
            e.preventDefault();
            if (action === 'add') {
                addGoal();
            } else {
                saveEdit();
            }
        } else if (e.key === 'Escape' && action === 'edit') {
            cancelEdit();
        }
    }
</script>

<div class="goal-input">
    <div class="intro-text">
        <p class="text-base text-muted-foreground leading-relaxed text-center max-w-2xl mx-auto">
            Setting clear goals helps focus your mind. Write down up to 5 goals or outcomes you'd like to accomplish through this process.
        </p>
    </div>
	
    <div class="prompt">
        <h2 class="text-2xl font-semibold text-foreground text-center mb-8">
            Add a goal or outcome you want to achieve
        </h2>
    </div>
	
    <!-- Existing goals -->
    {#if hasGoals}
        <div class="goals-list" transition:fade={{ duration: 300 }}>
            <h3 class="list-title">Your Goals</h3>
            <div class="goals-container">
                {#each goals as goal, index (goal.id)}
                    <div 
                        class="goal-item"
                        animate:flip={{ duration: 300 }}
                        transition:slide={{ duration: 300 }}
                    >
                        <div class="goal-number">
                            {index + 1}
                        </div>
						
                        {#if editingGoalId === goal.id}
                            <!-- Edit mode -->
                            <div class="goal-edit">
                                <textarea
                                    bind:value={editingText}
                                    class="edit-input"
                                    placeholder="Enter your goal..."
                                    on:keydown={(e) => handleKeydown(e, 'edit')}
                                    aria-label="Edit goal text"
                                ></textarea>
                                <div class="edit-actions">
                                    <button class="action-btn save" on:click={saveEdit} aria-label="Save goal changes">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13.3333 4L6.00004 11.3333L2.66671 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                    <button class="action-btn cancel" on:click={cancelEdit} aria-label="Cancel goal editing">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        {:else}
                            <!-- Display mode -->
                            <div class="goal-content">
                                <p class="goal-text">{goal.text}</p>
                                <div class="goal-actions">
                                    <button
                                        class="action-btn edit"
                                        on:click={() => startEditing(goal)}
                                        aria-label="Edit goal"
                                    >
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M11.3333 2.00004C11.5085 1.82494 11.7163 1.68605 11.9451 1.59129C12.1738 1.49653 12.4191 1.44775 12.6667 1.44775C12.9142 1.44775 13.1595 1.49653 13.3882 1.59129C13.617 1.68605 13.8248 1.82494 14 2.00004C14.1751 2.17513 14.314 2.38297 14.4088 2.61171C14.5035 2.84044 14.5523 3.08578 14.5523 3.33337C14.5523 3.58096 14.5035 3.8263 14.4088 4.05504C14.314 4.28377 14.1751 4.49161 14 4.66671L5.00001 13.6667L1.33334 14.6667L2.33334 11L11.3333 2.00004Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                    <button
                                        class="action-btn delete"
                                        on:click={() => removeGoal(goal.id)}
                                        aria-label="Remove goal"
                                    >
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M2 4H14M12.6667 4V13.3333C12.6667 13.6869 12.5262 14.0261 12.2761 14.2761C12.0261 14.5262 11.6869 14.6667 11.3333 14.6667H4.66667C4.31305 14.6667 3.97391 14.5262 3.72386 14.2761C3.47381 14.0261 3.33333 13.6869 3.33333 13.3333V4M5.33333 4V2.66667C5.33333 2.31305 5.47381 1.97391 5.72386 1.72386C5.97391 1.47381 6.31305 1.33333 6.66667 1.33333H9.33333C9.68696 1.33333 10.0261 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        {/if}
                    </div>
                {/each}
            </div>
        </div>
    {/if}
	
    <!-- Add new goal -->
    {#if canAddMore}
        <div class="add-goal-section" transition:fade={{ duration: 300 }}>
            <div class="add-goal-container">
                <div class="input-wrapper">
                    <textarea
                        bind:value={newGoalText}
                        placeholder="I want to feel more confident during meetings..."
                        class="goal-input"
                        rows="3"
                        on:keydown={(e) => handleKeydown(e, 'add')}
                    ></textarea>
                    <button 
                        class="add-button"
                        on:click={addGoal}
                        disabled={!newGoalText.trim()}
                    >
                        <span class="add-icon" transition:scale={{ duration: 200 }}>
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10 4.16667V15.8333M4.16667 10H15.8333" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>
                        Add Goal
                    </button>
                </div>
            </div>
        </div>
    {:else}
        <div class="max-goals-message" transition:fade={{ duration: 300 }}>
            <p class="text-sm text-muted-foreground text-center">
                You've reached the maximum of 5 goals. You can edit or remove existing goals to add new ones.
            </p>
        </div>
    {/if}
	
    <!-- Progress indicator -->
    {#if hasGoals}
        <div class="progress-section" transition:fade={{ duration: 300 }}>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {(goals.length / 5) * 100}%"></div>
            </div>
            <p class="progress-text">
                {goals.length} of 5 goals added
            </p>
        </div>
    {/if}
</div>

