<script lang="ts">
    export let steps: { name: string; path: string }[];
    export let currentStepIndex: number;
</script>

<div class="flex items-center justify-center gap-2 mb-5 w-full max-w-sm mx-auto">
    {#each steps.map((_, i) => i) as i (i)}
        <div
            class="h-2 rounded-full flex-grow transition-all duration-300
                {i <= currentStepIndex ? 'bg-primary' : 'bg-muted dark:bg-border'}"
        ></div>
    {/each}
</div>