<script lang="ts">
    import { fade, slide } from 'svelte/transition';
    import type { Goal, BeliefAlignment } from '$lib/types/beliefs';
	
    export let goals: Goal[] = [];
    export let selectedBeliefs: string[] = [];
    export let beliefAlignments: BeliefAlignment[] = [];

    // Callback prop to replace createEventDispatcher
    export let onSetAlignment: ((data: { beliefId: string; goalId: string; supports: boolean }) => void) | undefined = undefined;
	
    let currentGoalIndex = 0;
    let selectedBeliefIndex: number | null = null;
	
    $: currentGoal = goals[currentGoalIndex];
    $: hasMoreGoals = currentGoalIndex < goals.length - 1;
    $: currentBeliefAlignments = beliefAlignments.filter(a => a.goalId === currentGoal?.id);
    $: completedBeliefs = currentBeliefAlignments.length;
    $: totalBeliefs = selectedBeliefs.length;
    $: isGoalComplete = completedBeliefs === totalBeliefs;
    $: allGoalsComplete = goals.every(goal => 
        beliefAlignments.filter(a => a.goalId === goal.id).length === selectedBeliefs.length
    );
	
    function selectBelief(index: number) {
        selectedBeliefIndex = index;
    }
	
    function setBeliefAlignment(supports: boolean) {
        if (selectedBeliefIndex !== null && currentGoal) {
            const belief = selectedBeliefs[selectedBeliefIndex];
            onSetAlignment?.({
                beliefId: belief,
                goalId: currentGoal.id,
                supports
            });
   
            // Auto-advance to next belief
            if (selectedBeliefIndex < selectedBeliefs.length - 1) {
                selectedBeliefIndex++;
            } else {
                selectedBeliefIndex = null;
            }
        }
    }
	
    function getBeliefAlignment(belief: string): BeliefAlignment | undefined {
        return currentBeliefAlignments.find(a => a.beliefId === belief);
    }
	
    function nextGoal() {
        if (hasMoreGoals) {
            currentGoalIndex++;
            selectedBeliefIndex = null;
        }
    }
	
    function previousGoal() {
        if (currentGoalIndex > 0) {
            currentGoalIndex--;
            selectedBeliefIndex = null;
        }
    }
	
    function goToGoal(index: number) {
        currentGoalIndex = index;
        selectedBeliefIndex = null;
    }
	
    // Get summary statistics
    $: summary = {
        supportive: beliefAlignments.filter(a => a.supports).length,
        hindering: beliefAlignments.filter(a => !a.supports).length,
        total: beliefAlignments.length
    };
</script>

<div class="alignment-review">
    <div class="intro-text">
        <p class="text-base text-muted-foreground leading-relaxed text-center max-w-2xl mx-auto">
            Now let's see how your beliefs relate to each goal. A belief can either support a goal (help achieve it) or hinder it. Identifying conflicts can show what you might want to work on.
        </p>
    </div>
	
    <!-- Goal navigation -->
    {#if goals.length > 1}
        <div class="goal-navigation">
            <div class="goal-tabs">
                {#each goals as goal, index (goal.id)}
                    <button
                        class="goal-tab"
                        class:active={index === currentGoalIndex}
                        class:completed={beliefAlignments.filter(a => a.goalId === goal.id).length === selectedBeliefs.length}
                        on:click={() => goToGoal(index)}
                    >
                        <span class="tab-number">{index + 1}</span>
                        <span class="tab-text">{goal.text.length > 25 ? goal.text.substring(0, 25) + '...' : goal.text}</span>
                        <div class="tab-progress">
                            {beliefAlignments.filter(a => a.goalId === goal.id).length}/{selectedBeliefs.length}
                        </div>
                    </button>
                {/each}
            </div>
        </div>
    {/if}
	
    {#if currentGoal}
        <div class="current-goal-section" transition:fade={{ duration: 300 }}>
            <!-- Current goal display -->
            <div class="goal-display">
                <h2 class="goal-title">Goal {currentGoalIndex + 1}</h2>
                <p class="goal-text">"{currentGoal.text}"</p>
                <div class="goal-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {(completedBeliefs / totalBeliefs) * 100}%"></div>
                    </div>
                    <p class="progress-text">{completedBeliefs} of {totalBeliefs} beliefs evaluated</p>
                </div>
            </div>
			
            <!-- Instruction -->
            <div class="instruction-section">
                <h3 class="instruction-text">Tap a belief below to evaluate it against your goal</h3>
            </div>
			
            <!-- Beliefs list -->
            <div class="beliefs-section">
                <div class="beliefs-grid">
                    {#each selectedBeliefs as belief, index (belief)}
                        {@const alignment = getBeliefAlignment(belief)}
                        <button
                            class="belief-card"
                            class:selected={selectedBeliefIndex === index}
                            class:evaluated={alignment}
                            class:supports={alignment?.supports}
                            class:hinders={alignment && !alignment.supports}
                            on:click={() => selectBelief(index)}
                        >
                            <div class="belief-content">
                                <p class="belief-text">"{belief}"</p>
								
                                <!-- Status indicator -->
                                <div class="status-indicator">
                                    {#if alignment}
                                        <div class="status-icon" class:supports={alignment.supports} class:hinders={!alignment.supports}>
                                            {#if alignment.supports}
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M16.6667 5L7.50004 14.1667L3.33337 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            {:else}
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            {/if}
                                        </div>
                                    {:else}
                                        <div class="pending-indicator">?</div>
                                    {/if}
                                </div>
                            </div>
                        </button>
                    {/each}
                </div>
            </div>
			
            <!-- Evaluation section -->
            {#if selectedBeliefIndex !== null}
                <div class="evaluation-section" transition:slide={{ duration: 300 }}>
                    <div class="evaluation-container">
                        <h3 class="evaluation-question">
                            Does this belief support or hinder your goal?
                        </h3>
                        <p class="selected-belief">"{selectedBeliefs[selectedBeliefIndex]}"</p>
						
                        <div class="evaluation-buttons">
                            <button 
                                class="eval-button supports"
                                on:click={() => setBeliefAlignment(true)}
                            >
                                <div class="button-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="button-content">
                                    <span class="button-title">Supports</span>
                                    <span class="button-subtitle">Helps achieve this goal</span>
                                </div>
                            </button>
							
                            <button 
                                class="eval-button hinders"
                                on:click={() => setBeliefAlignment(false)}
                            >
                                <div class="button-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="button-content">
                                    <span class="button-title">Hinders</span>
                                    <span class="button-subtitle">Blocks or limits this goal</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            {/if}
			
            <!-- Navigation and summary -->
            <div class="navigation-section">
                {#if isGoalComplete}
                    <div class="completion-message" transition:fade={{ duration: 300 }}>
                        <div class="message-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <p class="message-text">
                            Goal evaluation complete! 
                            {hasMoreGoals ? 'Ready for the next goal?' : 'All goals evaluated!'}
                        </p>
                    </div>
                {/if}
				
                <div class="nav-buttons">
                    {#if currentGoalIndex > 0}
                        <button class="nav-button secondary" on:click={previousGoal}>
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12.5 15L7.5 10L12.5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Previous Goal
                        </button>
                    {/if}
					
                    {#if hasMoreGoals && isGoalComplete}
                        <button class="nav-button primary" on:click={nextGoal}>
                            Next Goal
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 15L12.5 10L7.5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    {/if}
                </div>
            </div>
        </div>
    {/if}
	
    <!-- Final summary -->
    {#if allGoalsComplete && summary.total > 0}
        <div class="final-summary" transition:fade={{ duration: 500 }}>
            <h3 class="summary-title">Your Belief-Goal Alignment Summary</h3>
            <div class="summary-stats">
                <div class="stat-item supports">
                    <div class="stat-number">{summary.supportive}</div>
                    <div class="stat-label">Supportive Beliefs</div>
                </div>
                <div class="stat-item hinders">
                    <div class="stat-number">{summary.hindering}</div>
                    <div class="stat-label">Hindering Beliefs</div>
                </div>
            </div>
            <p class="summary-insight">
                {#if summary.supportive > summary.hindering}
                    Great! You have more supportive beliefs than hindering ones. Focus on strengthening these positive beliefs.
                {:else if summary.hindering > summary.supportive}
                    You've identified some beliefs that may be holding you back. This awareness is the first step toward change.
                {:else}
                    You have a balanced mix of supportive and hindering beliefs. Consider which ones serve you best.
                {/if}
            </p>
        </div>
    {/if}
</div>

