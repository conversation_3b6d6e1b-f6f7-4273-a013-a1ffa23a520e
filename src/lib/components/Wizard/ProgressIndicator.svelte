<script lang="ts">
    export let current: number = 1;
    export let total: number = 6;
    export let showLabels: boolean = false;
	
    $: steps = Array.from({ length: total }, (_, i) => i + 1);
</script>

<div class="progress-container">
    {#if showLabels}
        <div class="progress-label">
            <span class="text-sm text-muted-foreground">Step {current} of {total}</span>
        </div>
    {/if}
	
    <!-- Dots Progress Indicator -->
    <div class="progress-dots">
        {#each steps as step (step)}
            <div 
                class="progress-dot"
                class:active={step <= current}
                class:current={step === current}
            >
                <div class="dot-inner"></div>
            </div>
        {/each}
    </div>
</div>

