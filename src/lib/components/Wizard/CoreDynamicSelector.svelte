<script lang="ts">
    import { slide } from 'svelte/transition';
    import type { CoreDynamic } from '$lib/types/beliefs';
	
    export let coreDynamics: CoreDynamic[] = [];
    export let selectedDynamic: string | null = null;

    // Callback prop to replace createEventDispatcher
    export let onSelect: ((data: { dynamic: string }) => void) | undefined = undefined;
	
    function selectDynamic(dynamic: CoreDynamic) {
        selectedDynamic = dynamic.id;
        onSelect?.({ dynamic: dynamic.id });
    }
	
    // Add animation delay for staggered entrance
    function getAnimationDelay(index: number): string {
        return `${index * 100}ms`;
    }
</script>

<div class="w-full max-w-4xl mx-auto">
    <div class="mb-8">
        <p class="text-base text-muted-foreground leading-relaxed text-center max-w-2xl mx-auto">
            Often we feel uneasy without noticing the exact thoughts behind it. By observing what you're thinking right now, you can begin to understand and address your discomfort. Let's identify the main issue or core dynamic affecting you.
        </p>
    </div>
	
    <div class="mb-8">
        <h2 class="text-2xl font-semibold text-foreground text-center mb-8">
            What are you thinking right now?
        </h2>
    </div>
	
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        {#each coreDynamics as dynamic, index (dynamic.id)}
            <button
                class="relative p-6 bg-background border border-border rounded-xl text-left transition-all duration-300 cursor-pointer transform opacity-0 translate-y-5 hover:border-primary/50 hover:shadow-lg hover:shadow-primary/10 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent active:scale-95 animate-fade-in-up"
                class:border-primary={selectedDynamic === dynamic.id}
                class:bg-muted={selectedDynamic === dynamic.id}
                class:shadow-lg={selectedDynamic === dynamic.id}
                style="animation-delay: {getAnimationDelay(index)}"
                on:click={() => selectDynamic(dynamic)}
            >
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-lg font-semibold text-foreground">{dynamic.name}</h3>
                    </div>
                    <p class="text-sm text-muted-foreground leading-relaxed">{dynamic.description}</p>
                </div>
				
                <!-- Selection indicator -->
                {#if selectedDynamic === dynamic.id}
                    <div class="absolute top-4 right-4 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground animate-bounce-in">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16.6667 5L7.50004 14.1667L3.33337 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                {/if}
            </button>
        {/each}
    </div>
	
    <!-- Other option -->
    <div class="mt-6">
        <button
            class="w-full p-4 bg-muted border-2 border-dashed border-border rounded-lg transition-all duration-300 cursor-pointer text-left hover:border-primary/50 hover:bg-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            class:border-primary={selectedDynamic === 'other'}
            class:bg-muted={selectedDynamic === 'other'}
            on:click={() => selectDynamic({ id: 'other', name: 'Other', description: 'Something else' })}
        >
            <div>
                <h3 class="text-lg font-semibold text-foreground">Other</h3>
                <p class="text-sm text-muted-foreground">Something else is on my mind</p>
            </div>
        </button>
		
        {#if selectedDynamic === 'other'}
            <div class="mt-4" transition:slide={{ duration: 300 }}>
                <input
                    type="text"
                    placeholder="Describe what's on your mind..."
                    class="w-full p-4 border border-border rounded-lg bg-background text-foreground text-base placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
            </div>
        {/if}
    </div>
</div>

