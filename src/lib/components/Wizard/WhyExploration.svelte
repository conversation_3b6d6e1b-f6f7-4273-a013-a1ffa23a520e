<script lang="ts">
    import { fade, slide, scale } from 'svelte/transition';
    import { flip } from 'svelte/animate';
    import type { Goal } from '$lib/types/beliefs';
	
    export let goals: Goal[] = [];

    // Callback prop to replace createEventDispatcher
    export let onAddWhy: ((data: { goalId: string; reason: string }) => void) | undefined = undefined;
	
    let currentGoalIndex = 0;
    let whyInput = '';
	
    $: currentGoal = goals[currentGoalIndex];
    $: hasMoreGoals = currentGoalIndex < goals.length - 1;
    $: currentWhyChain = currentGoal?.whyChain || [];
    $: canAddWhy = currentWhyChain.length < 5; // Max 5 levels of why
	
    function addWhyReason() {
        if (whyInput.trim() && currentGoal && canAddWhy) {
            onAddWhy?.({ goalId: currentGoal.id, reason: whyInput.trim() });
            whyInput = '';
        }
    }
	
    function nextGoal() {
        if (hasMoreGoals) {
            currentGoalIndex++;
            whyInput = '';
        }
    }
	
    function previousGoal() {
        if (currentGoalIndex > 0) {
            currentGoalIndex--;
            whyInput = '';
        }
    }
	
    function goToGoal(index: number) {
        currentGoalIndex = index;
        whyInput = '';
    }
	
    function handleKeydown(e: KeyboardEvent) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            addWhyReason();
        }
    }
	
    // Get the current question based on the why level
    function getCurrentQuestion(): string {
        const level = currentWhyChain.length;
        if (level === 0) {
            return `Why is "${currentGoal?.text}" important to you?`;
        } else {
            const lastReason = currentWhyChain[level - 1];
            return `And why is "${lastReason}" important?`;
        }
    }
</script>

<div class="why-exploration">
    <div class="intro-text">
        <p class="text-base text-muted-foreground leading-relaxed text-center max-w-2xl mx-auto">
            Let's explore why each goal matters to you. Asking "why" multiple times can reveal the deeper reasons behind your aspirations.
        </p>
    </div>
	
    <!-- Goal navigation -->
    {#if goals.length > 1}
        <div class="goal-navigation">
            <div class="goal-tabs">
                {#each goals as goal, index (goal.id)}
                    <button
                        class="goal-tab"
                        class:active={index === currentGoalIndex}
                        class:completed={goal.whyChain.length >= 3}
                        on:click={() => goToGoal(index)}
                    >
                        <span class="tab-number">{index + 1}</span>
                        <span class="tab-text">{goal.text.length > 30 ? goal.text.substring(0, 30) + '...' : goal.text}</span>
                        {#if goal.whyChain.length >= 3}
                            <div class="completion-indicator" transition:scale={{ duration: 200 }}>
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M13.3333 4L6.00004 11.3333L2.66671 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        {/if}
                    </button>
                {/each}
            </div>
        </div>
    {/if}
	
    {#if currentGoal}
        <div class="current-goal-section" transition:fade={{ duration: 300 }}>
            <!-- Current goal display -->
            <div class="goal-display">
                <h2 class="goal-title">Goal {currentGoalIndex + 1}</h2>
                <p class="goal-text">"{currentGoal.text}"</p>
            </div>
			
            <!-- Why chain display -->
            {#if currentWhyChain.length > 0}
                <div class="why-chain" transition:slide={{ duration: 300 }}>
                    <h3 class="chain-title">Your Why Chain</h3>
                    <div class="chain-container">
                        {#each currentWhyChain as reason, index (index)}
                            <div 
                                class="why-item"
                                animate:flip={{ duration: 300 }}
                                transition:slide={{ duration: 300 }}
                            >
                                <div class="why-connector">
                                    <div class="connector-line"></div>
                                    <div class="connector-dot">
                                        <span class="dot-number">{index + 1}</span>
                                    </div>
                                </div>
                                <div class="why-content">
                                    <p class="why-text">"{reason}"</p>
                                </div>
                            </div>
                        {/each}
                    </div>
                </div>
            {/if}
			
            <!-- Current question and input -->
            {#if canAddWhy}
                <div class="question-section" transition:fade={{ duration: 300 }}>
                    <div class="question-container">
                        <h3 class="question-text">{getCurrentQuestion()}</h3>
                        <div class="input-container">
                            <textarea
                                bind:value={whyInput}
                                placeholder="Because it would make me feel..."
                                class="why-input"
                                rows="3"
                                on:keydown={handleKeydown}
                                aria-label="Enter your reason for why this goal is important"
                            ></textarea>
                            <button 
                                class="add-why-button"
                                on:click={addWhyReason}
                                disabled={!whyInput.trim()}
                            >
                                <span class="button-icon">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M4.16667 10H15.8333M10 4.16667V15.8333" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </span>
                                Add Reason
                            </button>
                        </div>
                    </div>
                </div>
            {:else}
                <div class="max-depth-message" transition:fade={{ duration: 300 }}>
                    <div class="message-container">
                        <div class="message-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <p class="message-text">
                            Great depth! You've explored this goal thoroughly. 
                            {hasMoreGoals ? 'Ready to explore the next goal?' : 'All goals explored!'}
                        </p>
                    </div>
                </div>
            {/if}
			
            <!-- Navigation buttons -->
            <div class="navigation-section">
                <div class="nav-buttons">
                    {#if currentGoalIndex > 0}
                        <button class="nav-button secondary" on:click={previousGoal}>
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12.5 15L7.5 10L12.5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Previous Goal
                        </button>
                    {/if}
					
                    {#if hasMoreGoals && currentWhyChain.length >= 3}
                        <button class="nav-button primary" on:click={nextGoal}>
                            Next Goal
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 15L12.5 10L7.5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    {/if}
                </div>
				
                {#if currentWhyChain.length >= 3}
                    <p class="completion-hint">
                        {hasMoreGoals ? 'You can continue exploring this goal or move to the next one.' : 'You\'ve completed the why exploration for all goals!'}
                    </p>
                {:else}
                    <p class="progress-hint">
                        Add at least {3 - currentWhyChain.length} more reason{3 - currentWhyChain.length === 1 ? '' : 's'} to complete this goal.
                    </p>
                {/if}
            </div>
        </div>
    {/if}
</div>

