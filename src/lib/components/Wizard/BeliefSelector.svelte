<script lang="ts">
    import { fade, scale } from 'svelte/transition';
	
    export let beliefs: string[] = [];
    export let selectedBeliefs: string[] = [];

    // Callback props to replace createEventDispatcher
    export let onToggle: ((data: { belief: string }) => void) | undefined = undefined;
    export let onAddCustom: ((data: { belief: string }) => void) | undefined = undefined;
	
    let customBelief = '';
    let showCustomInput = false;
	
    function toggleBelief(belief: string) {
        onToggle?.({ belief });
    }
 
    function addCustomBelief() {
        if (customBelief.trim()) {
            onAddCustom?.({ belief: customBelief.trim() });
            customBelief = '';
            showCustomInput = false;
        }
    }
	
    function isSelected(belief: string): boolean {
        return selectedBeliefs.includes(belief);
    }
</script>

<div class="belief-selector">
    <div class="intro-text">
        <p class="intro-paragraph">
            Our beliefs influence how we interpret events. Let's explore the thoughts or beliefs that come up for you right now. Identifying these can help you see patterns in how you're thinking.
        </p>
    </div>
	
    <div class="prompt">
        <h2 class="prompt-title">
            Which thoughts or beliefs are coming up?
        </h2>
        <p class="prompt-subtitle">
            Select any that apply to you right now
        </p>
    </div>
	
    <div class="beliefs-container">
        {#each beliefs as belief (belief)}
            <button
                class="belief-item"
                class:selected={isSelected(belief)}
                on:click={() => toggleBelief(belief)}
            >
                <div class="belief-content">
                    <span class="belief-text">{belief}</span>
					
                    <div class="selection-indicator">
                        {#if isSelected(belief)}
                            <div class="checkmark" transition:scale={{ duration: 200 }}>
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M13.3333 4L6.00004 11.3333L2.66671 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        {:else}
                            <div class="checkbox"></div>
                        {/if}
                    </div>
                </div>
            </button>
        {/each}
		
        <div class="custom-belief-section">
            {#if !showCustomInput}
                <button
                    class="add-custom-button"
                    on:click={() => showCustomInput = true}
                >
                    <div class="add-icon">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 4.16667V15.8333M4.16667 10H15.8333" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <span>Add your own belief</span>
                </button>
            {:else}
                <div class="custom-input-container" transition:fade={{ duration: 300 }}>
                    <div class="custom-input-wrapper">
                        <input
                            type="text"
                            bind:value={customBelief}
                            placeholder="Enter your belief..."
                            class="custom-input"
                            on:keydown={(e) => e.key === 'Enter' && addCustomBelief()}
                        />
                        <div class="input-actions">
                            <button
                                class="action-button confirm"
                                on:click={addCustomBelief}
                                disabled={!customBelief.trim()}
                            >
                                Add
                            </button>
                            <button
                                class="action-button cancel"
                                on:click={() => { showCustomInput = false; customBelief = ''; }}
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    </div>
	
    {#if selectedBeliefs.length > 0}
        <div class="selection-summary" transition:fade={{ duration: 300 }}>
            <p class="summary-text">
                {selectedBeliefs.length} belief{selectedBeliefs.length === 1 ? '' : 's'} selected
            </p>
        </div>
    {/if}
</div>

