<script lang="ts">
    import { beliefsExplorerActions } from '../stores/beliefsExplorer';
    import type { BeliefExplorerStep } from '../types/beliefs';

    export let steps: BeliefExplorerStep[];
    export let currentStepIndex: number;

    function goToStep(stepIndex: number) {
        beliefsExplorerActions.goToStep(stepIndex);
    }
</script>

<div class="py-5 mb-6">
    <div class="flex items-center justify-center gap-2 overflow-x-auto px-4">
        {#each steps as step, index (step.id || index)}
            <button
                class="flex flex-col items-center relative min-w-[80px] flex-shrink-0 bg-transparent border-none cursor-pointer p-1 rounded-lg transition-colors duration-200 hover:bg-muted focus:outline-2 focus:outline-primary focus:outline-offset-2 md:min-w-[80px] sm:min-w-[60px] min-w-[40px]"
                on:click={() => goToStep(index)}
                aria-label="Go to step {index + 1}: {step.name}"
            >
                <div class="w-8 h-8 rounded-full flex items-center justify-center bg-muted border-2 border-border text-muted-foreground font-semibold text-sm transition-all duration-300 mb-2 {index === currentStepIndex ? 'bg-primary border-primary text-primary-foreground scale-110' : ''} {index < currentStepIndex ? 'bg-accent border-accent text-accent-foreground' : ''} md:w-8 md:h-8 sm:w-7 sm:h-7 sm:text-xs">
                    {#if index < currentStepIndex}
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="md:w-4 md:h-4 w-3 h-3">
                            <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                    {:else}
                        <span>{index + 1}</span>
                    {/if}
                </div>
                <div class="text-center">
                    <span class="text-xs text-muted-foreground font-medium leading-tight {index === currentStepIndex ? 'text-primary font-semibold' : ''} {index < currentStepIndex ? 'text-accent' : ''} md:text-xs sm:text-[10px] sm:block hidden">{step.name}</span>
                </div>
                {#if index < steps.length - 1}
                    <div class="absolute top-4 left-[calc(100%-4px)] w-4 h-0.5 bg-border transition-colors duration-300 {index < currentStepIndex ? 'bg-accent' : ''} md:w-4 sm:w-3"></div>
                {/if}
            </button>
        {/each}
    </div>
</div>