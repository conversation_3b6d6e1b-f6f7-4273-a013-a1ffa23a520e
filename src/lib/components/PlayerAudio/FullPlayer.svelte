<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { browser } from '$app/environment';
    import { useAudioCache } from '$lib/audioCache';
    import SeekBar from './SeekBar.svelte';

    export let src: string;

    let audioRef: HTMLAudioElement | null = null;
    let isPlaying = false;
    let isLoading = false;
    let isBuffering = false;
    let currentTime = 0;
    let duration = 0;
    let bufferedTime = 0;
    let error: string | null = null;
    let tickId: number | null = null;
    let effectiveSrc: string = src;
    let isCached: boolean = false;

    // Initialize audio cache system
    const { isAudioCached, getAudioBlobUrl, preloadAudio } = useAudioCache();

    const MEDIA_ERROR_MESSAGES: Record<number, string> = {
        1: 'Network error while fetching audio.',
        2: 'Decoding error – audio not playable.',
        3: 'Format not supported by your browser.',
        4: 'Playback aborted.',
    };

    // percentage of file buffered
    $: bufferedPercent = duration > 0 ? (bufferedTime / duration) * 100 : 0;

    // pause this player if another one starts
    function onOtherPlayed(e: CustomEvent): void {
        if (e.detail !== audioRef) pause();
    }

    onMount(() => {
        if (browser) {
            window.addEventListener('audio-played', onOtherPlayed as EventListener);
			
            // Check if audio is already cached and update the source
            if (src?.startsWith('http')) {
                checkCacheAndUpdateSource(src);
    
                // Preload audio in the background for offline use
                preloadAudio(src);
            }
			
            audioRef?.load();
        }
    });

    onDestroy(() => {
        if (browser) {
            window.removeEventListener('audio-played', onOtherPlayed as EventListener);
            if (audioRef) {
                audioRef.pause();
                audioRef.src = '';
            }
            stopTicker();
        }
    });

    function formatTime(sec: number): string {
        const m = Math.floor(sec / 60);
        const s = Math.floor(sec % 60)
            .toString()
            .padStart(2, '0');
        return `${m}:${s}`;
    }

    function onLoadedMetadata(): void {
        if (audioRef) {
            duration = audioRef.duration;
        }
    }

    function updateBufferProgress(): void {
        const audio = audioRef;
        if (audio === null || audio === undefined) return;

        const buf = audio.buffered;
        if (buf.length > 0) {
            // Find the range that contains the current time
            let currentRange = -1;
            for (let i = 0; i < buf.length; i++) {
                if (audio.currentTime >= buf.start(i) && audio.currentTime <= buf.end(i)) {
                    currentRange = i;
                    break;
                }
            }

            // If we found a range containing current time, use that, otherwise use the last range
            if (currentRange !== -1) {
                bufferedTime = buf.end(currentRange);
            } else if (buf.length > 0) {
                bufferedTime = buf.end(buf.length - 1);
            }
        }
    }

    function onProgress(): void {
        updateBufferProgress();
    }

    function onTimeUpdate(): void {
        updateBufferProgress();
    }

    /**
  * Check if the audio file is cached and update the source accordingly
  */
    async function checkCacheAndUpdateSource(srcUrl: string): Promise<void> {
        if (!srcUrl?.startsWith('http')) {
            effectiveSrc = srcUrl;
            return;
        }
  
        // Check if already cached
        const cached = await isAudioCached(srcUrl);
        
        // Get the blob URL or original URL
        const blobUrl = await getAudioBlobUrl(srcUrl);
        
        // Only update if values actually changed to prevent reactive loops
        if (isCached !== cached) {
            isCached = cached;
        }
        
        if (effectiveSrc !== blobUrl) {
            effectiveSrc = blobUrl;
            
            // If the audio source changed while audio is already loaded, reload it
            if (audioRef?.src !== blobUrl) {
                audioRef?.load();
            }
        }
    }

    async function validateUrl(): Promise<boolean> {
        if (!src) {
            error = 'No audio source provided';
            return false;
        }
		
        // If it's not an HTTP URL or it's already cached, no need to validate
        if (!src.startsWith('http') || isCached) return true;
		
        try {
            const res = await fetch(src, { method: 'HEAD' });
            if (!res.ok) {
                error =
                    res.status === 404
                        ? 'File not found (404)'
                        : res.status === 403
                        ? 'Access denied (403)'
                        : `HTTP error ${res.status}`;
                return false;
            }
        } catch (e: unknown) {
            // Check if we have it cached, if so we can still play even offline
            const cached = await isAudioCached(src);
            if (cached) {
                isCached = true;
                return true;
            }
			
            const errorMessage = e instanceof Error ? e.message : String(e);
            error = `Network error: ${errorMessage}`;
            return false;
        }
        return true;
    }

    async function play(): Promise<void> {
        if (!audioRef || !browser) return;
        window.dispatchEvent(
            new CustomEvent('audio-played', { detail: audioRef })
        );

        error = null;
        isLoading = true;

        if (!(await validateUrl())) {
            isLoading = false;
            return;
        }

        try {
            await audioRef.play();
            duration = audioRef.duration ?? duration;
            isPlaying = true;
            startTicker();
        } catch (e: unknown) {
            const err = e as Error;
            error = err.message ?? 'Cannot play audio.';
        } finally {
            isLoading = false;
        }
    }

    function pause(): void {
        if (!audioRef) return;
        audioRef.pause();
        isPlaying = false;
        stopTicker();
    }

    function stop(): void {
        if (!audioRef) return;
        audioRef.pause();
        audioRef.currentTime = 0;
        isPlaying = false;
        currentTime = 0;
        stopTicker();
    }

    function togglePlay(): void {
        if (isPlaying) {
            pause();
        } else {
            play();
        }
    }

    function tick(): void {
        if (audioRef) {
            currentTime = audioRef.currentTime;
            tickId = requestAnimationFrame(tick);
        }
    }

    function startTicker(): void {
        if (tickId === null) {
            tick();
        }
    }

    function stopTicker(): void {
        if (tickId !== null) {
            cancelAnimationFrame(tickId);
            tickId = null;
        }
    }

    function onEnded(): void {
        stop();
    }

    function onMediaError(): void {
        const code = audioRef?.error?.code;
        error = code !== undefined
            ? MEDIA_ERROR_MESSAGES[code] ?? 'Unknown media error.'
            : 'Playback error.';
        isPlaying = false;
        isLoading = false;
        isBuffering = false;
        stopTicker();
    }

    function onWaiting(): void {
        isBuffering = true;
    }

    function onCanPlay(): void {
        isBuffering = false;
    }

    function onSeek(event: CustomEvent<number>): void {
        if (!audioRef) return;
        audioRef.currentTime = event.detail;
        currentTime = event.detail;
    }

    // Watch src changes - use a different approach to avoid reactive loops
    let previousSrc = '';
    
    // Use a separate function to handle src changes to avoid reactive loops
    function handleSrcChange(newSrc: string) {
        if (newSrc && newSrc !== previousSrc) {
            previousSrc = newSrc;
            error = null;
            stop();
            isCached = false;
            
            // Update the audio source when src prop changes
            if (newSrc?.startsWith('http')) {
                // Use setTimeout to break the reactive loop
                setTimeout(() => {
                    checkCacheAndUpdateSource(newSrc);
                    preloadAudio(newSrc);
                }, 0);
            } else {
                effectiveSrc = newSrc;
            }
            
            if (audioRef) {
                audioRef.load();
                duration = 0;
                bufferedTime = 0;
            }
        }
    }
    
    // Call the function when src changes
    $: handleSrcChange(src);
</script>

<div class="audio-player p-4 bg-background border border-border rounded shadow max-w-md mx-auto">
    <audio
        bind:this={audioRef}
        preload="metadata"
        on:loadedmetadata={onLoadedMetadata}
        on:progress={onProgress}
        on:timeupdate={onTimeUpdate}
        on:ended={onEnded}
        on:error={onMediaError}
        on:waiting={onWaiting}
        on:canplaythrough={onCanPlay}
    >
        <source src={effectiveSrc} />
        Audio playback not supported.
    </audio>

    <div class="controls flex items-center space-x-4 mb-2">
        <button
            disabled={isLoading || isBuffering}
            class="flex items-center px-3 py-1 bg-primary text-primary-foreground rounded disabled:opacity-50"
            on:click={togglePlay}
        >
            {#if isPlaying}
                <!-- Pause icon -->
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            {:else}
                <!-- Play icon -->
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"/>
                </svg>
            {/if}
        </button>

        <button
            disabled={(!isPlaying && currentTime === 0) || isLoading || isBuffering}
            class="flex items-center px-3 py-1 bg-destructive text-destructive-foreground rounded disabled:opacity-50"
            on:click={stop}
        >
            <!-- Stop icon -->
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"/>
            </svg>
        </button>

        <div class="flex items-center space-x-2 text-foreground">
            {#if isLoading || isBuffering}
                <!-- Loading spinner -->
                <svg class="animate-spin h-5 w-5 text-muted-foreground" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            {:else if duration}
                <div>{formatTime(currentTime)} / {formatTime(duration)}</div>
            {/if}
        </div>
		
        <!-- Offline cache status indicator -->
        {#if src && src.startsWith('http')}
            <div class="ml-auto flex items-center">
                {#if isCached}
                    <div class="text-xs px-2 py-0.5 bg-primary/10 text-primary rounded-full flex items-center">
                        <!-- Hard drive icon -->
                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"/>
                        </svg>
                        <span>Cached</span>
                    </div>
                {:else}
                    <div class="text-xs px-2 py-0.5 bg-accent text-accent-foreground rounded-full flex items-center">
                        <!-- Wifi icon -->
                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"/>
                        </svg>
                        <span>Online</span>
                    </div>
                {/if}
            </div>
        {/if}
    </div>

    <SeekBar
        {currentTime}
        {duration}
        {bufferedPercent}
        on:update:currentTime={onSeek}
    />

    {#if error}
        <div class="text-sm text-destructive">
            ⚠️ {error}
        </div>
    {/if}
</div>