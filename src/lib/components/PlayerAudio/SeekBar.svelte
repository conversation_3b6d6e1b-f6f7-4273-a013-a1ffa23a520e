<script lang="ts">
    import { onDestroy } from 'svelte';

    export let currentTime: number;
    export let duration: number;
    export let bufferedPercent: number;

    // Callback prop to replace createEventDispatcher
    export let onUpdateCurrentTime: ((time: number) => void) | undefined = undefined;

    let bar: HTMLElement | null = null;
    let isDragging = false;
    let progress = 0;

    // update play progress when audio moves (unless dragging)
    $: {
        if (!isDragging && duration > 0) {
            progress = Math.min(100, (currentTime / duration) * 100);
        }
    }

    // calculate time based on click or drag
    function calculateNewTime(clientX: number): number {
        if (!bar) return 0;
        const { left, width } = bar.getBoundingClientRect();
        let x = clientX - left;
        x = Math.max(0, Math.min(x, width));
        const ratio = x / width;
        return ratio * duration;
    }

    function onClick(e: MouseEvent): void {
        const newTime = calculateNewTime(e.clientX);
        onUpdateCurrentTime?.(newTime);
    }

    function onMouseMove(e: MouseEvent): void {
        if (!isDragging) return;
        const newTime = calculateNewTime(e.clientX);
        const clampedTime = Math.min(newTime, duration);
        progress = (clampedTime / duration) * 100;
        onUpdateCurrentTime?.(clampedTime);
    }

    function onMouseUp(): void {
        if (isDragging) {
            isDragging = false;
            window.removeEventListener('mousemove', onMouseMove);
            window.removeEventListener('mouseup', onMouseUp);
        }
    }

    function startDrag(): void {
        isDragging = true;
        window.addEventListener('mousemove', onMouseMove);
        window.addEventListener('mouseup', onMouseUp);
    }

    onDestroy(() => {
        window.removeEventListener('mousemove', onMouseMove);
        window.removeEventListener('mouseup', onMouseUp);
    });
</script>

<div class="w-full px-4 py-2">
    <div
        bind:this={bar}
        class="seek-bar relative w-full h-2 bg-muted rounded cursor-pointer"
        role="slider"
        aria-label="Audio seek bar"
        aria-valuemin="0"
        aria-valuemax={duration}
        aria-valuenow={currentTime}
        tabindex="0"
        on:click={onClick}
        on:keydown={(e) => {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                onUpdateCurrentTime?.(Math.max(0, currentTime - 5));
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                onUpdateCurrentTime?.(Math.min(duration, currentTime + 5));
            }
        }}
    >
        <!-- buffered portion -->
        <div class="absolute top-0 left-0 h-2 bg-muted-foreground/30 rounded" style="width: {bufferedPercent}%"></div>
        <!-- played portion -->
        <div class="absolute top-0 left-0 h-2 bg-primary rounded" style="width: {progress}%"></div>
        <!-- draggable handle -->
        <div
            class="absolute top-1/2 h-4 w-4 bg-background border border-border rounded-full transform -translate-y-1/2 -translate-x-1/2 cursor-grab"
            style="left: {progress}%"
            role="button"
            aria-label="Drag to seek"
            tabindex="0"
            on:mousedown|preventDefault={startDrag}
        ></div>
    </div>
</div>