<script lang="ts">
    import { onDestroy } from 'svelte';
    import { browser } from '$app/environment';

    export let src: string;
    export let crossfadeDuration: number = 0.05;

    let isPlaying = false;
    let error: Error | null = null;

    let audioContext: AudioContext | null = null;
    let buffer: AudioBuffer | null = null;
    let sources: AudioBufferSourceNode[] = [];
    let gains: GainNode[] = [];
    let loopTimer: number | null = null;

    /** Load and decode the audio buffer from source */
    async function loadBuffer(): Promise<void> {
        if (!buffer && audioContext) {
            const res = await fetch(src);
            if (!res.ok) throw new Error(`Network error: ${res.status}`);
            const data = await res.arrayBuffer();
            buffer = await audioContext.decodeAudioData(data);
        }
    }

    /** Schedule a loop with crossfade */
    function scheduleLoop(): void {
        if (!audioContext || !buffer) return;

        // stop old nodes
        sources.forEach(s => s.stop());
        gains.forEach(g => g.disconnect());
        sources = [];
        gains = [];

        const ctx = audioContext;
        const now = ctx.currentTime;
        const dur = buffer.duration;
        const cf = crossfadeDuration;

        // First buffer
        const s1 = ctx.createBufferSource();
        s1.buffer = buffer;
        const g1 = ctx.createGain();
        g1.gain.setValueAtTime(1, now);
        s1.connect(g1).connect(ctx.destination);
        s1.start(now);

        // Second buffer
        const start2 = now + dur - cf;
        const s2 = ctx.createBufferSource();
        s2.buffer = buffer;
        const g2 = ctx.createGain();
        g2.gain.setValueAtTime(0, now);
        g2.gain.linearRampToValueAtTime(1, start2 + cf);
        s2.connect(g2).connect(ctx.destination);
        s2.start(start2);

        // Crossfade out the first
        g1.gain.setValueAtTime(1, start2);
        g1.gain.linearRampToValueAtTime(0, start2 + cf);

        sources = [s1, s2];
        gains = [g1, g2];

        loopTimer = window.setTimeout(() => {
            if (isPlaying) scheduleLoop();
        }, (dur - cf) * 1000);

        isPlaying = true;
    }

    /** Force-unlock iOS AudioContext by playing a silent buffer */
    function unlockAudioContext(ctx: AudioContext): void {
        const silentBuffer = ctx.createBuffer(1, 1, ctx.sampleRate);
        const source = ctx.createBufferSource();
        source.buffer = silentBuffer;
        source.connect(ctx.destination);
        source.start();
    }

    /** Toggle play / pause */
    async function toggle(): Promise<void> {
        if (!browser) return;
		
        try {
            if (!audioContext) {
                audioContext = new AudioContext();
                unlockAudioContext(audioContext); // 🔑 unlock before any await
                await loadBuffer();
                scheduleLoop();
            } else if (!isPlaying && audioContext.state === 'suspended') {
                await audioContext.resume();
                scheduleLoop();
            } else if (isPlaying && audioContext !== null) {
                audioContext.suspend();
                isPlaying = false;
                if (loopTimer !== null) clearTimeout(loopTimer);
            } else {
                scheduleLoop();
            }
        } catch (e: unknown) {
            error = e instanceof Error ? e : new Error(String(e));
        }
    }

    onDestroy(() => {
        if (browser) {
            sources.forEach(s => s.stop());
            gains.forEach(g => g.disconnect());
            if (loopTimer !== null) clearTimeout(loopTimer);
            audioContext?.close();
        }
    });
</script>

<button class="p-2 bg-secondary text-secondary-foreground rounded hover:bg-secondary/80 focus:outline-none" on:click={toggle}>
    {#if isPlaying}
        <!-- Pause icon -->
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
    {:else}
        <!-- Play icon -->
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"/>
        </svg>
    {/if}
</button>

{#if error}
    <div class="text-sm text-destructive mt-2">
        ⚠️ {error.message}
    </div>
{/if}