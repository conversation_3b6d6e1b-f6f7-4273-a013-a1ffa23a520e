<script lang="ts">
    import { onDestroy, createEventDispatcher } from 'svelte';
    
    export let message: string;
    export let variant: 'error' | 'warning' | 'info' = 'error';
    export let autoDismiss = true;
    export let dismissAfter = 8000; // 8 seconds
    
    const dispatch = createEventDispatcher();
    
    let timeout: ReturnType<typeof setTimeout> | null = null;
    
    // Set up auto-dismiss when component mounts or message changes
    function setupAutoDismiss() {
        if (message && autoDismiss) {
            // Clear any existing timeout
            if (timeout) {
                clearTimeout(timeout);
            }
            
            // Set new timeout
            timeout = setTimeout(() => {
                handleDismiss();
            }, dismissAfter);
        }
    }
    
    $: if (message) setupAutoDismiss();
    
    function handleDismiss() {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
        dispatch('dismiss');
    }
    
    onDestroy(() => {
        if (timeout) {
            clearTimeout(timeout);
        }
    });
    
    // Compute classes based on variant
    $: bgClass = {
        error: 'bg-destructive/10',
        warning: 'bg-yellow-50',
        info: 'bg-blue-50'
    }[variant];
    
    $: borderClass = {
        error: 'border-destructive/20',
        warning: 'border-yellow-200',
        info: 'border-blue-200'
    }[variant];
    
    $: textClass = {
        error: 'text-destructive',
        warning: 'text-yellow-800',
        info: 'text-blue-800'
    }[variant];
    
    $: iconClass = {
        error: 'text-destructive',
        warning: 'text-yellow-600',
        info: 'text-blue-600'
    }[variant];
</script>

{#if message}
    <div class="relative p-4 rounded-lg border {bgClass} {borderClass}">
        <button
            on:click={handleDismiss}
            class="absolute top-3 right-3 p-1 hover:opacity-70 rounded transition-opacity"
            aria-label="Dismiss message"
        >
            <svg class="w-4 h-4 {iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
        <p class="text-sm pr-8 {textClass}">{message}</p>
        <slot />
    </div>
{/if}