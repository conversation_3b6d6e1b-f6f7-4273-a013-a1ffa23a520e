<script lang="ts">
    import { beliefsExplorerActions } from '../stores/beliefsExplorer';

    export let dynamics: string[];
    export let selectedDynamic: string | null;

    function selectDynamic(dynamic: string) {
        beliefsExplorerActions.selectDynamic(dynamic);
    }
</script>

<div class="my-6">
    <h3 class="text-xl font-semibold text-foreground mb-2 text-center">Which dynamic feels most dominant right now?</h3>
    <p class="text-muted-foreground text-center mb-6 leading-relaxed">
        Choose the one that resonates most with your current experience. You can work with one at a time for clarity.
    </p>
	
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
        {#each dynamics as dynamic (dynamic)}
            <button
                class="relative p-4 px-5 border-2 border-border rounded-lg bg-background text-foreground cursor-pointer transition-all duration-200 text-left flex items-center justify-between hover:border-primary hover:bg-muted {selectedDynamic === dynamic ? 'border-primary bg-muted text-primary' : ''}"
                on:click={() => selectDynamic(dynamic)}
            >
                <div class="font-medium text-sm">{dynamic}</div>
                {#if selectedDynamic === dynamic}
                    <div class="text-primary flex items-center justify-center">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                    </div>
                {/if}
            </button>
        {/each}
    </div>

    {#if selectedDynamic}
        <div class="p-4 bg-muted border border-border rounded-lg text-center">
            <p class="m-0 text-foreground mb-2">You've selected: <strong>{selectedDynamic}</strong></p>
            <p class="m-0 text-sm text-muted-foreground">The following flashcards will be tailored to this dynamic.</p>
        </div>
    {/if}
</div>