<script lang="ts">
    import { profile } from '../stores/profile';
    import type { Goal } from '../stores/profile';

    $: goals = $profile.goals;

    function addGoal() {
        const newGoal: Goal = {
            id: crypto.randomUUID(),
            name: '',
            description: '',
            why: ''
        };
		
        profile.update(p => ({
            ...p,
            goals: [...p.goals, newGoal]
        }));
    }

    function updateGoal(goalId: string, field: keyof Goal, value: string) {
        profile.update(p => ({
            ...p,
            goals: p.goals.map(goal => 
                goal.id === goalId ? { ...goal, [field]: value } : goal
            )
        }));
    }

    function removeGoal(goalId: string) {
        profile.update(p => ({
            ...p,
            goals: p.goals.filter(goal => goal.id !== goalId)
        }));
    }
</script>

<div class="my-6">
    <h3 class="text-xl font-semibold text-foreground mb-2 text-center">Your Goals & Why They Matter</h3>
    <p class="text-muted-foreground text-center mb-6 leading-relaxed">
        Reflect on what matters to you most. Understanding your "why" helps reconnect you to your intrinsic motivation and personal agency.
    </p>

    {#if goals.length === 0}
        <div class="text-center py-10 px-5 bg-muted rounded-lg border border-border">
            <p class="text-muted-foreground mb-5">You haven't added any goals yet. Let's start by adding what matters most to you.</p>
            <button class="flex items-center gap-2 px-5 py-3 bg-primary text-primary-foreground border border-primary rounded-lg cursor-pointer transition-all duration-200 text-sm font-medium hover:bg-primary/90" on:click={addGoal}>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Add Your First Goal
            </button>
        </div>
    {:else}
        <div class="flex flex-col gap-5 mb-5">
            {#each goals as goal (goal.id)}
                <div class="p-5 bg-background border border-border rounded-lg">
                    <div class="flex gap-3 items-start mb-3 md:flex-row flex-col">
                        <input
                            type="text"
                            placeholder="What is this goal?"
                            value={goal.name}
                            on:input={(e) => updateGoal(goal.id, 'name', e.currentTarget.value)}
                            class="flex-1 p-3 border border-border rounded-md bg-background text-foreground text-base font-medium focus:outline-none focus:border-primary"
                        />
                        <button
                            class="p-2 bg-transparent border border-border rounded-md text-muted-foreground cursor-pointer transition-all duration-200 hover:bg-muted hover:border-red-500 hover:text-red-500 md:self-auto self-end"
                            on:click={() => removeGoal(goal.id)}
                            aria-label="Remove goal"
                        >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>
					
                    <textarea
                        placeholder="Describe this goal in more detail (optional)"
                        value={goal.description || ''}
                        on:input={(e) => updateGoal(goal.id, 'description', e.currentTarget.value)}
                        class="w-full p-3 border border-border rounded-md bg-background text-foreground text-sm resize-y mb-4 font-inherit focus:outline-none focus:border-primary"
                        rows="2"
                    ></textarea>
					
                    <div class="flex flex-col gap-2">
                        <label class="text-sm font-medium text-foreground" for="why-{goal.id}">Why does this matter to you?</label>
                        <textarea
                            id="why-{goal.id}"
                            placeholder="What would achieving this give you? How would it make you feel? What values does it express?"
                            value={goal.why || ''}
                            on:input={(e) => updateGoal(goal.id, 'why', e.currentTarget.value)}
                            class="w-full p-3 border border-border rounded-md bg-background text-foreground text-sm resize-y font-inherit focus:outline-none focus:border-accent"
                            rows="3"
                        ></textarea>
                    </div>
                </div>
            {/each}
        </div>

        <button class="flex items-center gap-2 px-5 py-3 border border-border rounded-lg bg-background text-foreground cursor-pointer transition-all duration-200 text-sm font-medium hover:bg-muted hover:border-primary" on:click={addGoal}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Add Another Goal
        </button>
    {/if}
</div>