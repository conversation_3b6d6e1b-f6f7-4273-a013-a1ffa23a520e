<script lang="ts">
    import { cn } from '$lib/utils';

    export let variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'ghost' | 'link' = 'default';
    export let size: 'sm' | 'md' | 'lg' | 'xl' | 'xxl' = 'lg';
    export let disabled = false;
    export let type: 'button' | 'submit' | 'reset' = 'button';

    let className = '';
    export { className as class };

    const variants = {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-primary bg-background hover:bg-accent hover:text-accent-foreground',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline'
    };

    const sizes = {
        sm: 'h-9 rounded-md px-3 text-sm',
        md: 'h-10 px-4 py-2 text-base',
        lg: 'h-11 rounded-md px-8 text-base',
        xl: 'h-12 rounded-md px-10 text-lg',
        xxl: 'py-4 px-6 rounded-lg font-semibold text-lg'
    };

    $: buttonClasses = cn(
        'inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        variants[variant],
        sizes[size],
        className
    );
</script>

<button
    {type}
    {disabled}
    class={buttonClasses}
    on:click
    {...$$restProps}
>
    <slot />
</button>