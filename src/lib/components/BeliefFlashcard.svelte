<script lang="ts">
    import type { Flashcard } from '../types/beliefs';

    export let flashcard: Flashcard;
    export let isStarred: boolean = false;

    // Callback prop to replace createEventDispatcher
    export let onToggleFavorite: ((data: { flashcardId: string }) => void) | undefined = undefined;

    let isFlipped = false;

    function flipCard() {
        isFlipped = !isFlipped;
    }

    function toggleFavorite() {
        onToggleFavorite?.({ flashcardId: flashcard.id });
    }
</script>

<div class="relative min-h-[200px] cursor-pointer transition-all duration-300 select-none border border-border rounded-lg bg-background shadow-sm mb-4 hover:-translate-y-0.5">
    <div class="relative h-full flex flex-col">
        <!-- Star/Favorite Button -->
        <button
            class="absolute top-2 right-2 bg-transparent border-none cursor-pointer z-10 p-1 rounded-full transition-all duration-200 text-muted-foreground hover:bg-muted hover:text-foreground {isStarred ? 'text-primary' : ''}"
            on:click={toggleFavorite}
            aria-label={isStarred ? 'Remove from favorites' : 'Add to favorites'}
        >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="{isStarred ? 'fill-current' : ''}">
                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" />
            </svg>
        </button>

        <!-- Flashcard Content -->
        <div class="flex-1 flex flex-col justify-center p-5 pt-10 relative outline-none focus:outline-2 focus:outline-primary focus:outline-offset-2" on:click={flipCard} on:keydown={(e) => e.key === 'Enter' && flipCard()} role="button" tabindex="0">
            <div class="flex flex-col justify-center text-center h-full transition-opacity duration-300 {isFlipped ? 'opacity-0 absolute pointer-events-none' : ''}">
                <div class="text-xs font-semibold uppercase tracking-wider text-primary mb-3">Question</div>
                <p class="text-lg leading-relaxed text-foreground m-0 flex-1 flex items-center justify-center">{flashcard.front}</p>
                <div class="text-sm text-muted-foreground mt-4 opacity-70">Click to reveal reflection</div>
            </div>
			
            <div class="flex flex-col justify-center text-center h-full transition-opacity duration-300 {!isFlipped ? 'opacity-0 absolute pointer-events-none' : ''}">
                <div class="text-xs font-semibold uppercase tracking-wider text-accent mb-3">Reflection</div>
                <p class="text-lg leading-relaxed text-foreground m-0 flex-1 flex items-center justify-center">{flashcard.back}</p>
                <div class="text-sm text-muted-foreground mt-4 opacity-70">Click to return to question</div>
            </div>
        </div>
    </div>
</div>