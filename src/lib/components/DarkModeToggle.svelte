<script lang="ts">
    import { darkMode, type DarkMode } from '$lib/stores/theme';
    import { onMount } from 'svelte';

    let currentMode: DarkMode;

    onMount(() => {
        darkMode.subscribe((value) => {
            currentMode = value;
        });
    });

    function setDarkMode(mode: DarkMode) {
        darkMode.set(mode);
    }
</script>

<div class="inline-flex rounded-md shadow-sm" role="group">
    <button
        type="button"
        class="px-4 py-2 text-sm font-medium rounded-l-lg focus:z-10 focus:ring-2 focus:ring-primary-700
            {currentMode === 'light' ? 'bg-primary-500 text-white' : 'bg-white text-gray-900 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:text-white'}"
        on:click={() => setDarkMode('light')}
    >
        Light
    </button>
    <button
        type="button"
        class="px-4 py-2 text-sm font-medium focus:z-10 focus:ring-2 focus:ring-primary-700
            {currentMode === 'dark' ? 'bg-primary-500 text-white' : 'bg-white text-gray-900 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:text-white'}"
        on:click={() => setDarkMode('dark')}
    >
        Dark
    </button>
    <button
        type="button"
        class="px-4 py-2 text-sm font-medium rounded-r-md focus:z-10 focus:ring-2 focus:ring-primary-700
            {currentMode === 'system' ? 'bg-primary-500 text-white' : 'bg-white text-gray-900 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:text-white'}"
        on:click={() => setDarkMode('system')}
    >
        System
    </button>
</div>