<script lang="ts">
    import { page } from '$app/stores';
    import YbdIcon2 from './YbdIcon2.svelte';

    interface NavigationItem {
        path: string;
        icon: string;
        label: string;
    }

    const navigationItems: NavigationItem[] = [
        {
            path: '/app',
            icon: 'ybd:icon',
            label: 'YBD'
        },
        {
            path: '/app/relief',
            icon: 'mdi:alert-circle-outline',
            label: 'Relief'
        },
        {
            path: '/app/more',
            icon: 'mdi:cog-outline',
            label: 'More'
        }
    ];

</script>

<nav class="bg-background border-t border-border flex justify-around items-center">
    {#each navigationItems as item, index (index)}
        <a
            href={item.path}
            class="flex flex-col items-center flex-1 py-2 text-muted-foreground hover:text-primary"
            class:text-primary={$page.url.pathname === item.path}
        >
            {#if item.icon === 'ybd:icon'}
                <YbdIcon2
                    size="24"
                    className="mb-1 w-6 h-6"
                    fill="currentColor"
                />
            {:else if item.icon === 'mdi:alert-circle-outline'}
                <svg class="mb-1 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            {:else if item.icon === 'mdi:cog-outline'}
                <svg class="mb-1 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
            {:else}
                <svg class="mb-1 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21l3-3-3-3m4 0h8"/>
                </svg>
            {/if}
            <span>{item.label}</span>
        </a>
    {/each}
</nav>