<script lang="ts">
    export let liked: boolean = false;
    export let size: number = 36;
</script>

<button
    type="button"
    class="p-2 rounded-lg transition-all duration-200"
    aria-label={liked ? "Unlike" : "Like"}
>
    <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill={liked ? "#ef4444" : "none"}
        stroke="#ef4444"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="transition-all duration-200"
    >
        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
    </svg>
</button>