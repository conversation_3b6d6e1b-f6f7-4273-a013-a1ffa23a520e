<script lang="ts">
    import type { EmotionOption } from './emotionsData';
    
    export let emotions: EmotionOption[];
    export let onSelect: (emotion: string) => void;
</script>

<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 max-w-3xl mx-auto">
    {#each emotions as emotion (emotion.label)}
        <button
            on:click={() => onSelect(emotion.label)}
            class="p-4 bg-primary-600 rounded-lg shadow text-left"
        >
            <div class="flex items-start gap-2">
                <div>
                    <h4 class="mb-2">
                        <span class="text-2xl">{emotion.emoji}</span>
                        {emotion.label}
                    </h4>
                    <p class="text-foreground">{emotion.description}</p>
                </div>
            </div>
        </button>
    {/each}
</div>