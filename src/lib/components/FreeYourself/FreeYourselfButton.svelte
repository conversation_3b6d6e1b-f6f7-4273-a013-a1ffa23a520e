<script lang="ts">
    import FreeYourselfModal from './FreeYourselfModal.svelte';
    
    let modalOpen = false;
    
    function openModal() {
        modalOpen = true;
    }
    
    function handleModalClose() {
        modalOpen = false;
    }
</script>

<div class="text-center">
    <h2 class="text-2xl font-bold mb-4">Free Yourself</h2>
    <p class="text-muted-foreground mb-8 max-w-2xl mx-auto">
        You have the power to transform your mental state in unexpected situations. Use the free tool to help remind you of your power and choices.
    </p>
    
    <button
        on:click={openModal}
        class="px-8 py-4 bg-primary text-primary-foreground font-medium rounded-lg hover:bg-accent/90 transition-all hover:scale-105 text-lg shadow-md"
    >
        Launch Free Yourself Tool
    </button>
</div>

<FreeYourselfModal bind:open={modalOpen} on:close={handleModalClose} />