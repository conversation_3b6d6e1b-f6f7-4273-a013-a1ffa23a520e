<!--
    DEPRECATED: This component was used for the press-and-hold animation.
    Kept for reference/future troubleshooting but is no longer used.
    The animation has been replaced with a simple click button.
-->
<script lang="ts">
    import { onMount } from 'svelte';
    
    export let isHolding: boolean;
    export let holdProgress: number;
    export let isFading: boolean;
    export let releaseMessage: string;
    export let onMouseDown: () => void;
    export let onMouseUp: () => void;
    
    $: countdown = Math.ceil((100 - holdProgress) / 20);
    const circumference = 2 * Math.PI * 84;
    $: strokeDashoffset = circumference * (1 - holdProgress / 100);
    
    // Add safety listeners for edge cases
    onMount(() => {
        const handleAppBackground = () => {
            // Only release if actually holding
            if (isHolding) {
                onMouseUp();
            }
        };
        
        // Handle when app goes to background or loses focus
        document.addEventListener('visibilitychange', handleAppBackground);
        window.addEventListener('blur', handleAppBackground);
        
        return () => {
            document.removeEventListener('visibilitychange', handleAppBackground);
            window.removeEventListener('blur', handleAppBackground);
        };
    });
</script>

{#if isHolding || isFading}
    <div class="flex justify-center mt-8 transition-opacity duration-[3000ms]"
        class:opacity-0={isFading}>
        <div class="text-center">
            <div class="relative w-48 h-48 mx-auto mb-4">
                <svg class="w-48 h-48 transform -rotate-90">
                    <circle
                        cx="96"
                        cy="96"
                        r="84"
                        stroke="currentColor"
                        stroke-width="8"
                        fill="none"
                        class="text-background/20"
                    />
                    <circle
                        cx="96"
                        cy="96"
                        r="84"
                        stroke="currentColor"
                        stroke-width="8"
                        fill="none"
                        stroke-dasharray={circumference}
                        stroke-dashoffset={strokeDashoffset}
                        class="text-background transition-all duration-100"
                    />
                </svg>
                <div class="absolute inset-0 flex flex-col items-center justify-center">
                    <span class="text-sm text-background/80 font-medium">Next in...</span>
                    <span class="text-4xl font-bold text-background my-1">
                        {countdown}
                    </span>
                    <span class="text-lg text-background/90 font-medium">Breathe</span>
                </div>
            </div>
            {#if holdProgress >= 100}
                <p class="text-background font-semibold animate-bounce">
                    {releaseMessage}
                </p>
            {/if}
        </div>
    </div>
{:else}
    <div 
        class="flex items-center justify-center gap-2 font-medium text-foreground animate-pulse mt-0 touch-none select-none cursor-pointer p-4"
        role="button"
        tabindex="0"
        aria-label="Press and hold for 5 seconds to see another response"
        on:mousedown={onMouseDown}
        on:mouseup={onMouseUp}
        on:mouseleave={onMouseUp}
        on:touchstart|preventDefault={onMouseDown}
        on:touchend|preventDefault={onMouseUp}
        on:touchcancel|preventDefault={onMouseUp}
        on:keydown={(e) => e.key === 'Enter' && onMouseDown()}
        on:keyup={(e) => e.key === 'Enter' && onMouseUp()}
    >
        <span>Press & hold for more</span>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <path d="M12 6v6l4 2"/>
        </svg>
    </div>
{/if}