# HoldProgress Component

## Overview
The HoldProgress component provides an interactive "press and hold" button with visual progress feedback. It's designed to create an intentional pause between actions, encouraging mindful interaction.

## How it works
1. User presses and holds the button to start the progress
2. Progress circle fills over 5 seconds while holding
3. If user releases before 5 seconds, the action is cancelled
4. If user holds for full 5 seconds, a release message appears
5. When user releases after 5 seconds, the next response card is shown

## Visual States
- **Initial state**: "Press & hold for more" button with animated pulse effect
- **While holding**: Circular progress indicator with countdown (5, 4, 3, 2, 1)
- **At completion**: Release message prompting user to let go

## Props
- `isHolding` (boolean): Whether the user is currently holding the button
- `holdProgress` (number): Progress percentage (0-100)
- `isFading` (boolean): Whether the component is in fade-out transition
- `releaseMessage` (string): Message shown when hold is complete
- `onMouseDown` (function): Handler for when user starts holding
- `onMouseUp` (function): Handler for when user releases

## Technical Features
- Touch-friendly with proper event handling for mobile devices
- Keyboard accessible (Enter key support)
- Safety listeners for edge cases (app backgrounding, window blur)
- Smooth SVG-based circular progress animation
- Responsive design with Tailwind CSS

## Usage Example
```svelte
<HoldProgress
    isHolding={$isHolding}
    holdProgress={$progress}
    isFading={$isFading}
    releaseMessage="Release to continue"
    onMouseDown={handleMouseDown}
    onMouseUp={handleMouseUp}
/>
```

## Accessibility
- Proper ARIA labels for screen readers
- Keyboard navigation support
- Focus management with tabindex
- Clear visual feedback for all states