export interface Response {
    responseType: 'thought' | 'affirmation' | 'action' | 'question';
    content: string;
}

export interface EmotionOption {
    label: string;
    emoji: string;
    description: string;
    responses: Response[];
}

export const releaseMessages = [
    'Release the past!',
    'Let go of worry!',
    'Release the future!',
    'Embrace this moment!',
    'Let go of painful focus!'
];

export const emotions: EmotionOption[] = [
    {
        label: 'Anger',
        emoji: '😠',
        description: 'Release frustration from a world of disappointments and find peace.',
        responses: [
            {
                responseType: 'action',
                content: 'Anger often comes from unmet expectations or feeling powerless. Take a deep breath and embrace your power to choose your response.'
            },
            {
                responseType: 'thought',
                content: 'What would happen if I approached this situation with curiosity instead of judgment?'
            },
            {
                responseType: 'affirmation',
                content: 'I choose peace over being right. My inner calm is stronger than any external storm.'
            },
            {
                responseType: 'action',
                content: 'Write down three things you\'re grateful for right now. Then take 3 deep breaths, releasing tension with each exhale.'
            },
            {
                responseType: 'thought',
                content: 'My anger is showing myself something important about my boundaries. What need is not being met? How can I honor that need with compassion?'
            },
            {
                responseType: 'action',
                content: 'Take 3 long deep breaths. With each exhale say "A wide array of influences beyond my control play into this situation.", "I am releasing tension and judgement"'
            },
            {
                responseType: 'action',
                content: 'Take 3 long deep breaths. With each exhale say "What is my ultimate goal for today or this moment?", "I release this distraction and take action towards my true goals."'
            },
            {
                responseType: 'thought',
                content: 'My anger is showing myself something important about my beliefs and focus. What do I believe about this? What empowering beliefs might also be true?'
            },
            {
                responseType: 'affirmation',
                content: 'I transform my anger into positive action. I am powerful when I respond with wisdom.'
            },
            {
                responseType: 'question',
                content: 'What would it feel like to let go of this anger right now? What might become possible if I released this energy?'
            },
            {
                responseType: 'question',
                content: 'If this anger were a messenger, what important message might it be trying to deliver to me about my beliefs, values or boundaries?'
            }
        ]
    },
    {
        label: 'Sadness',
        emoji: '😢',
        description: 'Find comfort and relieve yourself of excessive grief and sadness.',
        responses: [
            {
                responseType: 'thought',
                content: 'Sadness is a natural response to loss or disappointment. I can embrace this feeling when it suits me. I can release it if it enslaves me.'
            },
            {
                responseType: 'thought',
                content: 'I have survived every difficult moment so far. What small act of kindness can I show myself right now?'
            },
            {
                responseType: 'affirmation',
                content: 'My tears water the seeds of my growth. I am becoming stronger through this experience.'
            },
            {
                responseType: 'action',
                content: 'Place your hand on your heart and say "I am here for you." Then do one small thing that brings you comfort - make tea, call a friend, or step outside.'
            },
            {
                responseType: 'thought',
                content: 'Sadness can connect us or paralyze us. I can embrace it when it serves something meaningful to me and release it with altering actions when it prevents my progress.'
            },
            {
                responseType: 'affirmation',
                content: 'I allow myself to feel, and in feeling, I heal. Joy will find me again.'
            },
            {
                responseType: 'question',
                content: 'What tiny spark of beauty or goodness can I find in this moment, even amidst the sadness?'
            },
            {
                responseType: 'question',
                content: 'If my sadness could speak, what would it say it needs from me? How can I honor that need with compassion?'
            }
        ]
    },
    {
        label: 'Worry/Fear',
        emoji: '😰',
        description: 'Calm your anxiety. Escape hyper-responsibility. Find hope and courage.',
        responses: [
            {
                responseType: 'thought',
                content: 'Worry tries to control an uncertain future. But I only have this moment. What is actually within my control right now? Focus there, and let the rest go.'
            },
            {
                responseType: 'affirmation',
                content: 'I trust my ability to handle whatever comes. The universe is working in my favor.'
            },
            {
                responseType: 'action',
                content: 'Name 5 things you can see, 4 you can touch, 3 you can hear, 2 you can smell, and 1 you can taste. Ground yourself in this present moment.'
            },
            {
                responseType: 'thought',
                content: 'Fear is just excitement without breath. What if this challenge is actually an opportunity I want but in disguise? What might be possible?'
            },
            {
                responseType: 'affirmation',
                content: 'I am safe in this moment. I have everything I need to take the next small step.'
            },
            {
                responseType: 'question',
                content: 'What evidence do I have that I\'ve handled difficult situations before? How did I surprise myself with my strength?'
            },
            {
                responseType: 'question',
                content: 'If my worry were trying to protect me, what is it trying to keep me safe from? Is that protection still needed? Is it based on all possibilities?'
            }
        ]
    },




    // {
    //     label: 'Suffering Loops',
    //     emoji: '🔄',
    //     description: 'Transform repetitive negative cycles into breakthrough opportunity cycles.',
    //     responses: [
    //         {
    //             responseType: 'thought',
    //             content: 'The future is unwritten. My power lies in this present moment. What one small action can I take today that my future self will thank me for?'
    //         },
    //         {
    //             responseType: 'affirmation',
    //             content: 'I trust the timing of my life. Everything is unfolding perfectly for my highest good.'
    //         },
    //         {
    //             responseType: 'action',
    //             content: 'Set a timer for 2 minutes. Write down my ideal day 1 year from now. Then choose one tiny step toward that vision I can take today.'
    //         },
    //     ]
    // }

];

