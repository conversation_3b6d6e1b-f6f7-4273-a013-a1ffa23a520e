# Emotions Data Notes


The following technically belong under a different title


```js
[
    {
        label: 'Visions from Past',
        emoji: '⬅️',
        description: 'Release yourself from the contradictory burden of history.',
        responses: [
            {
                responseType: 'thought',
                content: 'The past exists only in my mind now. I cannot change what happened if Im holding onto the story. I can change what observations means to me. I can change my focus and stories from pain to hope and progress.'
            },
            {
                responseType: 'affirmation',
                content: 'I am not my past. I am the author of my future, writing a new story with each choice.'
            },
            {
                responseType: 'action',
                content: 'Write a letter to your past self with compassion. Then symbolically release it - burn it safely, bury it, or tear it up and let it go.'
            },
            {
                responseType: 'thought',
                content: 'Every scar tells a story of survival. I am here, now, with all the power of my experiences. How will I use this strength?'
            },
            {
                responseType: 'affirmation',
                content: 'I forgive myself and others. I am free to create the life I deserve.'
            },
            {
                responseType: 'question',
                content: 'What parts of my past have actually made me wiser, stronger, or more compassionate? How can I honor that growth in this moment?'
            }
        ]
    },
    {
        label: 'Visions of Future',
        emoji: '➡️',
        description: 'Transform future concerns into future hope in a world of chaos.',
        responses: [
            {
                responseType: 'thought',
                content: 'The future is unwritten. My power lies in this present moment. What one small action can I take today that my future self will thank me for?'
            },
            {
                responseType: 'affirmation',
                content: 'I trust the timing of my life. Everything is unfolding perfectly for my highest good.'
            },
            {
                responseType: 'action',
                content: 'Set a timer for 2 minutes. Write down my ideal day 1 year from now. Then choose one tiny step toward that vision I can take today.'
            },
            {
                responseType: 'thought',
                content: 'Uncertainty is not my enemy - it\'s the space where all possibilities live. What would I do if I knew I couldn\'t fail?'
            },
            {
                responseType: 'affirmation',
                content: 'I am exactly where I need to be. My future is bright with infinite possibilities.'
            },
            {
                responseType: 'question',
                content: 'What if everything I\'am worried about works out better than I can imagine? How would that change how I feel right now?'
            },
            {
                responseType: 'question',
                content: 'What unique gifts, skills, or experiences do I have that will help me navigate whatever the future brings?'
            }
        ]
    },
]

```