<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import FreeYourself from './FreeYourself.svelte';
    
    export let open = false;
    
    const dispatch = createEventDispatcher();
    
    function handleClose() {
        open = false;
        dispatch('close');
    }
    
    function handleKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            handleClose();
        }
    }
</script>

{#if open}
    <div 
        class="fixed inset-0 z-50 bg-background overflow-y-auto"
        on:keydown={handleKeydown}
        role="dialog"
        aria-modal="true"
        tabindex="-1"
    >
        <!-- Header with close button -->
        <div class="bg-background border-b">
            <div class="max-w-4xl mx-auto p-4 flex justify-between items-center">
                <h2 class="mb-2">Free Yourself</h2>
                <button
                    on:click={handleClose}
                    class="p-2 hover:bg-accent rounded-lg transition-colors"
                    aria-label="Close"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Content -->
        <div class="max-w-4xl mx-auto p-5">
            <FreeYourself />
        </div>
    </div>
{/if}

<style>
    /* Prevent body scroll when modal is open */
    :global(body:has(.fixed.inset-0)) {
        overflow: hidden;
    }
</style>