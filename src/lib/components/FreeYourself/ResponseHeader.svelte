<script lang="ts">
    export let responseType: 'thought' | 'affirmation' | 'action' | 'question';
    
    const typeConfig = {
        thought: { emoji: '💭', label: 'Thought' },
        affirmation: { emoji: '✨', label: 'Affirmation' },
        action: { emoji: '🎯', label: 'Action' },
        question: { emoji: '❓', label: 'Question' }
    };
    
    $: config = typeConfig[responseType];
</script>

{#if config}
    <p class="text-sm uppercase text-foreground mb-3 text-center">
        {config.emoji} {config.label}
    </p>
{/if}