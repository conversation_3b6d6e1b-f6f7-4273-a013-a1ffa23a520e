<script lang="ts">
    import { currentTheme, themes, type Theme } from '$lib/stores/theme';
    import { fly } from 'svelte/transition';

    let isOpen = false;

    function toggleDropdown() {
        isOpen = !isOpen;
    }

    function selectOption(optionValue: Theme) {
        if (themes.some(t => t.value === optionValue)) {
            currentTheme.set(optionValue);
            console.log('Theme set to:', optionValue);
        }
        isOpen = false;
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            toggleDropdown();
        } else if (event.key === 'Escape') {
            isOpen = false;
        }
    }

    function handleOptionKeydown(event: KeyboardEvent, optionValue: Theme) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            selectOption(optionValue);
        }
    }

    // Close dropdown if clicked outside
    function handleClickOutside(event: MouseEvent) {
        const target = event.target as HTMLElement;
        if (!target.closest('.custom-select-container')) {
            isOpen = false;
        }
    }
</script>

<svelte:window on:click={handleClickOutside} />

<div class="custom-select-container relative w-full">
    <div
        class="selected-display bg-background border border-input rounded-md px-3 py-2 text-sm font-medium text-foreground cursor-pointer flex justify-between items-center"
        on:click={toggleDropdown}
        on:keydown={handleKeydown}
        tabindex="0"
        role="button"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
    >
        <div class="flex items-center space-x-2">
            <div class="w-3 h-3 rounded-full bg-primary-500" data-theme={$currentTheme}></div>
            <span>{themes.find(o => o.value === $currentTheme)?.label || 'Select a theme'}</span>
        </div>
        <svg
            class="w-4 h-4 transition-transform duration-200 ml-auto"
            class:rotate-180={isOpen}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
        >
            <path
                fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"
            />
        </svg>
    </div>

    {#if isOpen}
        <ul
            class="options-list absolute z-10 w-full bg-background border border-input rounded-md mt-1 shadow-lg max-h-60 overflow-auto"
            transition:fly={{ y: -5, duration: 150 }}
            role="listbox"
        >
            {#each themes as theme (theme.value)}
                <li
                    class="px-3 py-2 text-sm text-foreground cursor-pointer hover:bg-primary-100 hover:text-primary-foreground flex items-center space-x-2"
                    class:bg-primary={theme.value === $currentTheme}
                    class:text-primary-content={theme.value === $currentTheme}
                    on:click={() => selectOption(theme.value)}
                    on:keydown={(e) => handleOptionKeydown(e, theme.value)}
                    role="option"
                    aria-selected={theme.value === $currentTheme}
                    tabindex="0"
                >
                    <div class="w-3 h-3 rounded-full bg-primary-500" data-theme={theme.value}></div>
                    <span>{theme.label}</span>
                </li>
            {/each}
        </ul>
    {/if}
</div>

