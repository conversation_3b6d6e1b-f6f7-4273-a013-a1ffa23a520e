<script lang="ts">
    import { cn } from '$lib/utils';
    import WrappedLucideIcon from '../../stories/WrappedLucideIcon.svelte';
	
    // Import commonly used icons from lucide-svelte
    import {
        Rocket, Star, Heart, Settings, Home, User, Bell, Info,
        Smartphone, ArrowRight, Zap, HelpCircle, Bar<PERSON>hart,
        Palette, Moon, Sparkles, Ruler, Target, Rainbow,
        Plus, Minus, Check, X, ChevronRight, ChevronLeft,
        Search, Menu, Calendar, Clock, Mail, Phone,
        Edit, Trash, Download, Upload, Share, Copy, Leaf,
        AlertOctagon,
        Circle
    } from 'lucide-svelte';

    // Type for lucide-svelte icon components
    type LucideIcon = typeof Rocket;

    // Create icon mapping with proper typing
    const iconMap: Record<string, LucideIcon> = {
        rocket: Rocket,
        star: Star,
        heart: Heart,
        settings: Settings,
        home: Home,
        user: User,
        bell: Bell,
        info: Info,
        smartphone: Smartphone,
        arrowright: ArrowRight,
        zap: Zap,
        helpcircle: HelpCircle,
        barchart: <PERSON><PERSON><PERSON>,
        palette: <PERSON><PERSON>,
        moon: <PERSON>,
        sparkles: <PERSON>rkles,
        ruler: Ruler,
        target: Target,
        rainbow: Rainbow,
        plus: Plus,
        minus: Minus,
        check: Check,
        x: X,
        chevronright: ChevronRight,
        chevronleft: ChevronLeft,
        search: Search,
        menu: Menu,
        calendar: Calendar,
        clock: Clock,
        mail: Mail,
        phone: Phone,
        edit: Edit,
        trash: Trash,
        download: Download,
        upload: Upload,
        share: Share,
        copy: Copy,
        leaf: Leaf,
        alertoctagon: AlertOctagon,
        circle: Circle,
    };

    let currentIcon: LucideIcon | undefined;
    let showInvalidIconMessage = false;

    $: {
        if (iconName) {
            const normalizedIconName = iconName.toLowerCase();
            if (iconMap[normalizedIconName]) {
                currentIcon = iconMap[normalizedIconName];
                showInvalidIconMessage = false;
            } else {
                currentIcon = undefined;
                showInvalidIconMessage = true;
            }
        } else {
            currentIcon = undefined;
            showInvalidIconMessage = false;
        }
    }

    export let title: string;
    export let content: string;
    export let count: number | string | undefined = undefined;
    export let destination: string | undefined = undefined;
    export let iconName: string | undefined = undefined;
    export let iconColor: string | undefined = undefined;
    export let iconHeight: string | undefined = undefined;
	
    let className = '';
    export { className as class };
</script>

{#if destination}
    <a
        href={destination}
        class={cn("relative border border-primary rounded-lg p-6 bg-background shadow-sm flex flex-col cursor-pointer hover:shadow-md transition-shadow duration-200", className)}
    >
        <div class="flex flex-col">
            <div class="flex items-center gap-3 mb-3">
                {#if iconName && currentIcon}
                    <div class={cn("flex items-center", iconColor ? '' : 'text-primary')}>
                        <WrappedLucideIcon
                            icon={currentIcon}
                            color={iconColor || 'currentColor'}
                            size={parseInt(iconHeight || '20')}
                        />
                    </div>
                {:else if iconName && showInvalidIconMessage}
                    <span class="text-red-500 text-sm">Icon nr or invalid</span>
                {/if}
                <h2 class="text-xl font-semibold text-foreground m-0">{title}</h2>
            </div>
            <hr class="border-t border-primary w-full mb-3" />
            <p class="text-muted-foreground leading-normal min-h-[72px] line-clamp-3">{content}</p>
        </div>
        {#if count !== undefined}
            <div class="absolute -top-2.5 -right-2.5 bg-muted text-muted-foreground rounded-full w-8 h-8 flex items-center justify-center font-bold text-base border border-border">
                {count}
            </div>
        {/if}
    </a>
{:else}
    <!-- Non-clickable version if no destination is provided -->
    <div class={cn("relative border border-primary rounded-lg p-6 mb-4 bg-background shadow-sm flex flex-col", className)}>
        <div class="flex flex-col">
            <div class="flex items-center gap-3 mb-3">
                {#if iconName && currentIcon}
                    <div class={cn("flex items-center", iconColor ? '' : 'text-primary')}>
                        <WrappedLucideIcon
                            icon={currentIcon}
                            color={iconColor || 'currentColor'}
                            size={parseInt(iconHeight || '20')}
                        />
                    </div>
                {:else if iconName && showInvalidIconMessage}
                    <span class="text-red-500 text-sm">Icon nr or invalid</span>
                {/if}
                <h2 class="text-2xl font-semibold text-foreground m-0">{title}</h2>
            </div>
            <hr class="border-t border-primary w-full mb-3" />
            <p class="text-muted-foreground leading-normal min-h-[72px] line-clamp-3">{content}</p>
        </div>
        {#if count !== undefined}
            <div class="absolute -top-2.5 -right-2.5 bg-muted text-muted-foreground rounded-full w-8 h-8 flex items-center justify-center font-bold text-base border border-border">
                {count}
            </div>
        {/if}
    </div>
{/if}