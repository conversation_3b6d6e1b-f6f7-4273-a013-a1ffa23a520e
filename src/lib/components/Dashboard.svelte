<script lang="ts">
    import Card from '$lib/components/CardBase.svelte';
	
    let recommendations_unread = 4;
</script>

<div class="flex flex-col items-center min-h-screen">
    <div class="text-left">
        <p class="text-muted-foreground italic mb-1 text-center">Get Back To...</p>

        <h1 class="text-foreground mb-4 text-center">Your Best Days</h1>
		
        <div class="w-full max-w-md h-px bg-border"></div>

        <p class="text-foreground mt-4 text-center">Choose your path...</p>
    </div>

    <div class="space-y-8 mx-4">
        {#if recommendations_unread === 0}
            <div class="text-left p-4">
                <p class="text-foreground mb-6 max-w-md">
                    Great job finishing your recommendations. Now live your life. Do what's in your power to make the best of this moment regardless of the last.
                </p>
				
                <p class="text-foreground mb-6 max-w-md">
                    Embrace alternative thoughts and actions to empower your mind. Do what's in your power to relieve mental suffering and achieve your goals.
                </p>
				
                <p class="text-foreground mb-6 max-w-md text-center">
                    Focus on this moment, to make today your best day.
                </p>
            </div>
        {:else}
            <div>
                <Card
                    class=""
                    iconName="Leaf"
                    title="Recommendations"
                    content="See unread YBD messages and activities. These can support a mindset for your goals based on your profile."
                    count={recommendations_unread}
                    destination="/app/ybd"
                />
            </div>
        {/if}

        <Card
            iconName="AlertOctagon"
            title="Experience Wizard"
            content="Your profile for living life intentionally. Revisit this often to ensure goal, focus and action alignment."
            destination="/app/wizard"
        />
    </div>
</div>