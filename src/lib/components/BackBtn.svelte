<script lang="ts">
    import { goto } from '$app/navigation';
    import { browser } from '$app/environment';

    export let title: string = '';

    function goBack() {
        if (browser && window.history.length > 1) {
            window.history.back();
        } else {
            // Fallback to home page if no history
            goto('/');
        }
    }
</script>

<div class="w-full bg-background border-t border-border/20 shadow-md">
    <div class="px-4 py-2">
        <button
            class="
                w-full flex items-center justify-center gap-2 px-4 py-2.5 rounded-lg
                bg-primary/15 hover:bg-primary/25 active:bg-primary/35
                text-primary hover:text-primary-foreground
                border border-primary/30 hover:border-primary/50
                transition-all duration-200 ease-in-out
                shadow-sm hover:shadow-md active:shadow-sm
                focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2
                font-medium text-sm
            "
            on:click={goBack}
            aria-label="Go back"
        >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back</span>
        </button>
    </div>
	
    {#if title}
        <div class="px-4 pb-1 text-center text-xs text-muted-foreground">
            {title}
        </div>
    {/if}
</div>