<script lang="ts">
    import type { YBDMessage, RandomThought, AudioFile } from '$lib/types/ybd';
    import Action from './Action.svelte';
    import BetweenThought from './BetweenThought.svelte';

    export let ideas: YBDMessage[] = [];
    export let audioFile: AudioFile | undefined = undefined;
    export let betweenThoughts: RandomThought[] = [];
    export const icon: string | undefined = undefined; // Kept for consistency, though not used in this template
    export let requiredActionViewTime: number = 5; // in seconds
    export let displayTimeBetweenThoughts: number = 8; // in seconds

    // Callback prop to replace createEventDispatcher
    export let onUpdateFavorite: ((data: { id: string; favorite: boolean }) => void) | undefined = undefined;

    let currentIdeaIndex = 0;
    let showingActions = false;
    let showingBetweenThought = false;
    let actionViewStartTime: Date | null = null;
    let canProceedToNext = ideas.length <= 1; // Allow proceeding if only one item or no items

    let scrollContainer: HTMLElement | null = null;
    let scrollToTopAnchor: HTMLElement | null = null;

    $: currentIdea = ideas[currentIdeaIndex];

    // Watch currentIdeaIndex changes
    $: {
        if (currentIdeaIndex !== undefined) {
            // Reset progression blockers when idea changes, unless there's only one idea
            if (ideas.length > 1) {
                canProceedToNext = false;
            }
            scrollToTop();
        }
    }

    // Watch ideas array changes
    $: {
        if (ideas) {
            if (currentIdeaIndex >= ideas.length) {
                currentIdeaIndex = Math.max(0, ideas.length - 1);
            }
            canProceedToNext = ideas.length <= 1;
        }
    }

    function scrollToTop(): void {
        scrollToTopAnchor?.scrollIntoView({ behavior: 'smooth' });
    // Fallback for the main scroll container if anchor is not enough
		// scrollContainer?.scrollTo({ top: 0, behavior: 'smooth' });
    }

    function toggleFavorite(): void {
        if (currentIdea && onUpdateFavorite) {
            onUpdateFavorite({ id: currentIdea.id, favorite: !currentIdea.favorite });
        }
    }

    function openActions(): void {
        showingActions = true;
        actionViewStartTime = new Date();
    }

    function closeActions(): void {
        showingActions = false;
        if (actionViewStartTime) {
            const timeSpent = (new Date().getTime() - actionViewStartTime.getTime()) / 1000;
            if (timeSpent >= requiredActionViewTime) {
                canProceedToNext = true;
            }
            actionViewStartTime = null;
        }
    }

    function prevIdea(): void {
        if (currentIdeaIndex > 0) {
            currentIdeaIndex--;
        }
    }

    function nextIdea(): void {
        if (currentIdeaIndex < ideas.length - 1 && canProceedToNext && !showingBetweenThought) {
            showingBetweenThought = true;
        // Actual idea change will happen in handleBetweenThoughtDone
        }
    }

    function handleBetweenThoughtDone(): void {
        showingBetweenThought = false;
        if (currentIdeaIndex < ideas.length - 1) {
            currentIdeaIndex++;
        }
    }
</script>

<div class="flex flex-col min-h-screen bg-background text-foreground">
    <div bind:this={scrollContainer} class="flex-grow overflow-y-auto p-5 pt-0">
        <div bind:this={scrollToTopAnchor}></div>
        {#if currentIdea}
            <div class="max-w-2xl mx-auto">
                <!-- Audio Player -->
                {#if audioFile}
                    <div class="mb-6">
                        <div class="bg-background border border-border p-4 rounded-lg shadow">
                            <h3 class="text-sm font-semibold text-muted-foreground mb-2">Audio Guide</h3>
                            <audio controls src={audioFile.remoteUrl} class="w-full">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                    </div>
                {/if}


                <!-- Quote -->
                {#if currentIdea.quote}
                    <div class="mb-6 p-4 bg-muted rounded-lg shadow-sm">
                        <p class="text-2xl italic text-center text-foreground">
                            "{currentIdea.quote.quote}"
                        </p>
                        {#if currentIdea.quote.quote_author}
                            <p class="text-sm text-right mt-2 text-muted-foreground">
                                — {currentIdea.quote.quote_author}
                            </p>
                        {/if}
                    </div>
                {/if}

                <!-- Message -->
                {#if currentIdea.message}
                    <p class="text-lg font-thin leading-relaxed mb-8 text-foreground whitespace-pre-line">
                        {currentIdea.message}
                    </p>
                {/if}

                <!-- Favorite Button -->
                <div class="flex justify-center my-6">
                    <button aria-label="Toggle favorite" on:click={toggleFavorite}>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-10 w-10 transition-transform duration-150 ease-in-out {currentIdea.favorite ? 'text-primary fill-current' : 'text-muted-foreground hover:text-primary'}"
                            viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>

                <!-- See Actions Button -->
                <button
                    class="w-full flex items-center justify-center py-3 px-4 border-2 border-primary text-primary rounded-lg hover:bg-primary/10 transition-colors mb-3"
                    on:click={openActions}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                    See Actions
                </button>

                <!-- Guidance Text -->
                {#if ideas.length > 1 && !canProceedToNext}
                    <p class="text-sm italic font-bold text-center text-muted-foreground mb-6 px-4">
                        Review and take actions before moving on.
                    </p>
                {/if}

                <!-- Navigation Controls (for multiple ideas) -->
                {#if ideas.length > 1}
                    <div class="mt-6">
                        <div class="grid grid-cols-2 gap-3">
                            <button
                                disabled={currentIdeaIndex === 0}
                                class="flex items-center justify-center py-3 rounded-lg transition-colors {currentIdeaIndex > 0
                                    ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                                    : 'bg-muted text-muted-foreground'}"
                                on:click={prevIdea}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                                Previous
                            </button>
                            <button
                                disabled={currentIdeaIndex >= ideas.length - 1 || !canProceedToNext || showingBetweenThought}
                                class="flex items-center justify-center py-3 rounded-lg transition-colors {currentIdeaIndex < ideas.length - 1 && !showingBetweenThought && canProceedToNext
                                    ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                                    : 'bg-muted text-muted-foreground'}"
                                on:click={nextIdea}
                            >
                                Next
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>
                        <p class="text-center text-sm text-muted-foreground mt-3">
                            {currentIdeaIndex + 1} / {ideas.length}
                        </p>
                    </div>
                {/if}
                <div class="h-12"></div> <!-- Spacer at the bottom -->
            </div>
        {:else}
            <div class="text-center py-10">
                <p class="text-muted-foreground">No ideas to display.</p>
            </div>
        {/if}
    </div>

    <!-- Modals -->
    {#if showingActions && currentIdea}
        <Action
            actions={currentIdea.actionItems || []}
            onClose={closeActions}
        />
    {/if}
    {#if showingBetweenThought}
        <BetweenThought
            thoughts={betweenThoughts}
            displayTime={displayTimeBetweenThoughts}
            onDone={handleBetweenThoughtDone}
        />
    {/if}
</div>