<script lang="ts">
    import { onMount } from 'svelte';
    import type { YBDAction, RandomThought } from '$lib/types/ybd';
    import BetweenThought from './BetweenThought.svelte';

    export let actions: YBDAction[];
    export let betweenActionsDisplayTime: number = 3.0;

    // Callback prop to replace createEventDispatcher
    export let onClose: (() => void) | undefined = undefined;

    // State variables
    let currentActionIndex = 0;
    let favoriteActions = new Set<string>();
    let completedActions = new Set<string>();
    let showingStar = false;
    let showingBetweenThought = false;
    let betweenThoughts: RandomThought[] = [];

    // Computed properties
    $: currentAction = actions[currentActionIndex];
    $: isFavorite = currentAction !== undefined && favoriteActions.has(currentAction.id);
    $: isCompleted = currentAction !== undefined && completedActions.has(currentAction.id);

    // Methods
    function toggleFavorite(): void {
        if (currentAction !== undefined) {
            if (favoriteActions.has(currentAction.id)) {
                favoriteActions.delete(currentAction.id);
            } else {
                favoriteActions.add(currentAction.id);
            }
            favoriteActions = favoriteActions; // Trigger reactivity
        }
    }

    function toggleCompleted(): void {
        if (currentAction !== undefined) {
            if (completedActions.has(currentAction.id)) {
                completedActions.delete(currentAction.id);
                showingStar = false;
            } else {
                completedActions.add(currentAction.id);
                showingStar = true;
				
                // Hide star after 1.5 seconds
                setTimeout(() => {
                    showingStar = false;
                }, 1500);
            }
            completedActions = completedActions; // Trigger reactivity
        }
    }

    function prevAction(): void {
        if (currentActionIndex > 0) {
            currentActionIndex--;
        }
    }

    function nextAction(): void {
        if (currentActionIndex < actions.length - 1) {
            // Show between thought before going to next action
            showingBetweenThought = true;
        }
    }

    function handleBetweenThoughtDone(): void {
        showingBetweenThought = false;
        if (currentActionIndex < actions.length - 1) {
            currentActionIndex++;
        }
    }

    // Function to load between thoughts
    async function loadBetweenThoughts(): Promise<void> {
        try {
            const response = await fetch('/data/ybd_between_actions.json');
            if (!response.ok) throw new Error('Failed to load between thoughts');
            betweenThoughts = await response.json();
        } catch (error) {
            console.error('Error loading between thoughts:', error);
            // Fallback data in case the fetch fails
            betweenThoughts = [
                { id: '1', thought: 'Taking a deep breath...' },
                { id: '2', thought: 'Finding your center...' },
                { id: '3', thought: "You're doing great..." }
            ];
        }
    }

    // Initialize
    onMount(() => {
        // Load between thoughts on component mount
        loadBetweenThoughts();
    });
</script>

<div class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-2 z-50">
    <div class="bg-background border border-border p-6 rounded-lg shadow-xl max-w-lg w-full flex flex-col h-full">
        <!-- Title -->
        <h2 class="text-2xl font-semibold mb-4 text-foreground">Actions</h2>
		
        <!-- Main content section -->
        <div class="flex-grow flex flex-col justify-center items-center">
            {#if actions && actions.length > 0}
                <div class="w-full mb-8">
                    <!-- Current action content -->
                    <p class="text-xl font-light text-center text-foreground mb-8">
                        {currentAction?.action}
                    </p>
					
                    <!-- Action buttons (star and checkbox) -->
                    <div class="flex justify-center space-x-8 mb-8">
                        <button class="focus:outline-none" aria-label="Toggle favorite" on:click={toggleFavorite}>
                            <svg
                                class="w-8 h-8 transition-colors duration-200 {isFavorite ? 'text-primary' : 'text-muted-foreground'}"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="currentColor">
                                <path d="M9.153 3.24c.433-.863 1.662-.863 2.094 0l1.874 3.764 4.195.608c.956.139 1.337 1.312.645 1.986l-3.035 2.947.717 4.169c.163.95-.833 1.675-1.684 1.226L10 15.874l-3.959 2.066c-.85.449-1.846-.275-1.684-1.226l.717-4.169-3.035-2.947c-.692-.674-.31-1.847.645-1.986l4.195-.608L9.153 3.24z" />
                            </svg>
                        </button>
                        <button class="focus:outline-none" aria-label="Mark as completed" on:click={toggleCompleted}>
                            <svg
                                class="w-8 h-8 transition-colors duration-200 {isCompleted ? 'text-primary' : 'text-muted-foreground'}"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="currentColor">
                                <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm-1.5 9.793l-2.646-2.647a.5.5 0 0 0-.708.708l3 3a.5.5 0 0 0 .708 0l5-5a.5.5 0 0 0-.708-.708L10.5 11.793z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
					
                    <!-- How button -->
                    <div class="flex justify-center">
                        <button
                            class="px-8 py-3 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors duration-200 focus:outline-none mb-8">
                            How?
                        </button>
                    </div>
                </div>
            {:else}
                <p class="text-muted-foreground text-center">No actions available.</p>
            {/if}
        </div>

        <!-- Navigation Controls -->
        <div class="mt-auto">
            <div class="grid grid-cols-2 gap-3 mb-3">
                <button
                    disabled={currentActionIndex === 0}
                    class="flex items-center justify-center py-3 rounded-lg transition-colors {currentActionIndex > 0
                        ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                        : 'bg-muted text-muted-foreground'}"
                    on:click={prevAction}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                    Previous
                </button>
                <button
                    disabled={currentActionIndex >= actions.length - 1 || showingBetweenThought}
                    class="flex items-center justify-center py-3 rounded-lg transition-colors {currentActionIndex < actions.length - 1 && !showingBetweenThought
                        ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                        : 'bg-muted text-muted-foreground'}"
                    on:click={nextAction}
                >
                    Next
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
			
            <!-- Exit button -->
            <button
                class="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
                on:click={() => onClose?.()}
            >
                <div class="flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    Actions Exit
                </div>
            </button>
			
            <p class="text-center text-sm text-muted-foreground mt-2">
                {currentActionIndex + 1} / {actions.length}
            </p>
        </div>
		
        <!-- Sparkling star animation if needed -->
        {#if showingStar}
            <div class="absolute inset-0 pointer-events-none flex items-center justify-center">
                <!-- Simple star animation using CSS -->
                <div class="stars-container">
                    {#each Array(8).fill(0).map((_, i) => i) as i (i)}
                        <div class="star" style="--delay: {i * 0.1}s; --angle: {i * 45}deg"></div>
                    {/each}
                </div>
            </div>
        {/if}
    </div>
</div>

<!-- Between Thought Modal -->
{#if showingBetweenThought}
    <BetweenThought
        thoughts={betweenThoughts}
        displayTime={betweenActionsDisplayTime}
        onDone={handleBetweenThoughtDone}
    />
{/if}

