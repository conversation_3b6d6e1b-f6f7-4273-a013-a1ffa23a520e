<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import type { RandomThought } from '$lib/types/ybd';

    export let thoughts: RandomThought[];
    export let displayTime: number = 8.0; // in seconds

    // Callback prop to replace createEventDispatcher
    export let onDone: (() => void) | undefined = undefined;

    let currentThoughtIndex = 0;
    let opacity = 0;
    let timeRemaining = 0;
    let showCountdown = false;
    let timer: number | null = null;
    let progressInterval: number | null = null;

    // Calculate a random thought from the array
    $: currentThought = thoughts.length > 0 ? thoughts[currentThoughtIndex % thoughts.length] : null;

    // Calculate the circle progress offset (100 = full circle, 0 = empty)
    $: progressOffset = displayTime ? 100 - (timeRemaining / displayTime) * 100 : 100;

    function startTimer(duration: number): void {
        timeRemaining = duration;
        showCountdown = true;

        if (progressInterval) clearInterval(progressInterval);

        progressInterval = window.setInterval(() => {
            if (timeRemaining > 0) {
                timeRemaining -= 0.1;
            } else {
                if (progressInterval) clearInterval(progressInterval);
                showCountdown = false;
            }
        }, 100);

        timer = window.setTimeout(() => {
            onDone?.();
        }, duration * 1000);
    }

    onMount(() => {
        if (thoughts.length === 0) {
            onDone?.();
            return;
        }

        // Select initial random thought
        if (thoughts.length > 0) {
            const randomIndex = Math.floor(Math.random() * thoughts.length);
            currentThoughtIndex = randomIndex;
        }

        // Animate in
        setTimeout(() => {
            opacity = 1;
        }, 100);

        // Start the countdown if we have a display time
        if (typeof displayTime === 'number' && displayTime > 0) {
            startTimer(displayTime);
        }
    });

    onDestroy(() => {
        if (timer) clearTimeout(timer);
        if (progressInterval) clearInterval(progressInterval);
    });
</script>

<div class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50">
    <div class="bg-background border border-border p-8 rounded-xl shadow-2xl text-center max-w-md w-full flex flex-col items-center">
        {#if currentThought}
            <p class="text-2xl text-foreground italic mb-8 transition-opacity duration-1500" style="opacity: {opacity}">
                "{currentThought.thought}"
            </p>
        {:else}
            <p class="text-xl text-muted-foreground transition-opacity duration-1500" style="opacity: {opacity}">
                Preparing next thought...
            </p>
        {/if}
		
        <!-- Progress Circle -->
        {#if showCountdown}
            <div class="mt-6 relative h-12 w-12">
                <svg class="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                    <!-- Background Circle -->
                    <circle
                        cx="18" cy="18" r="16"
                        fill="none"
                        stroke="var(--border)"
                        stroke-width="2"
                        opacity="0.3"
                    />
                    <!-- Progress Circle -->
                    <circle
                        cx="18" cy="18" r="16"
                        fill="none"
                        stroke="var(--primary-500)"
                        stroke-width="2"
                        stroke-dasharray="100"
                        stroke-dashoffset={progressOffset}
                        stroke-linecap="round"
                    />
                </svg>
            </div>
        {/if}
    </div>
</div>