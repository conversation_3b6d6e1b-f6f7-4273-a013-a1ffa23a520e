<script lang="ts">
    export let size: string = '24';
    export let fill: string = 'currentColor';
    export let stroke: string = 'none';
    export let className: string = '';
</script>

<svg 
    class={className}
    width={size} 
    height={size} 
    fill={fill} 
    stroke={stroke}
    viewBox="0 0 360 360"
    xmlns="http://www.w3.org/2000/svg"
>
    <circle cx="297.8" cy="179.79" r="31.02" transform="translate(-13.21 23.97) rotate(-4.51)"/>
    <path d="M180,2.5C81.97,2.5,2.5,81.97,2.5,180s79.47,177.5,177.5,177.5,177.5-79.47,177.5-177.5S278.03,2.5,180,2.5ZM62.19,218.85c-21.71,0-39.3-17.6-39.3-39.3s17.6-39.3,39.3-39.3,39.3,17.6,39.3,39.3-17.6,39.3-39.3,39.3ZM252.94,314.31c-5.49-.22-11.8-.38-18.93-.49-7.14-.11-14.55-.22-22.23-.33-7.68-.11-15.26-.17-22.72-.17h-6.59c-7.24,0-14.82.06-22.72.17-7.9.11-15.47.16-22.72.16s-13.39.11-18.44.33v-12.84l17.45-.99c10.32-1.32,16.96-3.67,19.92-7.08,2.96-3.4,4.44-10.04,4.44-19.92v-47.48l-66.18-110.58c-3.08-5.27-6.09-9.22-9.06-11.85-2.96-2.63-6.09-4.66-9.38-6.09-3.29-1.43-7.47-2.25-12.51-2.47l-8.89-.66.33-12.84c5.48.22,12.12.44,19.92.66,7.79.22,15.53.33,23.21.33h25.19c6.26,0,12.51-.05,18.77-.17,6.26-.11,12.07-.22,17.45-.33,5.38-.11,9.93-.17,13.66-.17l.33,12.84-11.2.99c-8.78.66-13.78,2.63-14.98,5.92-1.21,3.3,1.15,9.99,7.08,20.09l48.44,79.73,30.59-55.69c8.78-16.25,12.18-28.37,10.21-36.39-1.98-8.01-9.22-12.68-21.73-14l-5.93-.99v-12.84c9,.22,17.62.44,25.85.66,8.23.22,16.3.33,24.2.33s14.7-.11,21.07-.33c6.36-.22,12.62-.44,18.77-.66v12.84l-5.93.33c-6.37,1.54-12.3,3.74-17.78,6.59-5.49,2.86-10.81,7.58-15.97,14.16-5.16,6.59-10.81,15.92-16.96,27.99l-38.53,70.26v59.8c0,9.88,1.54,16.58,4.61,20.09,3.07,3.51,9.66,5.82,19.76,6.91l18.11,1.32v12.84ZM297.61,218.9c-21.71,0-39.3-17.6-39.3-39.3s17.6-39.3,39.3-39.3,39.3,17.6,39.3,39.3-17.6,39.3-39.3,39.3Z"/>
</svg>