<script lang="ts">
    export let size: string = '24';
    export let fill: string = 'currentColor';
    export let stroke: string = 'none';
    export let className: string = '';
</script>

<svg 
    class={className}
    width={size} 
    height={size} 
    fill={fill} 
    stroke={stroke}
    viewBox="0 0 360 360"
    xmlns="http://www.w3.org/2000/svg"
>
    <path d="M297.61,140.3c-21.71,0-39.3,17.6-39.3,39.3s17.6,39.3,39.3,39.3,39.3-17.6,39.3-39.3-17.6-39.3-39.3-39.3ZM297.8,210.81c-17.13,0-31.02-13.89-31.02-31.02s13.89-31.02,31.02-31.02,31.02,13.89,31.02,31.02-13.89,31.02-31.02,31.02Z"/>
    <circle cx="62.19" cy="179.55" r="39.3" transform="translate(-10.08 3.81) rotate(-3.25)"/>
    <path d="M215.07,293.24c-3.07-3.51-4.61-10.21-4.61-20.09v-59.8l38.53-70.26c6.14-12.07,11.8-21.4,16.96-27.99,5.16-6.59,10.48-11.3,15.97-14.16,5.48-2.85,11.41-5.04,17.78-6.59l5.93-.33v-12.84c-6.15.22-12.4.44-18.77.66-6.37.22-13.39.33-21.07.33s-15.97-.11-24.2-.33c-8.23-.22-16.85-.44-25.85-.66v12.84l5.93.99c12.51,1.32,19.76,5.98,21.73,14,1.98,8.02-1.43,20.14-10.21,36.38l-30.59,55.69-48.44-79.73c-5.93-10.1-8.29-16.79-7.08-20.09,1.2-3.29,6.2-5.27,14.98-5.92l11.2-.99-.33-12.84c-3.74,0-8.29.06-13.66.17-5.38.11-11.2.22-17.45.33-6.26.11-12.51.17-18.77.17h-25.19c-7.69,0-15.42-.11-23.21-.33-7.79-.22-14.44-.44-19.92-.66l-.33,12.84,8.89.66c5.05.22,9.22,1.04,12.51,2.47,3.29,1.43,6.42,3.46,9.38,6.09,2.96,2.63,5.98,6.59,9.06,11.85l66.18,110.58v47.48c0,9.88-1.48,16.52-4.44,19.92-2.96,3.41-9.61,5.76-19.92,7.08l-17.45.99v12.84c5.05-.22,11.2-.33,18.44-.33s14.82-.05,22.72-.16c7.9-.11,15.48-.17,22.72-.17h6.59c7.46,0,15.03.06,22.72.17,7.68.11,15.09.22,22.23.33,7.13.11,13.44.28,18.93.49v-12.84l-18.11-1.32c-10.1-1.09-16.69-3.4-19.76-6.91Z"/>
    <path d="M180,2.5C81.97,2.5,2.5,81.97,2.5,180s79.47,177.5,177.5,177.5,177.5-79.47,177.5-177.5S278.03,2.5,180,2.5ZM180,349.68c-93.71,0-169.68-75.97-169.68-169.68S86.29,10.32,180,10.32s169.68,75.97,169.68,169.68-75.97,169.68-169.68,169.68Z"/>
</svg>