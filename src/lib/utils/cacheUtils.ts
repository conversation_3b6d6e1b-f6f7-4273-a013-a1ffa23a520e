import type { NotificationCache, NotificationItem } from '$lib/types/notifications';
import { getCurrentTimezone } from './dateUtils';

const CACHE_KEY = 'ybd_notification_queue';
const CACHE_VERSION = '1.0';

/**
 * Save notification cache to localStorage
 */
export function saveNotificationCache(
    notifications: NotificationItem[],
    profileHash: string,
    expiresAt: Date = new Date(Date.now() + 24 * 60 * 60 * 1000)
): void {
    try {
        const cacheData: NotificationCache = {
            generatedAt: new Date().toISOString(),
            expiresAt: expiresAt.toISOString(),
            profileHash,
            notifications: notifications.map(notification => ({
                ...notification,
                scheduledTime: notification.scheduledTime // Keep as Date object for now
            })),
            nextRegenerationTime: expiresAt.toISOString(),
            version: CACHE_VERSION,
            timezone: getCurrentTimezone()
        };

        // Convert dates to ISO strings for storage
        const storageData = {
            ...cacheData,
            notifications: cacheData.notifications.map(notification => ({
                ...notification,
                scheduledTime: notification.scheduledTime.toISOString()
            }))
        };

        localStorage.setItem(CACHE_KEY, JSON.stringify(storageData));
    } catch (error) {
        console.error('Failed to save notification cache:', error);
    }
}

/**
 * Load notification cache from localStorage
 */
export function loadNotificationCache(): NotificationCache | null {
    try {
        const cachedData = localStorage.getItem(CACHE_KEY);
        if (!cachedData) {
            return null;
        }

        const parsed = JSON.parse(cachedData);
    
        // Validate cache structure
        if (!validateCacheStructure(parsed)) {
            console.warn('Invalid cache structure detected, clearing cache');
            clearNotificationCache();
            return null;
        }

        // Convert ISO strings back to Date objects
        const cache: NotificationCache = {
            ...parsed,
            notifications: parsed.notifications.map((notification: Record<string, unknown>) => ({
                ...notification,
                scheduledTime: new Date(notification.scheduledTime as string)
            }))
        };

        return cache;
    } catch (error) {
        console.error('Failed to load notification cache:', error);
        clearNotificationCache();
        return null;
    }
}

/**
 * Check if the cache is valid (not expired and profile hasn't changed)
 */
export function isCacheValid(cache: NotificationCache | null, currentProfileHash: string): boolean {
    if (!cache) {
        return false;
    }

    const now = new Date();
    const expiresAt = new Date(cache.expiresAt);
  
    // Check if cache has expired
    if (now >= expiresAt) {
        return false;
    }

    // Check if profile has changed
    if (cache.profileHash !== currentProfileHash) {
        return false;
    }

    // Check if timezone has changed
    if (cache.timezone !== getCurrentTimezone()) {
        return false;
    }

    // Check cache version
    if (cache.version !== CACHE_VERSION) {
        return false;
    }

    return true;
}

/**
 * Clear the notification cache
 */
export function clearNotificationCache(): void {
    try {
        localStorage.removeItem(CACHE_KEY);
    } catch (error) {
        console.error('Failed to clear notification cache:', error);
    }
}

/**
 * Validate the structure of cached data
 */
function validateCacheStructure(data: unknown): boolean {
    if (!data || typeof data !== 'object') {
        return false;
    }
    
    const dataObj = data as Record<string, unknown>;

    const requiredFields = [
        'generatedAt',
        'expiresAt', 
        'profileHash',
        'notifications',
        'nextRegenerationTime',
        'version',
        'timezone'
    ];

    // Check if all required fields exist
    for (const field of requiredFields) {
        if (!(field in dataObj)) {
            return false;
        }
    }

    // Validate notifications array
    if (!Array.isArray(dataObj.notifications)) {
        return false;
    }

    // Validate each notification item
    for (const notification of dataObj.notifications) {
        if (!validateNotificationItem(notification)) {
            return false;
        }
    }

    return true;
}

/**
 * Validate a single notification item structure
 */
function validateNotificationItem(notification: unknown): boolean {
    if (!notification || typeof notification !== 'object') {
        return false;
    }
    
    const notificationObj = notification as Record<string, unknown>;

    const requiredFields = [
        'id',
        'scheduledTime',
        'message',
        'matchedKeywords',
        'priority',
        'sent',
        'read'
    ];

    for (const field of requiredFields) {
        if (!(field in notificationObj)) {
            return false;
        }
    }

    // Validate message structure
    if (!notificationObj.message || typeof notificationObj.message !== 'object') {
        return false;
    }

    const messageObj = notificationObj.message as Record<string, unknown>;
    const messageRequiredFields = ['id', 'title', 'content', 'tags'];
    for (const field of messageRequiredFields) {
        if (!(field in messageObj)) {
            return false;
        }
    }

    // Validate arrays
    if (!Array.isArray(notificationObj.matchedKeywords) ||
      !Array.isArray(messageObj.tags)) {
        return false;
    }

    // Validate priority
    if (!['high', 'medium', 'low'].includes(notificationObj.priority as string)) {
        return false;
    }

    return true;
}

/**
 * Get cache statistics for debugging
 */
export function getCacheStats(): {
  exists: boolean;
  size: number;
  valid: boolean;
  expiresAt: Date | null;
  generatedAt: Date | null;
  notificationCount: number;
  } {
    const cache = loadNotificationCache();
  
    if (!cache) {
        return {
            exists: false,
            size: 0,
            valid: false,
            expiresAt: null,
            generatedAt: null,
            notificationCount: 0
        };
    }

    const cacheString = localStorage.getItem(CACHE_KEY) || '';
  
    return {
        exists: true,
        size: new Blob([cacheString]).size,
        valid: isCacheValid(cache, ''), // We don't have profile hash here
        expiresAt: new Date(cache.expiresAt),
        generatedAt: new Date(cache.generatedAt),
        notificationCount: cache.notifications.length
    };
}

/**
 * Mark a notification as sent
 */
export function markNotificationSent(notificationId: string): void {
    const cache = loadNotificationCache();
    if (!cache) return;

    const notification = cache.notifications.find(n => n.id === notificationId);
    if (notification) {
        notification.sent = true;
        saveNotificationCache(cache.notifications, cache.profileHash, new Date(cache.expiresAt));
    }
}

/**
 * Mark a notification as read
 */
export function markNotificationRead(notificationId: string): void {
    const cache = loadNotificationCache();
    if (!cache) return;

    const notification = cache.notifications.find(n => n.id === notificationId);
    if (notification) {
        notification.read = true;
        saveNotificationCache(cache.notifications, cache.profileHash, new Date(cache.expiresAt));
    }
}