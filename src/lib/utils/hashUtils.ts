import type { Profile } from '$lib/stores/profile';

/**
 * Generate a simple hash from a string
 */
function simpleHash(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
  
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
  
    return Math.abs(hash).toString(36);
}

/**
 * Generate a hash from user profile to detect changes
 */
export function generateProfileHash(profile: Profile): string {
    // Create a deterministic string representation of relevant profile data
    const relevantData = {
        experiences: profile.experiences.map(exp => ({ name: exp.name, summary: exp.summary })).sort((a, b) => a.name.localeCompare(b.name)),
        obstacles: profile.obstacles.map(obs => ({ name: obs.name, summary: obs.summary })).sort((a, b) => a.name.localeCompare(b.name)),
        schedules: profile.schedules.map(schedule => ({
            id: schedule.id,
            name: schedule.name,
            selectedDays: [...schedule.selectedDays].sort(),
            timeRanges: schedule.timeRanges.map(range => ({
                id: range.id,
                startHour: range.startHour,
                endHour: range.endHour,
                maxPerHour: range.maxPerHour
            })).sort((a, b) => a.startHour - b.startHour)
        })).sort((a, b) => a.id - b.id)
    };
  
    const dataString = JSON.stringify(relevantData);
    return simpleHash(dataString);
}

/**
 * Generate a unique ID for notifications
 */
export function generateUniqueId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = Math.random().toString(36).substring(2, 8);
    return `${timestamp}-${randomPart}`;
}

/**
 * Generate a hash for a list of keywords
 */
export function generateKeywordHash(keywords: string[]): string {
    const sortedKeywords = [...keywords].sort().join('|');
    return simpleHash(sortedKeywords);
}