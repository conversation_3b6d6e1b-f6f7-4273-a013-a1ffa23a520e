/**
 * Date utility functions for the notification system
 */

/**
 * Add hours to a date
 */
export function addHours(date: Date, hours: number): Date {
    const result = new Date(date);
    result.setHours(result.getHours() + hours);
    return result;
}

/**
 * Add minutes to a date
 */
export function addMinutes(date: Date, minutes: number): Date {
    const result = new Date(date);
    result.setMinutes(result.getMinutes() + minutes);
    return result;
}

/**
 * Get the start of the current day
 */
export function getStartOfDay(date: Date = new Date()): Date {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
}

/**
 * Get the end of the current day
 */
export function getEndOfDay(date: Date = new Date()): Date {
    const result = new Date(date);
    result.setHours(23, 59, 59, 999);
    return result;
}

/**
 * Check if a date is within the next 24 hours
 */
export function isWithinNext24Hours(date: Date, from: Date = new Date()): boolean {
    const twentyFourHoursLater = addHours(from, 24);
    return date >= from && date <= twentyFourHoursLater;
}

/**
 * Get the current timezone
 */
export function getCurrentTimezone(): string {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Format hour as 12-hour time string (e.g., "9AM", "2PM")
 */
export function formatHour(hour: number): string {
    if (hour === 0) return '12AM';
    if (hour < 12) return `${hour}AM`;
    if (hour === 12) return '12PM';
    return `${hour - 12}PM`;
}

/**
 * Get day of week from date (0 = Sunday, 1 = Monday, etc.)
 */
export function getDayOfWeek(date: Date): number {
    return date.getDay();
}

/**
 * Convert day abbreviation to day index
 * Mon = 1, Tue = 2, ..., Sun = 0
 */
export function dayAbbreviationToIndex(dayAbbr: string): number {
    const dayMap: Record<string, number> = {
        'Sun': 0,
        'Mon': 1,
        'Tue': 2,
        'Wed': 3,
        'Thu': 4,
        'Fri': 5,
        'Sat': 6
    };
    return dayMap[dayAbbr] ?? -1;
}

/**
 * Get the next occurrence of a specific day of the week
 */
export function getNextOccurrenceOfDay(dayAbbr: string, from: Date = new Date()): Date {
    const targetDayIndex = dayAbbreviationToIndex(dayAbbr);
    if (targetDayIndex === -1) {
        throw new Error(`Invalid day abbreviation: ${dayAbbr}`);
    }

    const currentDayIndex = getDayOfWeek(from);
    let daysUntilTarget = targetDayIndex - currentDayIndex;
  
    // If the target day is today or has passed this week, get next week's occurrence
    if (daysUntilTarget <= 0) {
        daysUntilTarget += 7;
    }

    const result = new Date(from);
    result.setDate(result.getDate() + daysUntilTarget);
    return result;
}

/**
 * Generate evenly distributed minutes within an hour
 */
export function generateEvenlyDistributedMinutesInHour(count: number): number[] {
    if (count <= 0) return [];
    if (count >= 60) {
    // If we need 60 or more notifications per hour, just return every minute
        return Array.from({ length: 60 }, (_, i) => i);
    }
  
    const minutes: number[] = [];
    const interval = 60 / count; // How many minutes between each notification
  
    for (let i = 0; i < count; i++) {
    // Calculate the minute for this notification, rounding to nearest minute
        const minute = Math.round(i * interval);
        // Ensure we don't exceed 59 minutes
        minutes.push(Math.min(minute, 59));
    }
  
    return minutes;
}

/**
 * Generate random minutes within an hour for distribution (legacy function)
 * @deprecated Use generateEvenlyDistributedMinutesInHour for better distribution
 */
export function generateRandomMinutesInHour(count: number): number[] {
    const minutes: number[] = [];
    const usedMinutes = new Set<number>();
  
    while (minutes.length < count && minutes.length < 60) {
        const randomMinute = Math.floor(Math.random() * 60);
        if (!usedMinutes.has(randomMinute)) {
            minutes.push(randomMinute);
            usedMinutes.add(randomMinute);
        }
    }
  
    return minutes.sort((a, b) => a - b);
}

/**
 * Distribute notifications evenly within a time range
 */
export function distributeNotificationsInRange(
    startHour: number,
    endHour: number,
    maxPerHour: number,
    baseDate: Date
): Date[] {
    const notifications: Date[] = [];
  
    for (let hour = startHour; hour < endHour; hour++) {
        const minutesInHour = generateEvenlyDistributedMinutesInHour(maxPerHour);
    
        minutesInHour.forEach(minute => {
            const notificationTime = new Date(baseDate);
            notificationTime.setHours(hour, minute, 0, 0);
            notifications.push(notificationTime);
        });
    }
  
    return notifications;
}

/**
 * Check if two dates are on the same day
 */
export function isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
}

/**
 * Get the next midnight (start of next day)
 */
export function getNextMidnight(from: Date = new Date()): Date {
    const nextDay = new Date(from);
    nextDay.setDate(nextDay.getDate() + 1);
    return getStartOfDay(nextDay);
}