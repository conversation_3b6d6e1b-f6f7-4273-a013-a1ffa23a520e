import type { NotificationItem, NotificationStats } from '$lib/types/notifications';
import { notificationService } from './notificationService';
import { get } from 'svelte/store';
import { profile } from '$lib/stores/profile';
import { loadNotificationCache } from '$lib/utils/cacheUtils';

/**
 * API interface for native apps to interact with the notification system
 */
export class NotificationAPI {
  
    /**
   * Get upcoming notifications for the next N hours
   */
    static async getUpcomingNotifications(hours: number = 24): Promise<NotificationItem[]> {
        try {
            const currentProfile = get(profile);
            return await notificationService.getUpcomingNotifications(currentProfile, hours);
        } catch (error) {
            console.error('Failed to get upcoming notifications:', error);
            return [];
        }
    }

    /**
   * Get notifications for a specific time range
   */
    static async getNotificationsInRange(startTime: Date, endTime: Date): Promise<NotificationItem[]> {
        try {
            const currentProfile = get(profile);
            const allNotifications = await notificationService.ensureValidQueue(currentProfile);
      
            return allNotifications.filter(notification => 
                notification.scheduledTime >= startTime && 
        notification.scheduledTime <= endTime
            );
        } catch (error) {
            console.error('Failed to get notifications in range:', error);
            return [];
        }
    }

    /**
   * Get the next notification to be sent
   */
    static async getNextNotification(): Promise<NotificationItem | null> {
        try {
            const currentProfile = get(profile);
            const notifications = await notificationService.ensureValidQueue(currentProfile);
            const now = new Date();
      
            const upcomingNotifications = notifications
                .filter(n => n.scheduledTime > now && !n.sent)
                .sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime());
      
            return upcomingNotifications.length > 0 ? upcomingNotifications[0] : null;
        } catch (error) {
            console.error('Failed to get next notification:', error);
            return null;
        }
    }

    /**
   * Mark a notification as sent
   */
    static markNotificationSent(notificationId: string): boolean {
        try {
            notificationService.markNotificationSent(notificationId);
            return true;
        } catch (error) {
            console.error('Failed to mark notification as sent:', error);
            return false;
        }
    }

    /**
   * Mark a notification as read
   */
    static markNotificationRead(notificationId: string): boolean {
        try {
            notificationService.markNotificationRead(notificationId);
            return true;
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
            return false;
        }
    }

    /**
   * Get notification statistics
   */
    static async getNotificationStats(): Promise<NotificationStats | null> {
        try {
            const currentProfile = get(profile);
            return await notificationService.getNotificationStats(currentProfile);
        } catch (error) {
            console.error('Failed to get notification stats:', error);
            return null;
        }
    }

    /**
   * Check if the notification system is ready
   */
    static async isSystemReady(): Promise<boolean> {
        try {
            const currentProfile = get(profile);
      
            // Check if profile is complete
            if (!currentProfile.wizardCompleted) {
                return false;
            }

            // Check if we have schedules
            if (!currentProfile.schedules || currentProfile.schedules.length === 0) {
                return false;
            }

            // Check if we have preferences
            if ((!currentProfile.experiences || currentProfile.experiences.length === 0) &&
          (!currentProfile.obstacles || currentProfile.obstacles.length === 0)) {
                return false;
            }

            return true;
        } catch (error) {
            console.error('Failed to check system readiness:', error);
            return false;
        }
    }

    /**
   * Force regeneration of the notification queue
   */
    static async forceRegeneration(): Promise<boolean> {
        try {
            const currentProfile = get(profile);
            await notificationService.forceRegeneration(currentProfile);
            return true;
        } catch (error) {
            console.error('Failed to force regeneration:', error);
            return false;
        }
    }

    /**
   * Get notifications that should be sent now (within the last 5 minutes)
   */
    static async getNotificationsDueNow(): Promise<NotificationItem[]> {
        try {
            const currentProfile = get(profile);
            const notifications = await notificationService.ensureValidQueue(currentProfile);
            const now = new Date();
            const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
      
            return notifications.filter(notification => 
                notification.scheduledTime >= fiveMinutesAgo &&
        notification.scheduledTime <= now &&
        !notification.sent
            );
        } catch (error) {
            console.error('Failed to get notifications due now:', error);
            return [];
        }
    }

    /**
   * Get notifications by priority level
   */
    static async getNotificationsByPriority(priority: 'high' | 'medium' | 'low'): Promise<NotificationItem[]> {
        try {
            const currentProfile = get(profile);
            const notifications = await notificationService.ensureValidQueue(currentProfile);
      
            return notifications.filter(notification => notification.priority === priority);
        } catch (error) {
            console.error('Failed to get notifications by priority:', error);
            return [];
        }
    }

    /**
   * Get unread notifications
   */
    static async getUnreadNotifications(): Promise<NotificationItem[]> {
        try {
            const currentProfile = get(profile);
            const notifications = await notificationService.ensureValidQueue(currentProfile);
      
            return notifications.filter(notification => !notification.read);
        } catch (error) {
            console.error('Failed to get unread notifications:', error);
            return [];
        }
    }

    /**
   * Get sent but unread notifications
   */
    static async getSentUnreadNotifications(): Promise<NotificationItem[]> {
        try {
            const currentProfile = get(profile);
            const notifications = await notificationService.ensureValidQueue(currentProfile);
      
            return notifications.filter(notification => notification.sent && !notification.read);
        } catch (error) {
            console.error('Failed to get sent unread notifications:', error);
            return [];
        }
    }

    /**
   * Get cache information for debugging
   */
    static getCacheInfo(): {
    exists: boolean;
    valid: boolean;
    expiresAt: Date | null;
    notificationCount: number;
    size: number;
    } {
        try {
            const cache = loadNotificationCache();
      
            if (!cache) {
                return {
                    exists: false,
                    valid: false,
                    expiresAt: null,
                    notificationCount: 0,
                    size: 0
                };
            }

            const cacheString = localStorage.getItem('ybd_notification_queue') || '';
      
            return {
                exists: true,
                valid: new Date(cache.expiresAt) > new Date(),
                expiresAt: new Date(cache.expiresAt),
                notificationCount: cache.notifications.length,
                size: new Blob([cacheString]).size
            };
        } catch (error) {
            console.error('Failed to get cache info:', error);
            return {
                exists: false,
                valid: false,
                expiresAt: null,
                notificationCount: 0,
                size: 0
            };
        }
    }

    /**
   * Clear all notification data
   */
    static clearAllData(): boolean {
        try {
            notificationService.clearCache();
            return true;
        } catch (error) {
            console.error('Failed to clear notification data:', error);
            return false;
        }
    }

    /**
   * Get system health status
   */
    static async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'error';
    issues: string[];
    lastGenerated: Date | null;
    nextRegeneration: Date | null;
    notificationCount: number;
  }> {
        try {
            const issues: string[] = [];
            let status: 'healthy' | 'warning' | 'error' = 'healthy';

            // Check if system is ready
            const isReady = await this.isSystemReady();
            if (!isReady) {
                issues.push('System not ready - profile incomplete');
                status = 'error';
            }

            // Get actual notification count from the service (not just cache)
            const stats = await this.getNotificationStats();
            const currentProfile = get(profile);
            const notifications = await notificationService.ensureValidQueue(currentProfile);
            const actualNotificationCount = notifications.length;

            // Only check for notification issues if system is ready
            if (isReady) {
                if (actualNotificationCount === 0) {
                    issues.push('No notifications generated despite ready system');
                    status = status === 'error' ? 'error' : 'warning';
                }
            }

            // Check cache status only for informational purposes, not as an error
            const cacheInfo = this.getCacheInfo();
            if (isReady && actualNotificationCount > 0) {
                // System is working, cache issues are just informational
                if (!cacheInfo.exists) {
                    // Don't report cache missing as an issue if notifications exist
                } else if (!cacheInfo.valid) {
                    // Cache expired but notifications exist - this is normal
                }
            } else if (isReady && actualNotificationCount === 0) {
                // System ready but no notifications - cache status might be relevant
                if (!cacheInfo.exists) {
                    // Already reported "No notifications generated" above
                }
            }

            return {
                status,
                issues,
                lastGenerated: stats?.lastGenerated || null,
                nextRegeneration: stats?.nextRegeneration || null,
                notificationCount: actualNotificationCount
            };
        } catch (error) {
            console.error('Failed to get system health:', error);
            return {
                status: 'error',
                issues: ['Failed to check system health'],
                lastGenerated: null,
                nextRegeneration: null,
                notificationCount: 0
            };
        }
    }

    /**
   * Export notifications for external use (e.g., backup, analysis)
   */
    static async exportNotifications(): Promise<{
    exportedAt: Date;
    notifications: NotificationItem[];
    stats: NotificationStats | null;
  }> {
        try {
            const currentProfile = get(profile);
            const notifications = await notificationService.ensureValidQueue(currentProfile);
            const stats = await notificationService.getNotificationStats(currentProfile);

            return {
                exportedAt: new Date(),
                notifications,
                stats
            };
        } catch (error) {
            console.error('Failed to export notifications:', error);
            return {
                exportedAt: new Date(),
                notifications: [],
                stats: null
            };
        }
    }
}

// Export for global access (useful for native app bridges)
if (typeof window !== 'undefined') {
    (window as typeof window & { YBDNotificationAPI: typeof NotificationAPI }).YBDNotificationAPI = NotificationAPI;
}

export default NotificationAPI;