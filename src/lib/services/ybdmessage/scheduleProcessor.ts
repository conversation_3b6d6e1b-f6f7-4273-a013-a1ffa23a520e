import type { Schedule, TimeRange } from '$lib/stores/profile';
import type { TimeSlot } from '$lib/types/notifications';
import {
    dayAbbreviationToIndex,
    getDayOfWeek,
    distributeNotificationsInRange
} from '$lib/utils/dateUtils';

/**
 * Service for processing user schedules into specific notification time slots
 */
export class ScheduleProcessor {
  
    /**
   * Generate time slots for the next 7 days based on user schedules
   */
    public generateTimeSlots(schedules: Schedule[], startTime: Date = new Date()): TimeSlot[] {
        const timeSlots: TimeSlot[] = [];
        const endTime = new Date(startTime.getTime() + 7 * 24 * 60 * 60 * 1000);

        schedules.forEach(schedule => {
            const slotsForSchedule = this.processSchedule(schedule, startTime, endTime);
            timeSlots.push(...slotsForSchedule);
        });

        // Sort by time and remove any duplicates
        const sortedSlots = timeSlots.sort((a, b) => a.time.getTime() - b.time.getTime());
        return this.removeDuplicateSlots(sortedSlots);
    }

    /**
   * Process a single schedule to generate time slots
   */
    private processSchedule(schedule: Schedule, startTime: Date, endTime: Date): TimeSlot[] {
        const slots: TimeSlot[] = [];

        schedule.selectedDays.forEach(dayAbbr => {
            schedule.timeRanges.forEach(timeRange => {
                const slotsForRange = this.generateSlotsForTimeRange(
                    timeRange, 
                    dayAbbr, 
                    schedule.id,
                    startTime, 
                    endTime
                );
                slots.push(...slotsForRange);
            });
        });

        return slots;
    }

    /**
   * Generate time slots for a specific time range on a specific day
   */
    private generateSlotsForTimeRange(
        timeRange: TimeRange,
        dayAbbr: string,
        scheduleId: number,
        startTime: Date,
        endTime: Date
    ): TimeSlot[] {
        const slots: TimeSlot[] = [];
        const targetDayIndex = dayAbbreviationToIndex(dayAbbr);
    
        if (targetDayIndex === -1) {
            console.warn(`Invalid day abbreviation: ${dayAbbr}`);
            return slots;
        }

        // Find all occurrences of this day within the 24-hour window
        const dayOccurrences = this.findDayOccurrencesInWindow(targetDayIndex, startTime, endTime);

        dayOccurrences.forEach(dayDate => {
            const notificationTimes = distributeNotificationsInRange(
                timeRange.startHour,
                timeRange.endHour,
                timeRange.maxPerHour,
                dayDate
            );

            notificationTimes.forEach(time => {
                // Only include times within our time window
                if (time >= startTime && time <= endTime) {
                    slots.push({
                        time,
                        scheduleId,
                        timeRangeId: timeRange.id,
                        priority: this.calculateSlotPriority(timeRange, time)
                    });
                }
            });
        });

        return slots;
    }

    /**
   * Find all occurrences of a specific day within the time window
   */
    private findDayOccurrencesInWindow(targetDayIndex: number, startTime: Date, endTime: Date): Date[] {
        const occurrences: Date[] = [];
        const current = new Date(startTime);
    
        // Check each day in the window
        while (current <= endTime) {
            if (getDayOfWeek(current) === targetDayIndex) {
                occurrences.push(new Date(current));
            }
            current.setDate(current.getDate() + 1);
        }

        return occurrences;
    }

    /**
   * Calculate priority for a time slot based on time range and time of day
   */
    private calculateSlotPriority(timeRange: TimeRange, time: Date): 'high' | 'medium' | 'low' {
        const hour = time.getHours();
    
        // Morning hours (6-10 AM) get high priority
        if (hour >= 6 && hour < 10) {
            return 'high';
        }
    
        // Evening hours (6-9 PM) get high priority
        if (hour >= 18 && hour < 21) {
            return 'high';
        }
    
        // Afternoon hours (12-5 PM) get medium priority
        if (hour >= 12 && hour < 17) {
            return 'medium';
        }
    
        // All other hours get low priority
        return 'low';
    }

    /**
   * Remove duplicate time slots (same time, different schedules)
   */
    private removeDuplicateSlots(slots: TimeSlot[]): TimeSlot[] {
        const uniqueSlots: TimeSlot[] = [];
        const seenTimes = new Set<string>();

        slots.forEach(slot => {
            const timeKey = slot.time.toISOString();
            if (!seenTimes.has(timeKey)) {
                seenTimes.add(timeKey);
                uniqueSlots.push(slot);
            }
        });

        return uniqueSlots;
    }

    /**
   * Validate that time ranges don't overlap within the same schedule
   */
    public validateSchedule(schedule: Schedule): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        // Check for overlapping time ranges
        for (let i = 0; i < schedule.timeRanges.length; i++) {
            for (let j = i + 1; j < schedule.timeRanges.length; j++) {
                const range1 = schedule.timeRanges[i];
                const range2 = schedule.timeRanges[j];

                if (this.timeRangesOverlap(range1, range2)) {
                    errors.push(
                        `Time ranges overlap: ${this.formatTimeRange(range1)} and ${this.formatTimeRange(range2)}`
                    );
                }
            }
        }

        // Check for invalid time ranges
        schedule.timeRanges.forEach((range, index) => {
            if (range.startHour >= range.endHour) {
                errors.push(`Time range ${index + 1}: End time must be after start time`);
            }
      
            if (range.startHour < 0 || range.startHour > 23) {
                errors.push(`Time range ${index + 1}: Invalid start hour (${range.startHour})`);
            }
      
            if (range.endHour < 0 || range.endHour > 24) {
                errors.push(`Time range ${index + 1}: Invalid end hour (${range.endHour})`);
            }
      
            if (range.maxPerHour < 1 || range.maxPerHour > 60) {
                errors.push(`Time range ${index + 1}: Invalid max per hour (${range.maxPerHour})`);
            }
        });

        // Check for empty days
        if (schedule.selectedDays.length === 0) {
            errors.push('Schedule must have at least one day selected');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
   * Check if two time ranges overlap
   */
    private timeRangesOverlap(range1: TimeRange, range2: TimeRange): boolean {
        return range1.startHour < range2.endHour && range2.startHour < range1.endHour;
    }

    /**
   * Format a time range for display
   */
    private formatTimeRange(range: TimeRange): string {
        const formatHour = (hour: number): string => {
            if (hour === 0) return '12AM';
            if (hour < 12) return `${hour}AM`;
            if (hour === 12) return '12PM';
            return `${hour - 12}PM`;
        };

        return `${formatHour(range.startHour)} to ${formatHour(range.endHour)}`;
    }

    /**
   * Get statistics about schedule processing
   */
    public getScheduleStats(schedules: Schedule[], startTime: Date = new Date()): {
    totalSchedules: number;
    totalTimeRanges: number;
    totalDays: number;
    estimatedNotificationsPerDay: number;
    nextNotificationTime: Date | null;
    validationErrors: string[];
  } {
        const timeSlots = this.generateTimeSlots(schedules, startTime);
        const allErrors: string[] = [];

        // Validate all schedules
        schedules.forEach((schedule, index) => {
            const validation = this.validateSchedule(schedule);
            if (!validation.valid) {
                validation.errors.forEach(error => {
                    allErrors.push(`Schedule ${index + 1}: ${error}`);
                });
            }
        });

        const totalTimeRanges = schedules.reduce((sum, schedule) => sum + schedule.timeRanges.length, 0);
        const uniqueDays = new Set(schedules.flatMap(schedule => schedule.selectedDays));
        const nextNotification = timeSlots.length > 0 ? timeSlots[0].time : null;

        return {
            totalSchedules: schedules.length,
            totalTimeRanges,
            totalDays: uniqueDays.size,
            estimatedNotificationsPerDay: timeSlots.length,
            nextNotificationTime: nextNotification,
            validationErrors: allErrors
        };
    }

    /**
   * Get upcoming time slots for a specific number of hours
   */
    public getUpcomingSlots(schedules: Schedule[], hours: number = 168): TimeSlot[] {
        const startTime = new Date();
        const endTime = new Date(startTime.getTime() + hours * 60 * 60 * 1000);
    
        return this.generateTimeSlots(schedules, startTime).filter(slot => 
            slot.time >= startTime && slot.time <= endTime
        );
    }

    /**
   * Check if schedules have conflicts (overlapping days/times)
   */
    public findScheduleConflicts(schedules: Schedule[]): Array<{
    schedule1: number;
    schedule2: number;
    conflictType: 'day_overlap' | 'time_overlap';
    details: string;
  }> {
        const conflicts: Array<{
      schedule1: number;
      schedule2: number;
      conflictType: 'day_overlap' | 'time_overlap';
      details: string;
    }> = [];

        for (let i = 0; i < schedules.length; i++) {
            for (let j = i + 1; j < schedules.length; j++) {
                const schedule1 = schedules[i];
                const schedule2 = schedules[j];

                // Check for day overlaps
                const dayOverlaps = schedule1.selectedDays.filter(day => 
                    schedule2.selectedDays.includes(day)
                );

                if (dayOverlaps.length > 0) {
                    // Check for time range overlaps on overlapping days
                    schedule1.timeRanges.forEach(range1 => {
                        schedule2.timeRanges.forEach(range2 => {
                            if (this.timeRangesOverlap(range1, range2)) {
                                conflicts.push({
                                    schedule1: schedule1.id,
                                    schedule2: schedule2.id,
                                    conflictType: 'time_overlap',
                                    details: `Overlapping time ranges on ${dayOverlaps.join(', ')}: ${this.formatTimeRange(range1)} and ${this.formatTimeRange(range2)}`
                                });
                            }
                        });
                    });
                }
            }
        }

        return conflicts;
    }
}

// Export a singleton instance
export const scheduleProcessor = new ScheduleProcessor();