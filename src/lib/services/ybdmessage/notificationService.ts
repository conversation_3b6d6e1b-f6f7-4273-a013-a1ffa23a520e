import type { Profile } from '$lib/stores/profile';
import type { NotificationItem, NotificationStats, TimeSlot, MatchedMessage, YBDTag, NotificationCache } from '$lib/types/notifications';
import { messageMatcher } from './messageMatcher';
import { scheduleProcessor } from './scheduleProcessor';
import { generateProfileHash, generateUniqueId } from '$lib/utils/hashUtils';
import { 
    saveNotificationCache, 
    loadNotificationCache, 
    isCacheValid, 
    clearNotificationCache,
    markNotificationSent as cacheMarkSent,
    markNotificationRead as cacheMarkRead
} from '$lib/utils/cacheUtils';
import { addHours } from '$lib/utils/dateUtils';

/**
 * Main notification service that orchestrates message matching and scheduling
 */
export class NotificationService {
    private lastProfileHash: string = '';
    private regenerationTimer: number | null = null;

    /**
   * Ensure we have a valid notification queue, regenerating if necessary
   */
    public async ensureValidQueue(profile: Profile): Promise<NotificationItem[]> {
    // Always regenerate to ensure fresh notifications with latest algorithm
        return await this.regenerateQueue(profile);
    }

    /**
   * Regenerate the notification queue from scratch
   */
    public async regenerateQueue(profile: Profile): Promise<NotificationItem[]> {
        try {
            console.log('Regenerating notification queue...');
      
            // Always clear cache to ensure fresh generation
            clearNotificationCache();
      
            // Clear any existing timer
            this.clearRegenerationTimer();

            // Validate profile has necessary data
            if (!this.validateProfile(profile)) {
                console.warn('Profile validation failed, returning empty queue');
                return [];
            }

            // Generate time slots from schedules
            const timeSlots = scheduleProcessor.generateTimeSlots(profile.schedules);
      
            if (timeSlots.length === 0) {
                console.warn('No time slots generated from schedules');
                return [];
            }

            // Match messages to user preferences
            const matchedMessages = messageMatcher.matchMessages(profile);
      
            if (matchedMessages.length === 0) {
                console.warn('No messages matched user preferences');
                return [];
            }

            // Generate notifications by combining time slots with messages
            const notifications = this.generateNotifications(timeSlots, matchedMessages);

            // Cache the results
            const profileHash = generateProfileHash(profile);
            const expiresAt = addHours(new Date(), 24);
            saveNotificationCache(notifications, profileHash, expiresAt);

            // Schedule next regeneration
            this.scheduleNextRegeneration(expiresAt);
            this.lastProfileHash = profileHash;

            console.log(`Generated ${notifications.length} notifications for next 7 days`);
            return notifications;

        } catch (error) {
            console.error('Failed to regenerate notification queue:', error);
            return [];
        }
    }

    /**
   * Generate notification items by combining time slots with matched messages
   * Ensures no duplicate messages within the same hour
   */
    private generateNotifications(timeSlots: TimeSlot[], matchedMessages: MatchedMessage[]): NotificationItem[] {
        const notifications: NotificationItem[] = [];
    
        // Sort time slots chronologically first
        const sortedSlots = timeSlots.sort((a, b) => a.time.getTime() - b.time.getTime());
    
        // Group messages by priority for round-robin selection
        const messagesByPriority = {
            high: matchedMessages.filter(m => m.priority === 'high'),
            medium: matchedMessages.filter(m => m.priority === 'medium'),
            low: matchedMessages.filter(m => m.priority === 'low')
        };
    
        console.log('Messages available by priority:', {
            high: messagesByPriority.high.length,
            medium: messagesByPriority.medium.length,
            low: messagesByPriority.low.length
        });
    
        // Group time slots by hour to ensure no duplicate messages within the same hour
        const slotsByHour = new Map<string, TimeSlot[]>();
        sortedSlots.forEach(slot => {
            const hourKey = `${slot.time.getFullYear()}-${slot.time.getMonth()}-${slot.time.getDate()}-${slot.time.getHours()}`;
            if (!slotsByHour.has(hourKey)) {
                slotsByHour.set(hourKey, []);
            }
      slotsByHour.get(hourKey)!.push(slot);
        });
    
        console.log(`Processing ${slotsByHour.size} unique hours with time slots`);
    
        // Process each hour separately to ensure message variety within each hour
        slotsByHour.forEach((hourSlots, hourKey) => {
            console.log(`Processing hour ${hourKey} with ${hourSlots.length} slots`);
      
            // Create fresh round-robin indexes for this hour
            const hourlyMessageIndexes = {
                high: 0,
                medium: 0,
                low: 0
            };
      
            hourSlots.forEach((slot, slotIndex) => {
                // Get appropriate messages for this slot's priority
                const availableMessages = this.getMessagesForPriority(matchedMessages, slot.priority);
        
                if (availableMessages.length === 0) {
                    console.log(`No messages available for priority ${slot.priority}`);
                    return;
                }
        
                // Use round-robin selection within this hour to ensure variety
                let selectedMessage: MatchedMessage;
                if (slot.priority === 'high' && messagesByPriority.high.length > 0) {
                    selectedMessage = messagesByPriority.high[hourlyMessageIndexes.high % messagesByPriority.high.length];
                    hourlyMessageIndexes.high++;
                } else if (slot.priority === 'medium' && messagesByPriority.medium.length > 0) {
                    selectedMessage = messagesByPriority.medium[hourlyMessageIndexes.medium % messagesByPriority.medium.length];
                    hourlyMessageIndexes.medium++;
                } else if (slot.priority === 'low' && messagesByPriority.low.length > 0) {
                    selectedMessage = messagesByPriority.low[hourlyMessageIndexes.low % messagesByPriority.low.length];
                    hourlyMessageIndexes.low++;
                } else {
                    // Fallback to any available message with round-robin
                    selectedMessage = availableMessages[slotIndex % availableMessages.length];
                }

                if (selectedMessage) {
                    console.log(`Hour ${hourKey}, Slot ${slotIndex} (${slot.time.toLocaleTimeString()}): Selected message "${selectedMessage.message.message?.substring(0, 50)}..."`);
          
                    const notification: NotificationItem = {
                        id: generateUniqueId(),
                        scheduledTime: slot.time,
                        message: {
                            id: selectedMessage.message.id,
                            content: selectedMessage.message.message,
                            tags: selectedMessage.message.tags.map((tag: YBDTag) => tag.tag_name),
                            actionItems: selectedMessage.message.actionItems,
                            quote: selectedMessage.message.quote
                        },
                        matchedKeywords: selectedMessage.matchedKeywords,
                        priority: selectedMessage.priority,
                        sent: false,
                        read: false
                    };

                    notifications.push(notification);
                }
            });
        });

        return notifications;
    }

    /**
   * Get messages filtered by priority level
   */
    private getMessagesForPriority(matchedMessages: MatchedMessage[], priority: 'high' | 'medium' | 'low'): MatchedMessage[] {
        switch (priority) {
            case 'high':
                // For high priority slots, prefer high priority messages, but include others if needed
                return matchedMessages.filter(m => m.priority === 'high').length > 0
                    ? matchedMessages.filter(m => m.priority === 'high')
                    : matchedMessages;
      
            case 'medium':
                // For medium priority slots, prefer medium and high priority messages
                return matchedMessages.filter(m => ['high', 'medium'].includes(m.priority)).length > 0
                    ? matchedMessages.filter(m => ['high', 'medium'].includes(m.priority))
                    : matchedMessages;
      
            case 'low':
                // For low priority slots, any message is fine
                return matchedMessages;
      
            default:
                return matchedMessages;
        }
    }

    /**
   * Validate that profile has minimum required data
   */
    private validateProfile(profile: Profile): boolean {
        console.log('Validating profile:', profile);
    
        // Must have at least one schedule
        if (!profile.schedules || profile.schedules.length === 0) {
            console.warn('Profile validation failed: No schedules found');
            return false;
        }

        // Must have at least one experience or obstacle
        if ((!profile.experiences || profile.experiences.length === 0) &&
        (!profile.obstacles || profile.obstacles.length === 0)) {
            console.warn('Profile validation failed: No experiences or obstacles found');
            return false;
        }

        // Validate schedules
        for (const schedule of profile.schedules) {
            console.log('Validating schedule:', schedule);
            const validation = scheduleProcessor.validateSchedule(schedule);
            if (!validation.valid) {
                console.warn('Invalid schedule detected:', validation.errors);
                return false;
            }
        }

        console.log('Profile validation passed');
        return true;
    }

    /**
   * Get upcoming notifications for a specific time window
   */
    public async getUpcomingNotifications(profile: Profile, hours: number = 168): Promise<NotificationItem[]> {
        const notifications = await this.ensureValidQueue(profile);
        const cutoffTime = addHours(new Date(), hours);
    
        return notifications.filter(notification => 
            notification.scheduledTime <= cutoffTime && !notification.sent
        );
    }

    /**
   * Mark a notification as sent
   */
    public markNotificationSent(notificationId: string): void {
        cacheMarkSent(notificationId);
    }

    /**
   * Mark a notification as read
   */
    public markNotificationRead(notificationId: string): void {
        cacheMarkRead(notificationId);
    }

    /**
   * Clear the notification cache
   */
    public clearCache(): void {
        clearNotificationCache();
        this.clearRegenerationTimer();
    }

    /**
   * Check if profile has changed and trigger regeneration if needed
   */
    public async handleProfileChange(profile: Profile): Promise<NotificationItem[]> {
        console.log('Profile change detected, regenerating notifications');
        return await this.regenerateQueue(profile);
    }

    /**
   * Schedule the next automatic regeneration
   */
    private scheduleNextRegeneration(expiresAt: Date): void {
        this.clearRegenerationTimer();
    
        const now = new Date();
        const timeUntilExpiration = expiresAt.getTime() - now.getTime();
    
        if (timeUntilExpiration > 0) {
            this.regenerationTimer = window.setTimeout(() => {
                console.log('Automatic regeneration triggered');
                // Note: This would need access to the current profile
                // In practice, this should be handled by the store subscription
            }, timeUntilExpiration);
        }
    }

    /**
   * Clear the regeneration timer
   */
    private clearRegenerationTimer(): void {
        if (this.regenerationTimer !== null) {
            clearTimeout(this.regenerationTimer);
            this.regenerationTimer = null;
        }
    }

    /**
   * Get statistics about the notification system
   */
    public async getNotificationStats(profile: Profile): Promise<NotificationStats> {
        const notifications = await this.ensureValidQueue(profile);
        const cache = loadNotificationCache();
    
        return {
            totalGenerated: notifications.length,
            totalSent: notifications.filter(n => n.sent).length,
            totalRead: notifications.filter(n => n.read).length,
            lastGenerated: cache ? new Date(cache.generatedAt) : null,
            nextRegeneration: cache ? new Date(cache.expiresAt) : null,
            cacheStatus: this.getCacheStatus(cache, generateProfileHash(profile))
        };
    }

    /**
   * Get the current cache status
   */
    private getCacheStatus(cache: NotificationCache | null, profileHash: string): 'valid' | 'expired' | 'missing' | 'corrupted' {
        if (!cache) {
            return 'missing';
        }

        try {
            if (isCacheValid(cache, profileHash)) {
                return 'valid';
            } else {
                return 'expired';
            }
        } catch {
            return 'corrupted';
        }
    }

    /**
   * Get debug information about the notification system
   */
    public async getDebugInfo(profile: Profile): Promise<{
    profileHash: string;
    scheduleStats: Record<string, unknown>;
    matchingStats: Record<string, unknown>;
    cacheInfo: Record<string, unknown>;
    upcomingSlots: Array<{
      time: string;
      priority: 'high' | 'medium' | 'low';
      scheduleId: number;
    }>;
  }> {
        const profileHash = generateProfileHash(profile);
        const scheduleStats = scheduleProcessor.getScheduleStats(profile.schedules);
        const matchingStats = messageMatcher.getMatchingStats(profile);
        const cache = loadNotificationCache();
        const upcomingSlots = scheduleProcessor.getUpcomingSlots(profile.schedules, 24);

        return {
            profileHash,
            scheduleStats,
            matchingStats,
            cacheInfo: {
                exists: !!cache,
                valid: cache ? isCacheValid(cache, profileHash) : false,
                expiresAt: cache?.expiresAt,
                notificationCount: cache?.notifications.length || 0
            },
            upcomingSlots: upcomingSlots.map(slot => ({
                time: slot.time.toISOString(),
                priority: slot.priority,
                scheduleId: slot.scheduleId
            }))
        };
    }

    /**
   * Force regeneration (for testing/debugging)
   * @deprecated Use regenerateQueue() instead - it now always clears cache
   */
    public async forceRegeneration(profile: Profile): Promise<NotificationItem[]> {
        return await this.regenerateQueue(profile);
    }
}

// Export a singleton instance
export const notificationService = new NotificationService();