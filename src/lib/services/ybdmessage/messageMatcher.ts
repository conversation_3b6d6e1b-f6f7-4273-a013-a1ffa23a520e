import type { Profile } from '$lib/stores/profile';
import type { YBDMessage, MatchedMessage, PrioritizedMessages } from '$lib/types/notifications';
import { DEFAULT_YBD_MESSAGES } from '$lib/data/defaultMessages';

/**
 * Service for matching user preferences to available YBD messages
 */
export class MessageMatcher {
    private messagesByTag: Map<string, YBDMessage[]> = new Map();
    private allMessages: YBDMessage[] = [];

    constructor() {
        this.initializeMessages();
    }

    /**
   * Initialize and index messages by tags for efficient lookup
   */
    private initializeMessages(): void {
        this.allMessages = DEFAULT_YBD_MESSAGES;
    
        // Index messages by tag for faster lookup
        this.allMessages.forEach(message => {
            message.tags.forEach(tag => {
                const tagName = tag.tag_name.toLowerCase();
                if (!this.messagesByTag.has(tagName)) {
                    this.messagesByTag.set(tagName, []);
                }
        this.messagesByTag.get(tagName)!.push(message);
            });
        });
    }

    /**
   * Match messages to user profile and return prioritized results
   */
    public matchMessages(userProfile: Profile): MatchedMessage[] {
        const userKeywords = this.extractUserKeywords(userProfile);
        const prioritizedMessages = this.categorizeMessagesByPriority(userKeywords);
    
        const matchedMessages: MatchedMessage[] = [];

        // Add high priority messages (matching both MWE and obstacles)
        prioritizedMessages.high.forEach(message => {
            matchedMessages.push({
                message,
                matchedKeywords: this.getMatchingKeywords(message, userKeywords),
                priority: 'high',
                matchType: 'both'
            });
        });

        // Add medium priority messages (matching MWE only)
        prioritizedMessages.medium.forEach(message => {
            matchedMessages.push({
                message,
                matchedKeywords: this.getMatchingKeywords(message, userKeywords),
                priority: 'medium',
                matchType: 'mwe'
            });
        });

        // Add low priority messages (matching obstacles only)
        prioritizedMessages.low.forEach(message => {
            matchedMessages.push({
                message,
                matchedKeywords: this.getMatchingKeywords(message, userKeywords),
                priority: 'low',
                matchType: 'obstacle'
            });
        });

        // Add fallback messages if we don't have enough matches
        if (matchedMessages.length < 10) {
            prioritizedMessages.fallback.forEach(message => {
                // Avoid duplicates
                if (!matchedMessages.some(m => m.message.id === message.id)) {
                    matchedMessages.push({
                        message,
                        matchedKeywords: [],
                        priority: 'low',
                        matchType: 'fallback'
                    });
                }
            });
        }

        return matchedMessages;
    }

    /**
   * Extract keywords from user's MWE and obstacles
   */
    private extractUserKeywords(profile: Profile): {
    mweKeywords: string[];
    obstacleKeywords: string[];
    allKeywords: string[];
  } {
        const mweKeywords = profile.experiences.map(exp => exp.name.toLowerCase());
        const obstacleKeywords = profile.obstacles.map(obs => obs.name.toLowerCase());
        const allKeywords = [...mweKeywords, ...obstacleKeywords];

        return {
            mweKeywords,
            obstacleKeywords,
            allKeywords
        };
    }

    /**
   * Categorize messages by priority based on keyword matches
   */
    private categorizeMessagesByPriority(userKeywords: ReturnType<typeof this.extractUserKeywords>): PrioritizedMessages {
        const { mweKeywords, obstacleKeywords, allKeywords } = userKeywords;
    
        const high: YBDMessage[] = [];
        const medium: YBDMessage[] = [];
        const low: YBDMessage[] = [];
        const processed = new Set<string>();

        // Find messages matching both MWE and obstacles (high priority)
        allKeywords.forEach(keyword => {
            const messages = this.messagesByTag.get(keyword) || [];
            messages.forEach(message => {
                if (processed.has(message.id)) return;
        
                const messageKeywords = this.getMessageKeywords(message);
                const matchesMWE = mweKeywords.some(mwe => messageKeywords.includes(mwe));
                const matchesObstacle = obstacleKeywords.some(obs => messageKeywords.includes(obs));
        
                if (matchesMWE && matchesObstacle) {
                    high.push(message);
                    processed.add(message.id);
                }
            });
        });

        // Find messages matching MWE only (medium priority)
        mweKeywords.forEach(keyword => {
            const messages = this.messagesByTag.get(keyword) || [];
            messages.forEach(message => {
                if (processed.has(message.id)) return;
        
                medium.push(message);
                processed.add(message.id);
            });
        });

        // Find messages matching obstacles only (low priority)
        obstacleKeywords.forEach(keyword => {
            const messages = this.messagesByTag.get(keyword) || [];
            messages.forEach(message => {
                if (processed.has(message.id)) return;
        
                low.push(message);
                processed.add(message.id);
            });
        });

        // Get fallback messages (general motivational content)
        const fallback = this.getFallbackMessages(processed);

        return { high, medium, low, fallback };
    }

    /**
   * Get fallback messages for when user keywords don't match enough content
   */
    private getFallbackMessages(excludeIds: Set<string>): YBDMessage[] {
        const fallbackTags = ['motivation', 'inspiration', 'happiness', 'positivity'];
        const fallbackMessages: YBDMessage[] = [];
        const processed = new Set<string>();

        fallbackTags.forEach(tag => {
            const messages = this.messagesByTag.get(tag) || [];
            messages.forEach(message => {
                if (!excludeIds.has(message.id) && !processed.has(message.id)) {
                    fallbackMessages.push(message);
                    processed.add(message.id);
                }
            });
        });

        // If still not enough, add any remaining messages
        if (fallbackMessages.length < 5) {
            this.allMessages.forEach(message => {
                if (!excludeIds.has(message.id) && !processed.has(message.id) && fallbackMessages.length < 10) {
                    fallbackMessages.push(message);
                    processed.add(message.id);
                }
            });
        }

        return fallbackMessages;
    }

    /**
   * Get keywords from a message's tags
   */
    private getMessageKeywords(message: YBDMessage): string[] {
        return message.tags.map(tag => tag.tag_name.toLowerCase());
    }

    /**
   * Get the keywords that match between a message and user keywords
   */
    private getMatchingKeywords(message: YBDMessage, userKeywords: ReturnType<typeof this.extractUserKeywords>): string[] {
        const messageKeywords = this.getMessageKeywords(message);
        return userKeywords.allKeywords.filter(keyword => messageKeywords.includes(keyword));
    }

    /**
   * Select a random message from a list
   */
    public selectRandomMessage(messages: MatchedMessage[]): MatchedMessage | null {
        if (messages.length === 0) return null;
    
        const randomIndex = Math.floor(Math.random() * messages.length);
        return messages[randomIndex];
    }

    /**
   * Select random messages by priority, ensuring variety
   */
    public selectRandomMessagesByPriority(
        matchedMessages: MatchedMessage[],
        count: number
    ): MatchedMessage[] {
        const selected: MatchedMessage[] = [];
        const remaining = [...matchedMessages];

        // Prioritize high priority messages
        const highPriority = remaining.filter(m => m.priority === 'high');
        const mediumPriority = remaining.filter(m => m.priority === 'medium');
        const lowPriority = remaining.filter(m => m.priority === 'low');

        // Select from high priority first
        while (selected.length < count && highPriority.length > 0) {
            const randomIndex = Math.floor(Math.random() * highPriority.length);
            selected.push(highPriority.splice(randomIndex, 1)[0]);
        }

        // Then medium priority
        while (selected.length < count && mediumPriority.length > 0) {
            const randomIndex = Math.floor(Math.random() * mediumPriority.length);
            selected.push(mediumPriority.splice(randomIndex, 1)[0]);
        }

        // Finally low priority
        while (selected.length < count && lowPriority.length > 0) {
            const randomIndex = Math.floor(Math.random() * lowPriority.length);
            selected.push(lowPriority.splice(randomIndex, 1)[0]);
        }

        return selected;
    }

    /**
   * Get statistics about message matching
   */
    public getMatchingStats(userProfile: Profile): {
    totalMessages: number;
    highPriorityMatches: number;
    mediumPriorityMatches: number;
    lowPriorityMatches: number;
    fallbackMessages: number;
    userKeywords: string[];
  } {
        const userKeywords = this.extractUserKeywords(userProfile);
        const prioritized = this.categorizeMessagesByPriority(userKeywords);

        return {
            totalMessages: this.allMessages.length,
            highPriorityMatches: prioritized.high.length,
            mediumPriorityMatches: prioritized.medium.length,
            lowPriorityMatches: prioritized.low.length,
            fallbackMessages: prioritized.fallback.length,
            userKeywords: userKeywords.allKeywords
        };
    }

    /**
   * Get all available tags for debugging
   */
    public getAvailableTags(): string[] {
        return Array.from(this.messagesByTag.keys()).sort();
    }
}

// Export a singleton instance
export const messageMatcher = new MessageMatcher();