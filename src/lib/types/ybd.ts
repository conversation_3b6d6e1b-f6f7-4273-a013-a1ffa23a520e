export interface YBDQuote {
	id: string;
	quote: string;
	quote_author: string;
}

export interface YBDAction {
	id: string;
	action: string;
}

export interface YBDMessage {
	id: string;
	message?: string;
	quote?: YBDQuote;
	status?: string; // Example: 'active'
	date_updated?: Date | string;
	favorite: boolean;
	actionItems?: YBDAction[];
}

export interface RandomThought {
	id: string;
	thought: string;
}

export interface AudioFile {
	id: string;
	remoteUrl: string;
}