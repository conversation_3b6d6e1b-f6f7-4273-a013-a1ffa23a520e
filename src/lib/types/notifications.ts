export interface NotificationItem {
  id: string;
  scheduledTime: Date;
  message: {
    id: string;
    content: string;
    tags: string[];
    actionItems?: ActionItem[];
    quote?: Quote;
  };
  matchedKeywords: string[]; // Which MWE/obstacles triggered this
  priority: 'high' | 'medium' | 'low'; // Based on match quality
  sent: boolean; // Tracking for native app
  read: boolean; // User interaction tracking
}

export interface NotificationCache {
  generatedAt: string; // ISO string for JSON serialization
  expiresAt: string; // ISO string for JSON serialization
  profileHash: string; // To detect profile changes
  notifications: NotificationItem[];
  nextRegenerationTime: string; // ISO string for JSON serialization
  version: string; // For cache format versioning
  timezone: string; // To detect timezone changes
}

export interface TimeSlot {
  time: Date;
  scheduleId: number;
  timeRangeId: number;
  priority: 'high' | 'medium' | 'low';
}

export interface MatchedMessage {
  message: YBDMessage;
  matchedKeywords: string[];
  priority: 'high' | 'medium' | 'low';
  matchType: 'both' | 'mwe' | 'obstacle' | 'fallback';
}

export interface PrioritizedMessages {
  high: YBDMessage[];
  medium: YBDMessage[];
  low: YBDMessage[];
  fallback: YBDMessage[];
}

export interface NotificationStats {
  totalGenerated: number;
  totalSent: number;
  totalRead: number;
  lastGenerated: Date | null;
  nextRegeneration: Date | null;
  cacheStatus: 'valid' | 'expired' | 'missing' | 'corrupted';
}

// Re-export types from existing files for convenience
export interface ActionItem {
  id: string;
  action: string;
  dateLastTaken: string | null;
  favorite: boolean;
}

export interface Quote {
  id: string;
  quote: string;
  quote_author: string;
}

export interface YBDTag {
  id: number;
  tag_name: string;
  status: string;
  date_updated: string;
}

export interface YBDMessage {
  id: string;
  message: string;
  status: string;
  date_updated: string;
  dateLastViewed: string | null;
  favorite: boolean;
  actionItems: ActionItem[];
  quote: Quote;
  tags: YBDTag[];
}