export interface Flashcard {
	id: string;
	front: string;
	back: string;
	favorite: boolean;
	dynamic_type?: string; // Optional: links to specific dynamics (e.g., "Anger-Based", "Fear-Based")
}

export interface BeliefExplorerStep {
	id: string;
	name: string;
	description: string;
	flashcards: Flashcard[];
}

export interface BeliefExplorerData {
	steps: BeliefExplorerStep[];
	dynamics: string[];
	coreDynamics: CoreDynamic[];
	beliefs: string[];
	themes: Theme[];
}

export interface CoreDynamic {
	id: string;
	name: string;
	description: string;
	icon?: string;
}

export interface Theme {
	id: string;
	name: string;
	description: string;
	color?: string;
	icon?: string;
}

export interface Goal {
	id: string;
	text: string;
	whyChain: string[];
}

export interface BeliefAlignment {
	beliefId: string;
	goalId: string;
	supports: boolean; // true = supports, false = hinders
}

export interface SessionHistory {
	id: string;
	timestamp: Date;
	coreDynamic: string | null;
	selectedBeliefs: string[];
	selectedTheme: string | null;
	goals: Goal[];
	beliefAlignments: BeliefAlignment[];
	completed: boolean;
}

export interface BeliefExplorerState {
	// Current session data
	currentStepIndex: number;
	selectedDynamic: string | null;
	selectedBeliefs: string[];
	selectedTheme: string | null;
	goals: Goal[];
	beliefAlignments: BeliefAlignment[];
	
	// UI state
	starredFlashcards: string[];
	currentFlashcards: Flashcard[];
	
	// Session management
	sessionId: string | null;
	sessionHistory: SessionHistory[];
	autoSaveEnabled: boolean;
}