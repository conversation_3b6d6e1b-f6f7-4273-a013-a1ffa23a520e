# YourBestDays Notification System

A comprehensive notification system that generates personalized message lists based on user preferences and schedules. The system creates a 24-hour notification queue that updates daily and regenerates when user profiles change.

## Quick Start

```typescript
import { quickSetup, getQuickStats } from '$lib/notifications';

// Initialize the notification system
await quickSetup();

// Get system status
const stats = await getQuickStats();
console.log('System ready:', stats.health.status === 'healthy');
```

## Core Features

- **Personalized Message Matching**: Matches user's Most Wanted Experiences (MWE) and obstacles to relevant messages
- **Smart Scheduling**: Converts user schedules into specific notification times
- **24-Hour Queue**: Generates notifications for the next 24 hours only
- **Automatic Regeneration**: Updates daily and when profile changes
- **localStorage Caching**: Persistent storage with timestamp validation
- **Native App Integration**: Clean API for iOS/Android push notifications

## Architecture

### Services

#### MessageMatcher
Matches user preferences to available YBD messages using exact keyword matching.

```typescript
import { messageMatcher } from '$lib/notifications';

const matchedMessages = messageMatcher.matchMessages(userProfile);
const stats = messageMatcher.getMatchingStats(userProfile);
```

#### ScheduleProcessor
Converts user schedules into specific notification time slots.

```typescript
import { scheduleProcessor } from '$lib/notifications';

const timeSlots = scheduleProcessor.generateTimeSlots(userSchedules);
const validation = scheduleProcessor.validateSchedule(schedule);
```

#### NotificationService
Main orchestrator that combines message matching with scheduling.

```typescript
import { notificationService } from '$lib/notifications';

const notifications = await notificationService.ensureValidQueue(profile);
await notificationService.regenerateQueue(profile);
```

### Stores

#### notificationQueue
Main Svelte store for the notification list.

```typescript
import { 
  notificationQueue, 
  queueStatus, 
  upcomingNotifications,
  initializeNotificationQueue 
} from '$lib/notifications';

// Initialize
await initializeNotificationQueue();

// Subscribe to updates
notificationQueue.subscribe(notifications => {
  console.log(`${notifications.length} notifications ready`);
});
```

#### Derived Stores

```typescript
import { 
  upcomingNotifications,    // Next 6 hours
  todaysNotifications,      // Today only
  unreadNotifications,      // Not read yet
  sentUnreadNotifications   // Sent but not read
} from '$lib/notifications';
```

### Native App API

Clean interface for native iOS/Android integration.

```typescript
import NotificationAPI from '$lib/notifications';

// Get notifications due now
const dueNow = await NotificationAPI.getNotificationsDueNow();

// Mark as sent
NotificationAPI.markNotificationSent(notificationId);

// Get system health
const health = await NotificationAPI.getSystemHealth();
```

## Usage Examples

### Basic Setup in a Svelte Component

```svelte
<script lang="ts">
  import { onMount } from 'svelte';
  import { 
    notificationQueue, 
    queueStatus, 
    initializeNotificationQueue 
  } from '$lib/notifications';

  onMount(async () => {
    await initializeNotificationQueue();
  });
</script>

{#if $queueStatus === 'ready'}
  <p>{$notificationQueue.length} notifications ready</p>
{:else if $queueStatus === 'loading'}
  <p>Loading notifications...</p>
{:else if $queueStatus === 'error'}
  <p>Error loading notifications</p>
{/if}
```

### Profile Change Handling

The system automatically monitors profile changes:

```typescript
import { startProfileMonitoring, stopProfileMonitoring } from '$lib/notifications';

// Start automatic monitoring (happens by default)
startProfileMonitoring();

// Stop monitoring (cleanup)
stopProfileMonitoring();
```

### Manual Regeneration

```typescript
import { regenerateNotificationQueue, forceRegenerateQueue } from '$lib/notifications';

// Regenerate if cache is invalid
await regenerateNotificationQueue();

// Force regeneration (clears cache first)
await forceRegenerateQueue();
```

### Getting Upcoming Notifications

```typescript
import NotificationAPI from '$lib/notifications';

// Next 24 hours (default)
const upcoming = await NotificationAPI.getUpcomingNotifications();

// Next 6 hours
const soon = await NotificationAPI.getUpcomingNotifications(6);

// Next notification
const next = await NotificationAPI.getNextNotification();
```

### Marking Notifications

```typescript
import { markNotificationSent, markNotificationRead } from '$lib/notifications';

// Mark as sent (for native app tracking)
markNotificationSent(notificationId);

// Mark as read (user interaction)
markNotificationRead(notificationId);
```

## Data Structures

### NotificationItem

```typescript
interface NotificationItem {
  id: string;
  scheduledTime: Date;
  message: {
    id: string;
    title: string;
    content: string;
    tags: string[];
    actionItems?: ActionItem[];
    quote?: Quote;
  };
  matchedKeywords: string[];
  priority: 'high' | 'medium' | 'low';
  sent: boolean;
  read: boolean;
}
```

### Profile Requirements

For the notification system to work, the user profile must have:

1. **At least one schedule** with valid time ranges
2. **At least one experience or obstacle** selected
3. **Completed wizard** (`wizardCompleted: true`)

## Message Matching Algorithm

1. **Extract Keywords**: Get lowercase names from user's MWE and obstacles
2. **Find Matches**: Look for messages with matching tags (exact keyword match)
3. **Prioritize**:
   - **High**: Messages matching both MWE + obstacles
   - **Medium**: Messages matching MWE only
   - **Low**: Messages matching obstacles only
   - **Fallback**: General motivational messages if no matches
4. **Random Selection**: Avoid repetition within priority groups

## Schedule Processing

1. **Parse Schedules**: Extract days and time ranges
2. **Generate Time Slots**: Create specific timestamps within ranges
3. **Respect Limits**: Honor `maxPerHour` settings
4. **24-Hour Window**: Only include times in next 24 hours
5. **Priority Assignment**: Based on time of day (morning/evening = high)

## Caching Strategy

- **localStorage Key**: `ybd_notification_queue`
- **Cache Duration**: 24 hours
- **Invalidation Triggers**:
  - Profile changes (experiences, obstacles, schedules)
  - Cache expiration
  - Timezone changes
  - Manual clearing
- **Fallback**: Graceful degradation on cache corruption

## Error Handling

### Common Issues

1. **No Schedules**: Returns empty queue with guidance message
2. **No Matching Messages**: Falls back to general motivational content
3. **Invalid Schedules**: Validation errors with specific details
4. **Cache Corruption**: Automatic regeneration with error logging

### Debugging

```typescript
import { getNotificationDebugInfo } from '$lib/notifications';

const debug = await getNotificationDebugInfo();
console.log('Debug info:', debug);
```

## Performance Considerations

- **Lazy Loading**: YBD messages loaded only when needed
- **Efficient Indexing**: Messages pre-indexed by tags
- **Debounced Updates**: Profile changes debounced to avoid excessive regeneration
- **Background Processing**: Uses `requestIdleCallback` when available

## Testing

Visit `/notifications-test` in your app to see the notification system in action with:

- Real-time status monitoring
- Manual regeneration controls
- Debug information display
- Notification preview and interaction

## Integration with Native Apps

### iOS/Android Bridge

```javascript
// Expose API globally for native app access
window.YBDNotificationAPI = NotificationAPI;

// Native app can call:
const notifications = await window.YBDNotificationAPI.getNotificationsDueNow();
```

### Recommended Native App Flow

1. **Check System Ready**: `await NotificationAPI.isSystemReady()`
2. **Get Due Notifications**: `await NotificationAPI.getNotificationsDueNow()`
3. **Send Push Notifications**: Use native push notification APIs
4. **Mark as Sent**: `NotificationAPI.markNotificationSent(id)`
5. **Handle User Interaction**: `NotificationAPI.markNotificationRead(id)`

## Future Enhancements

- **Smart Scheduling**: Learn from user interaction patterns
- **Advanced Matching**: Semantic similarity beyond exact keywords
- **Analytics**: Track notification effectiveness
- **A/B Testing**: Test different message variations
- **Social Features**: Shared goals and community messages

## Troubleshooting

### System Not Ready
- Ensure user has completed the wizard
- Check that schedules are configured
- Verify MWE or obstacles are selected

### No Notifications Generated
- Check schedule validation errors
- Verify time ranges are valid
- Ensure messages exist with matching tags

### Cache Issues
- Clear cache: `clearNotificationQueue()`
- Check browser localStorage limits
- Verify no localStorage corruption

### Performance Issues
- Monitor console for error messages
- Check debug info for bottlenecks
- Ensure profile changes aren't too frequent

## Support

For issues or questions about the notification system:

1. Check the test page at `/notifications-test`
2. Review debug information
3. Check browser console for errors
4. Verify profile completeness