// Main notification system exports

// Types
export type {
    NotificationItem,
    NotificationCache,
    TimeSlot,
    MatchedMessage,
    PrioritizedMessages,
    NotificationStats,
    ActionItem,
    Quote,
    YBDTag,
    YBDMessage
} from '../types/notifications';

// Services
export { MessageMatcher, messageMatcher } from '../services/ybdmessage/messageMatcher';
export { ScheduleProcessor, scheduleProcessor } from '../services/ybdmessage/scheduleProcessor';
export { NotificationService, notificationService } from '../services/ybdmessage/notificationService';
export { default as NotificationAPI } from '../services/ybdmessage/notificationAPI';

// Stores
export {
    notificationQueue,
    queueStatus,
    lastGenerated,
    queueError,
    queueStats,
    upcomingNotifications,
    todaysNotifications,
    unreadNotifications,
    sentUnreadNotifications,
    initializeNotificationQueue,
    regenerateNotificationQueue,
    handleProfileChange,
    markNotificationSent,
    markNotificationRead,
    getUpcomingNotifications,
    clearNotificationQueue,
    getNotificationDebugInfo,
    forceRegenerateQueue,
    startProfileMonitoring,
    stopProfileMonitoring
} from '../stores/notificationQueue';

// Utilities
export {
    addHours,
    addMinutes,
    getStartOfDay,
    getEndOfDay,
    isWithinNext24Hours,
    getCurrentTimezone,
    formatHour,
    getDayOfWeek,
    dayAbbreviationToIndex,
    getNextOccurrenceOfDay,
    generateRandomMinutesInHour,
    distributeNotificationsInRange,
    isSameDay,
    getNextMidnight
} from '../utils/dateUtils';

export {
    generateProfileHash,
    generateUniqueId,
    generateKeywordHash
} from '../utils/hashUtils';

export {
    saveNotificationCache,
    loadNotificationCache,
    isCacheValid,
    clearNotificationCache,
    getCacheStats,
    markNotificationSent as cacheMarkSent,
    markNotificationRead as cacheMarkRead
} from '../utils/cacheUtils';

// Constants
export const NOTIFICATION_SYSTEM_VERSION = '1.0.0';
export const CACHE_KEY = 'ybd_notification_queue';
export const DEFAULT_CACHE_DURATION_HOURS = 24;
export const DEFAULT_UPCOMING_HOURS = 6;

// Helper functions for common use cases
export async function quickSetup() {
    const { initializeNotificationQueue, startProfileMonitoring } = await import('../stores/notificationQueue');
  
    // Initialize the notification system
    await initializeNotificationQueue();
  
    // Start monitoring profile changes
    startProfileMonitoring();
  
    console.log('YBD Notification System initialized');
}

export async function getSystemStatus() {
    const { default: NotificationAPI } = await import('../services/ybdmessage/notificationAPI');
    return await NotificationAPI.getSystemHealth();
}

export async function getQuickStats() {
    const { default: NotificationAPI } = await import('../services/ybdmessage/notificationAPI');
  
    const [stats, health, upcoming] = await Promise.all([
        NotificationAPI.getNotificationStats(),
        NotificationAPI.getSystemHealth(),
        NotificationAPI.getUpcomingNotifications(6)
    ]);
  
    return {
        stats,
        health,
        upcomingCount: upcoming.length,
        nextNotification: upcoming.length > 0 ? upcoming[0] : null
    };
}

// Export everything as a namespace as well
export * as NotificationSystem from './index';