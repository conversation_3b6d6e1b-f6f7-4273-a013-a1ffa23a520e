export const FEATURES = {
    // Core features (always free after login)
    AUTH: { requiresSubscription: false },
    PROFILE: { requiresSubscription: false },
    SETTINGS: { requiresSubscription: false },
    ONBOARDING: { requiresSubscription: false },
  
    // Premium features
    BELIEFS_EXPLORER: { requiresSubscription: true },
    YBD_MESSAGES: { requiresSubscription: true },
    CUSTOM_SCHEDULES: { requiresSubscription: true },
    AUDIO_MESSAGES: { requiresSubscription: true },
    ANALYTICS: { requiresSubscription: true },
  
    // Features that might have limited free access later
    BASIC_MESSAGES: { 
        requiresSubscription: true, // For now, all messages are premium
        limits: { messagesPerDay: 3 } // Ready for future freemium implementation
    }
} as const;

// Helper to check if a feature requires subscription
export function requiresSubscription(feature: keyof typeof FEATURES): boolean {
    return FEATURES[feature]?.requiresSubscription ?? true;
}

// Free routes that don't require subscription
export const FREE_ROUTES = [
    '/app/free',
    '/app/profile',
    '/app/settings',
    '/app/onboarding',
    '/pricing',
    '/payment/success',
    '/payment/cancel',
    '/auth',
    '/debug' // Debug routes in dev mode
];

export function isFreeRoute(pathname: string): boolean {
    return FREE_ROUTES.some(route => pathname.startsWith(route));
}

// Subscription status types that grant access
export const ACTIVE_STATUSES = ['active', 'trialing', 'past_due'];

// Grace period for past_due status (in days)
export const PAST_DUE_GRACE_PERIOD_DAYS = 3;