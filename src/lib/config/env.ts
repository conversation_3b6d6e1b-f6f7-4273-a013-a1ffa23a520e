import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, PUBLIC_STRIPE_PUBLISHABLE_KEY, PUBLIC_APP_URL } from '$env/static/public'

// Validate required environment variables for client-side app
export function validateEnvironment() {
    const required = {
        PUBLIC_SUPABASE_URL,
        PUBLIC_SUPABASE_ANON_KEY,
        PUBLIC_STRIPE_PUBLISHABLE_KEY,
        PUBLIC_APP_URL
    }

    const missing = Object.entries(required)
        .filter(([, value]) => !value)
        .map(([key]) => key)

    if (missing.length > 0) {
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
    }

    return required
}

// Export validated environment variables
export const env = validateEnvironment()

// Helper to check if we're in development mode
export const isDev = PUBLIC_APP_URL?.includes('localhost') || PUBLIC_APP_URL?.includes('127.0.0.1')

// Helper to get the correct redirect URLs for different environments
export const getRedirectUrls = () => {
    const baseUrl = PUBLIC_APP_URL || 'http://localhost:5173'
    return {
        signIn: `${baseUrl}/auth/callback`,
        signOut: `${baseUrl}/`,
        passwordReset: `${baseUrl}/auth/reset-password`
    }
}