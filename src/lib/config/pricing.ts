// Centralized pricing configuration
export interface PricingPlan {
    name: string;
    priceId: string;
    price: string;
    subInterval: number;
    subIntervalType: 'yr' | 'mo';
    description: string;
    features: string[];
    paymentLink: string;
    recommended: boolean;
}

// TODO: Replace with actual Stripe price IDs when available
export const PRICING_PLANS: PricingPlan[] = [
    {
        name: 'Yearly Plan',
        priceId: 'price_1RhO2CD7PiFPw0TZw7fifvBK',
        price: '35.00',
        subInterval: 1,
        subIntervalType: 'yr',
        description: 'Prefer annual power renewals?',
        features: ['Renews once per year', 'Includes all features', '7-day free trial'],
        paymentLink: '#',
        recommended: true
    },
    {
        name: '6 Month Plan',
        priceId: 'price_1RhO2CD7PiFPw0TZSPhpNPHj',
        price: '18.00',
        subInterval: 6,
        subIntervalType: 'mo',
        description: 'Prefer 6 month power renewals?',
        features: ['Renews every 6 months', 'Includes all features', '7-day free trial'],
        paymentLink: '#',
        recommended: false
    },
    {
        name: '3 Month Plan',
        priceId: 'price_1RhO2CD7PiFPw0TZiUeAgmVI',
        price: '10.00',
        subInterval: 3,
        subIntervalType: 'mo',
        description: 'Prefer 3 month power renewals?',
        features: ['Renews every 3 months', 'Includes all features', '7-day free trial'],
        paymentLink: '#',
        recommended: false
    }
];

// Feature list for premium access
export const PREMIUM_FEATURES = [
    'Unlimited YBD messages',
    'Profile wizard',
    'Custom notification schedules',
    'Priority support'
];