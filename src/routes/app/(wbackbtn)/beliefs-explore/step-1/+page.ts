import type { PageLoad } from './$types';
import type { BeliefExplorerData } from '$lib/types/beliefs';

export const load: PageLoad = async ({ fetch }) => {
    try {
        const response = await fetch('/beliefs_explorer_data.json');
        const beliefsExplorerData: BeliefExplorerData = await response.json();
		
        return {
            beliefsExplorerData
        };
    } catch (error) {
        console.error('Failed to load beliefs explorer data:', error);
        throw error;
    }
};