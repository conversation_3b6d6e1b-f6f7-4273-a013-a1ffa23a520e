<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { fade } from 'svelte/transition';
    import {
        beliefsExplorerState,
        beliefsExplorerActions
    } from '$lib/stores/beliefsExplorer';
    import type { BeliefExplorerData } from '$lib/types/beliefs';
	
    import ProgressIndicator from '$lib/components/Wizard/ProgressIndicator.svelte';
    import CoreDynamicSelector from '$lib/components/Wizard/CoreDynamicSelector.svelte';
	
    export let data: { beliefsExplorerData: BeliefExplorerData };
	
    $: canProceed = $beliefsExplorerState.selectedDynamic !== null;
	
    function handleDynamicSelect(event: CustomEvent<{ dynamic: string }>) {
        beliefsExplorerActions.selectDynamic(event.detail.dynamic);
    }
	
    function handleNext() {
        if (canProceed) {
            beliefsExplorerActions.nextStep();
            goto('/beliefs-explore/step-2');
        }
    }
	
	
    onMount(() => {
        // Ensure we're on the correct step
        beliefsExplorerActions.goToStep(0);
		
        // Initialize data if not already loaded
        if (!$beliefsExplorerState.sessionId) {
            beliefsExplorerActions.loadData(data.beliefsExplorerData);
            beliefsExplorerActions.startNewSession();
        }
    });
</script>

<svelte:head>
    <title>Step 1: Notice Discomfort - Beliefs Explorer</title>
</svelte:head>

<div class="relative bg-background px-6 py-8">
    <div class="relative z-10 max-w-2xl mx-auto flex flex-col">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-5xl md:text-6xl font-bold text-foreground mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Beliefs Explorer
            </h1>
            <p class="text-lg text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                Step 1: Notice Discomfort & Identify Core Dynamic
            </p>
        </div>
		
        <!-- Progress Indicator -->
        <ProgressIndicator
            current={1}
            total={6}
            showLabels={true}
        />
		
        <!-- Step Content -->
        <div class="flex-1 flex flex-col" transition:fade={{ duration: 300 }}>
            <div class="flex flex-col gap-8">
                <div class="w-full">
                    <CoreDynamicSelector
                        coreDynamics={data.beliefsExplorerData.coreDynamics}
                        selectedDynamic={$beliefsExplorerState.selectedDynamic}
                        on:select={handleDynamicSelect}
                    />
                </div>
            </div>
        </div>
		
        <!-- Navigation -->
        <div class="wizard-navigation">
            <div class="nav-container">
                <div class="step-info">
                    <span class="step-text">Step 1 of 6</span>
                </div>
				
                <button
                    class="nav-button next"
                    class:disabled={!canProceed}
                    on:click={handleNext}
                    disabled={!canProceed}
                >
                    <span class="button-text">Next</span>
                    <div class="button-icon">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7.5 15L12.5 10L7.5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </button>
            </div>
			
            {#if !canProceed}
                <div class="progress-hint" transition:fade={{ duration: 300 }}>
                    <p class="hint-text">Select a core dynamic to continue</p>
                </div>
            {/if}
        </div>
    </div>
</div>

