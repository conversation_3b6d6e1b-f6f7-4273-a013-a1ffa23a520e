<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { fade } from 'svelte/transition';
    import {
        beliefsExplorerState,
        beliefsExplorerActions
    } from '$lib/stores/beliefsExplorer';
    import type { BeliefExplorerData } from '$lib/types/beliefs';
	
    import ProgressIndicator from '$lib/components/Wizard/ProgressIndicator.svelte';
    import AlignmentReview from '$lib/components/Wizard/AlignmentReview.svelte';
	
    export let data: { beliefsExplorerData: BeliefExplorerData };
	
    $: canProceed = $beliefsExplorerState.goals.every(goal =>
        $beliefsExplorerState.beliefAlignments.filter(a => a.goalId === goal.id).length === $beliefsExplorerState.selectedBeliefs.length
    );
	
    function handleSetAlignment(event: CustomEvent<{ beliefId: string; goalId: string; supports: boolean }>) {
        beliefsExplorerActions.setBeliefAlignment(event.detail.beliefId, event.detail.goalId, event.detail.supports);
    }
	
    function handleComplete() {
        if (canProceed) {
            beliefsExplorerActions.completeSession();
            // Navigate to a completion page or back to intro
            goto('/beliefs-explore/intro');
        }
    }
	
	
    onMount(() => {
        // Ensure we're on the correct step
        beliefsExplorerActions.goToStep(5);
		
        // Initialize data if not already loaded
        if (!$beliefsExplorerState.sessionId) {
            beliefsExplorerActions.loadData(data.beliefsExplorerData);
            beliefsExplorerActions.startNewSession();
        }
    });
</script>

<svelte:head>
    <title>Step 6: Review Alignment - Beliefs Explorer</title>
</svelte:head>

<div class="relative bg-background px-6 py-8">
    <div class="relative z-10 max-w-2xl mx-auto flex flex-col">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-5xl md:text-6xl font-bold text-foreground mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Beliefs Explorer
            </h1>
            <p class="text-lg text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                Step 6: Review Alignment – Goals vs Beliefs
            </p>
        </div>
		
        <!-- Progress Indicator -->
        <ProgressIndicator
            current={6}
            total={6}
            showLabels={true}
        />
		
        <!-- Step Content -->
        <div class="flex-1 flex flex-col" transition:fade={{ duration: 300 }}>
            <div class="flex flex-col gap-8">
                <div class="w-full">
                    <AlignmentReview
                        goals={$beliefsExplorerState.goals}
                        selectedBeliefs={$beliefsExplorerState.selectedBeliefs}
                        beliefAlignments={$beliefsExplorerState.beliefAlignments}
                        on:setAlignment={handleSetAlignment}
                    />
                </div>
            </div>
        </div>
		
        <!-- Navigation -->
        <div class="wizard-navigation">
            <div class="nav-container">
                <div class="step-info">
                    <span class="step-text">Step 6 of 6</span>
                </div>
				
                <button
                    class="nav-button complete"
                    class:disabled={!canProceed}
                    on:click={handleComplete}
                    disabled={!canProceed}
                >
                    <span class="button-text">Complete</span>
                    <div class="button-icon">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16.6667 5L7.50004 14.1667L3.33337 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </button>
            </div>
			
            {#if !canProceed}
                <div class="progress-hint" transition:fade={{ duration: 300 }}>
                    <p class="hint-text">Evaluate all beliefs against your goals</p>
                </div>
            {/if}
        </div>
    </div>
</div>

