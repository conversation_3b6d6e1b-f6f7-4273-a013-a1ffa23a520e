<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { fade } from 'svelte/transition';
    import {
        beliefsExplorerState,
        beliefsExplorerActions
    } from '$lib/stores/beliefsExplorer';
    import type { BeliefExplorerData } from '$lib/types/beliefs';
	
    import ProgressIndicator from '$lib/components/Wizard/ProgressIndicator.svelte';
    import WhyExploration from '$lib/components/Wizard/WhyExploration.svelte';
	
    export let data: { beliefsExplorerData: BeliefExplorerData };
	
    $: canProceed = $beliefsExplorerState.goals.every(goal => goal.whyChain.length >= 3);
	
    function handleAddWhy(event: CustomEvent<{ goalId: string; reason: string }>) {
        beliefsExplorerActions.addWhyReason(event.detail.goalId, event.detail.reason);
    }
	
    function handleNext() {
        if (canProceed) {
            beliefsExplorerActions.nextStep();
            goto('/beliefs-explore/step-6');
        }
    }
	
	
    onMount(() => {
        // Ensure we're on the correct step
        beliefsExplorerActions.goToStep(4);
		
        // Initialize data if not already loaded
        if (!$beliefsExplorerState.sessionId) {
            beliefsExplorerActions.loadData(data.beliefsExplorerData);
            beliefsExplorerActions.startNewSession();
        }
    });
</script>

<svelte:head>
    <title>Step 5: Discover Why - Beliefs Explorer</title>
</svelte:head>

<div class="relative bg-background px-6 py-8">
    <div class="relative z-10 max-w-2xl mx-auto flex flex-col">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-5xl md:text-6xl font-bold text-foreground mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Beliefs Explorer
            </h1>
            <p class="text-lg text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                Step 5: Discover the "Why" Behind Your Goals
            </p>
        </div>
		
        <!-- Progress Indicator -->
        <ProgressIndicator
            current={5}
            total={6}
            showLabels={true}
        />
		
        <!-- Step Content -->
        <div class="flex-1 flex flex-col" transition:fade={{ duration: 300 }}>
            <div class="flex flex-col gap-8">
                <div class="w-full">
                    <WhyExploration
                        goals={$beliefsExplorerState.goals}
                        on:addWhy={handleAddWhy}
                    />
                </div>
            </div>
        </div>
		
        <!-- Navigation -->
        <div class="w-full max-w-5xl mx-auto mt-12 flex flex-col gap-4">
            <div class="flex items-center justify-between gap-4 sm:flex-col sm:gap-6">
                <div class="flex-1 text-center sm:order-first">
                    <span class="step-text">Step 5 of 6</span>
                </div>
				
                <button
                    class="group flex items-center gap-3 px-6 py-3 rounded-2xl font-medium transition-all duration-300 bg-primary text-primary-foreground border-none outline-none hover:bg-primary-600 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed sm:w-full sm:justify-center"
                    on:click={handleNext}
                    disabled={!canProceed}
                >
                    <span class="button-text">Next</span>
                    <svg
                        class="w-5 h-5 transition-transform duration-200 group-hover:translate-x-1"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path d="M7.5 15L12.5 10L7.5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
			
            {#if !canProceed}
                <div class="text-center p-4 bg-muted/30 border border-border rounded sm:mx-auto" transition:fade={{ duration: 300 }}>
                    <p class="text-sm text-muted-foreground m-0">Complete the why exploration for your goals</p>
                </div>
            {/if}
        </div>
    </div>
</div>

