<script lang="ts">
    import { goto } from '$app/navigation';

    function showDetails() {
        goto('/beliefs-explore/intro-2');
    }
</script>

<svelte:head>
    <title>Beliefs Explorer - Your Best Days</title>
</svelte:head>

<div class="min-h-screen flex flex-col px-6 py-8">
    <!-- First Screen: Main Introduction -->
    <div class="flex-1 flex flex-col">
        <div class="text-center max-w-lg mx-auto pt-12">
            <h1 class="text-3xl md:text-4xl font-bold text-foreground mb-6 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Beliefs Explorer
            </h1>
            <p class="text-lg text-muted-foreground leading-relaxed mb-12">
                A guided 6-step journey to explore your beliefs and reconnect with what matters most to you.
            </p>
			
            <button
                class="w-full px-8 py-4 bg-primary text-primary-foreground border border-primary rounded-lg text-lg font-medium cursor-pointer transition-all duration-200 hover:bg-primary/90 mb-4"
                on:click={showDetails}
            >
                Next
            </button>
        </div>
    </div>
</div>