<script lang="ts">
    import { fade } from 'svelte/transition';
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import {
        beliefsExplorerState,
        beliefsExplorerActions,
        currentStep,
        progress
    } from '$lib/stores/beliefsExplorer';
    import type { BeliefExplorerData } from '$lib/types/beliefs';
	
    // Import working wizard components
    import ProgressIndicator from '$lib/components/Wizard/ProgressIndicator.svelte';
    import CoreDynamicSelector from '$lib/components/Wizard/CoreDynamicSelector.svelte';
    import BeliefSelector from '$lib/components/Wizard/BeliefSelector.svelte';
    import WizardNavigation from '$lib/components/Wizard/WizardNavigation.svelte';
	
    // Declare data prop from load function
    export let data: { beliefsExplorerData: BeliefExplorerData };
	
    // Reactive statements for navigation logic
    $: canProceed = getCanProceed($beliefsExplorerState, $currentStep);
    $: isComplete = $beliefsExplorerState.currentStepIndex === 5 && canProceed;
	
    function getCanProceed(state: typeof $beliefsExplorerState, step: typeof $currentStep): boolean {
        if (!step) return false;
		
        switch (step.index) {
            case 0: // Core Dynamic Selection
                return state.selectedDynamic !== null;
            case 1: // Belief Selection
                return state.selectedBeliefs.length > 0;
            case 2: // Theme Selection
                return state.selectedTheme !== null;
            case 3: // Goal Input
                return state.goals.length > 0;
            case 4: // Why Exploration
                return state.goals.every(goal => goal.whyChain.length >= 3);
            case 5: // Alignment Review
                return state.goals.every(goal =>
                    state.beliefAlignments.filter(a => a.goalId === goal.id).length === state.selectedBeliefs.length
                );
            default:
                return false;
        }
    }
	
    // Event handlers
    function handleNext() {
        const currentIndex = $beliefsExplorerState.currentStepIndex;
        beliefsExplorerActions.nextStep();
        // Navigate to the appropriate step route
        goto(`/beliefs-explore/step-${currentIndex + 2}`);
    }
	
    function handleBack() {
        const currentIndex = $beliefsExplorerState.currentStepIndex;
        beliefsExplorerActions.previousStep();
        // Navigate to the appropriate step route or intro
        if (currentIndex === 0) {
            goto('/beliefs-explore/intro-2');
        } else {
            goto(`/beliefs-explore/step-${currentIndex}`);
        }
    }
	
    function handleComplete() {
        beliefsExplorerActions.completeSession();
        console.log('Session completed!', $beliefsExplorerState);
    }
	
    // Step-specific event handlers
    function handleDynamicSelect(event: CustomEvent<{ dynamic: string }>) {
        beliefsExplorerActions.selectDynamic(event.detail.dynamic);
    }
	
    function handleBeliefToggle(event: CustomEvent<{ belief: string }>) {
        beliefsExplorerActions.toggleBelief(event.detail.belief);
    }
	
    function handleBeliefAdd(event: CustomEvent<{ belief: string }>) {
        beliefsExplorerActions.toggleBelief(event.detail.belief);
    }
	
    // Initialize store data and scroll-to-top logic
    onMount(() => {
        // Initialize store with data from load function
        beliefsExplorerActions.loadData(data.beliefsExplorerData);
        beliefsExplorerActions.startNewSession();
        beliefsExplorerActions.loadSessionHistory();
		
        // Aggressive scroll-to-top logic
        const forceScrollToTop = () => {
            window.scrollTo(0, 0);
            document.documentElement.scrollTop = 0;
            document.body.scrollTop = 0;
            console.log('Scroll position after force:', window.scrollY, document.documentElement.scrollTop, document.body.scrollTop);
        };
		
        // Initial scroll
        forceScrollToTop();
		
        // Multiple delayed scrolls to ensure it fires after rendering and layout shifts
        setTimeout(forceScrollToTop, 0);
        setTimeout(forceScrollToTop, 50);
        setTimeout(forceScrollToTop, 100);
        setTimeout(forceScrollToTop, 250);
        setTimeout(forceScrollToTop, 500);
		
        // Log current scroll position after a short delay
        setTimeout(() => {
            console.log('Final scroll position check:', window.scrollY, document.documentElement.scrollTop, document.body.scrollTop);
        }, 1000);
    });
</script>

<svelte:head>
    <title>Beliefs Explorer - Your Best Days</title>
</svelte:head>

<div class="relative bg-background px-6 py-8">
    <!-- Main Wizard Interface -->
    <div class="relative z-10 max-w-6xl mx-auto flex flex-col">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-5xl md:text-6xl font-bold text-foreground mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Beliefs Explorer
            </h1>
            <p class="text-lg text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                A guided journey to explore your beliefs and reconnect with what matters most
            </p>
        </div>
		
        <!-- Progress Indicator -->
        <ProgressIndicator
            current={$progress.current}
            total={$progress.total}
            showLabels={true}
        />
		
        <!-- Step Content -->
        {#if $currentStep}
            {#key $currentStep.index}
                <div class="flex-1 flex flex-col">
                    <div class="flex flex-col gap-8" transition:fade={{ duration: 300 }}>
                        <!-- Step Component -->
                        <div class="w-full">
                            {#if $currentStep.index === 0}
                                <CoreDynamicSelector
                                    coreDynamics={data.beliefsExplorerData.coreDynamics}
                                    selectedDynamic={$beliefsExplorerState.selectedDynamic}
                                    on:select={handleDynamicSelect}
                                />
                            {:else if $currentStep.index === 1}
                                <BeliefSelector
                                    beliefs={data.beliefsExplorerData.beliefs}
                                    selectedBeliefs={$beliefsExplorerState.selectedBeliefs}
                                    on:toggle={handleBeliefToggle}
                                    on:addCustom={handleBeliefAdd}
                                />
                            {:else}
                                <!-- Placeholder for other steps -->
                                <div class="text-center p-12 bg-muted border border-border rounded-xl">
                                    <p class="text-2xl font-semibold text-foreground mb-4">
                                        Step {$currentStep.index + 1}: {$currentStep.name}
                                    </p>
                                    <p class="text-muted-foreground">
                                        This step is under development. The core wizard structure is working!
                                    </p>
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
            {/key}
        {/if}
		
        <!-- Navigation -->
        <WizardNavigation
            currentStep={$beliefsExplorerState.currentStepIndex}
            totalSteps={6}
            {canProceed}
            {isComplete}
            on:next={handleNext}
            on:back={handleBack}
            on:complete={handleComplete}
        />
    </div>
	
    <!-- Ambient Background Animation -->
    <div class="ambient-background">
        <div class="floating-particles">
            {#each Array(8).fill(0).map((_, i) => i) as i (i)}
                <div
                    class="particle"
                    style="
                        --delay: {i * 0.5}s;
                            --duration: {8 + (i % 4) * 2}s;
                            --size: {4 + (i % 3) * 2}px;
                            --x: {10 + (i * 7) % 80}%;
                            --y: {20 + (i * 11) % 60}%;
                    "
                ></div>
            {/each}
        </div>
    </div>
</div>

