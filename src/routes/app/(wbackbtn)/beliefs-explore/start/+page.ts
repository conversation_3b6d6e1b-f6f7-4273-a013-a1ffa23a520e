import type { PageLoad } from './$types';
import type { BeliefExplorerData } from '$lib/types/beliefs';

export const load: PageLoad = async ({ fetch }) => {
    const response = await fetch('/beliefs_explorer_data.json');
    const fetchedData: BeliefExplorerData = await response.json();
	
    // Return data to the component - store actions will be called in onMount
    return {
        beliefsExplorerData: fetchedData
    };
};