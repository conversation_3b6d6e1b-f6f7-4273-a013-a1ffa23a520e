<script lang="ts">
    import { goto } from '$app/navigation';
    import { beliefsExplorerActions } from '$lib/stores/beliefsExplorer';

    function startJourney() {
        beliefsExplorerActions.reset(); // Reset state when starting a new journey
        goto('/beliefs-explore/step-1');
    }

</script>

<svelte:head>
    <title>How It Works - Beliefs Explorer</title>
</svelte:head>

<div class="min-h-screen flex flex-col px-6 py-8">
    <!-- Second Screen: How It Works -->
    <div class="flex-1">
        <div class="w-full max-w-none mx-auto">
            <div class="text-center mb-6">
                <h1 class="text-2xl md:text-3xl font-bold text-foreground mb-2">How It Works</h1>
                <p class="text-sm text-muted-foreground">
                    This focused wizard helps you identify misalignments and find clarity through structured reflection.
                </p>
            </div>
			
            <!-- How It Works Content - Almost Full Width -->
            <div class="bg-background border border-border rounded-lg p-4 mb-6 text-left">
                <div class="space-y-4 text-muted-foreground">
                    <p class="text-sm">This guided wizard takes you through 6 focused steps:</p>
                    <ol class="list-decimal list-inside space-y-3 text-sm">
                        <li><strong class="text-foreground">Notice Discomfort & Identify Core Dynamic:</strong> Recognize what's bothering you and identify the main issue</li>
                        <li><strong class="text-foreground">Identify Beliefs at Play:</strong> Explore the thoughts and beliefs that are coming up for you</li>
                        <li><strong class="text-foreground">Choose Your Theme:</strong> Set an intention or mood for your session</li>
                        <li><strong class="text-foreground">Define Your Goals:</strong> Clarify what you want to achieve</li>
                        <li><strong class="text-foreground">Discover the "Why":</strong> Uncover the deeper motivations behind your goals</li>
                        <li><strong class="text-foreground">Review Alignment:</strong> See how your beliefs support or hinder your goals</li>
                    </ol>
                    <p class="text-sm mt-4">Each step is designed to keep you focused on one idea at a time, with a clean, minimalistic interface that guides you through the process.</p>
                </div>
            </div>

            <button
                class="w-full px-8 py-4 bg-primary text-primary-foreground border border-primary rounded-lg text-lg font-medium cursor-pointer transition-all duration-200 hover:bg-primary/90"
                on:click={startJourney}
            >
                Begin Your Journey
            </button>
        </div>
    </div>
</div>