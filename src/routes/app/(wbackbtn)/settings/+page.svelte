<script lang="ts">
    import { goto } from '$app/navigation';
    import DarkModeToggle from '$lib/components/DarkModeToggle.svelte';
    import ThemeSwitcher from '$lib/components/ThemeSwitcher.svelte';
    import Button from '$lib/components/Button.svelte';

    function goToExperienceWizard() {
        goto('/app/wizard');
    }
</script>

<div class="flex flex-col space-y-6">
    <h1 class="text-2xl font-bold text-foreground">Settings</h1>
	
    <!-- Appearance Section -->
    <div class="space-y-4">
        <h2 class="text-lg font-semibold text-foreground border-b border-border pb-2">Appearance</h2>
		
        <div class="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
            <span class="font-medium text-foreground">Mode</span>
            <DarkModeToggle />
        </div>
		
        <div class="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
            <span class="font-medium text-foreground">Theme</span>
            <ThemeSwitcher />
        </div>
    </div>
	
    <!-- Additional Settings Sections (for future expansion) -->
    <div class="space-y-4">
        <h2 class="text-lg font-semibold text-foreground border-b border-border pb-2">Preferences</h2>
		
        <div class="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
            <div class="flex flex-col">
                <span class="font-medium text-foreground">Experience Wizard</span>
                <span class="text-sm text-muted-foreground">Set up your personalized experience</span>
            </div>
            <Button variant="outline" on:click={goToExperienceWizard}>
                Open Wizard
            </Button>
        </div>
    </div>
</div>