<script lang="ts">
    import { page } from '$app/stores';
    import { goto } from '$app/navigation';
    import StepIndicator from '$lib/components/Wizard/StepIndicator.svelte';
    import Button from '$lib/components/Button.svelte';
    import { profile } from '$lib/stores/profile';

    const steps = [
        { name: 'Intro', path: '/app/wizard', number: 1 },
        { name: 'Personal', path: '/app/wizard/personal', number: 2 },
        { name: 'Theme', path: '/app/wizard/theme', number: 3 },
        { name: 'Experience', path: '/app/wizard/mwe', number: 4 },
        { name: 'Obstacles', path: '/app/wizard/obstacles', number: 5 },
        { name: 'Schedule', path: '/app/wizard/schedule', number: 6 },
        { name: 'Confirmation', path: '/app/wizard/confirmation', number: 7 },
    ];

    let currentStepIndex = 0;

    $: {
        let foundIndex = steps.findIndex(step => $page.url.pathname === step.path);
        // If the current path is '/wizard', it should be index 0.
        // If it's a sub-path like '/wizard/personal', it should find the exact match.
        // If no exact match is found, it means we are on the intro page, so set to 0.
        if (foundIndex === -1 && $page.url.pathname === '/app/wizard') {
            foundIndex = 0;
        }
        currentStepIndex = foundIndex;
    }

    $: canGoBack = currentStepIndex > 0;
    $: canGoNext = currentStepIndex < steps.length - 1;
    $: isLastStep = currentStepIndex === steps.length - 1;
  
    // Check if we're on the Personal page and require all personal fields to be filled
    $: isPersonalPage = $page.url.pathname === '/app/wizard/personal';
    $: hasRequiredPersonalInfo = !isPersonalPage || (
        $profile.personal.preferredName.trim() !== '' &&
        $profile.personal.ageRange !== '' &&
        $profile.personal.genderIdentity !== ''
    );
  
    // Check if we're on the MWE page and require at least one experience selection
    $: isMWEPage = $page.url.pathname === '/app/wizard/mwe';
    $: hasRequiredSelection = !isMWEPage || $profile.experiences.length > 0;
  
    // Check if we're on the Obstacles page and require at least one obstacle selection
    $: isObstaclesPage = $page.url.pathname === '/app/wizard/obstacles';
    $: hasRequiredObstacles = !isObstaclesPage || $profile.obstacles.length > 0;
  
    // Check if we're on the Schedule page and require at least one saved schedule
    $: isSchedulePage = $page.url.pathname === '/app/wizard/schedule';
    $: hasRequiredSchedule = !isSchedulePage || $profile.schedules.length > 0;
  

    function goBack() {
        if (canGoBack) {
            const prevStep = steps[currentStepIndex - 1];
            goto(prevStep.path);
        }
    }

    function goNext() {
        if (canGoNext) {
            const nextStep = steps[currentStepIndex + 1];
            goto(nextStep.path);
        }
    }
</script>

{#if $page.url.pathname === '/app/wizard'}
    <slot />
{:else if $page.url.pathname === '/app/wizard/confirmation'}
    <div class="flex flex-col gap-5 p-5 border border-border rounded-lg max-w-xl mx-auto my-5">
        <div class="min-h-[150px]">
            <slot />
        </div>
    </div>
{:else}
    <div class="flex flex-col gap-5 p-5 border border-border rounded-lg max-w-xl mx-auto my-5">
        <StepIndicator {steps} {currentStepIndex} />

        <div class="min-h-[150px]">
            <slot />
        </div>

        <!-- Simple navigation buttons -->
        <div class="flex justify-between items-center mt-6">
            {#if canGoBack}
                <Button on:click={goBack} variant="outline">
                    Back
                </Button>
            {:else}
                <div></div>
            {/if}

            {#if canGoNext}
                <Button
                    on:click={goNext}
                    variant="default"
                    disabled={!hasRequiredPersonalInfo || !hasRequiredSelection || !hasRequiredObstacles || !hasRequiredSchedule}
                >
                    Next
                </Button>
            {:else if isLastStep}
                <Button variant="default">
                    Complete
                </Button>
            {:else}
                <div></div>
            {/if}
        </div>
    </div>
{/if}