<script lang="ts">
    import { profile } from '$lib/stores/profile';
  
    let errors: { [key: string]: string } = {};
    let touched: { [key: string]: boolean } = {};
  
    // Validation functions
    function validatePreferredName(value: string): string {
        if (!value || value.trim() === '') {
            return 'Preferred name is required';
        }
        if (value.length > 30) {
            return 'Preferred name must be 30 characters or less';
        }
        if (!/^[a-zA-Z\s]+$/.test(value)) {
            return 'Preferred name can only contain letters and spaces';
        }
        return '';
    }
  
    function validateAgeRange(value: string): string {
        if (!value || value === '') {
            return 'Age range is required';
        }
        return '';
    }
  
    function validateGenderIdentity(value: string): string {
        if (!value || value === '') {
            return 'Gender identity is required';
        }
        return '';
    }
  
    // Reactive validation
    $: {
        if (touched.preferredName) {
            errors.preferredName = validatePreferredName($profile.personal.preferredName);
        }
        if (touched.ageRange) {
            errors.ageRange = validateAgeRange($profile.personal.ageRange);
        }
        if (touched.genderIdentity) {
            errors.genderIdentity = validateGenderIdentity($profile.personal.genderIdentity);
        }
    }
  
    // Check if all fields are valid
    $: isFormValid =
        $profile.personal.preferredName.trim() !== '' &&
        $profile.personal.ageRange !== '' &&
        $profile.personal.genderIdentity !== '';
  
    function handleBlur(field: string) {
        touched[field] = true;
    }
  
    function getInputClass(fieldName: string, baseClass: string): string {
        const hasError = touched[fieldName] && errors[fieldName];
        const isValid = touched[fieldName] && !errors[fieldName] && $profile.personal[fieldName as keyof typeof $profile.personal];
    
        if (hasError) {
            return `${baseClass} border-red-500 focus:ring-red-500`;
        } else if (isValid) {
            return `${baseClass} border-green-500 focus:ring-green-500`;
        }
        return baseClass;
    }
</script>

<h2 class="text-2xl font-bold mb-4">Tell us about yourself</h2>
<form class="flex flex-col gap-4">
    <div class="flex flex-col">
        <label for="preferredName" class="mb-1 font-medium">
            Preferred Name: <span class="text-red-500">*</span>
        </label>
        <input
            type="text"
            id="preferredName"
            bind:value={$profile.personal.preferredName}
            on:blur={() => handleBlur('preferredName')}
            class={getInputClass('preferredName', 'p-2 border border-border rounded-md bg-input text-foreground focus:outline-none focus:ring-2 focus:ring-primary')}
            required
        />
        {#if touched.preferredName && errors.preferredName}
            <span class="text-red-500 text-sm mt-1">{errors.preferredName}</span>
        {/if}
    </div>
  
    <div class="flex flex-col">
        <label for="ageRange" class="mb-1 font-medium">
            Age Range: <span class="text-red-500">*</span>
        </label>
        <select
            id="ageRange"
            bind:value={$profile.personal.ageRange}
            on:blur={() => handleBlur('ageRange')}
            class={getInputClass('ageRange', 'p-2 border border-border rounded-md bg-input text-foreground focus:outline-none focus:ring-2 focus:ring-primary')}
            required
        >
            <option value="">Select your age range</option>
            <option value="18-24">18-24</option>
            <option value="25-34">25-34</option>
            <option value="35-44">35-44</option>
            <option value="45-54">45-54</option>
            <option value="55-64">55-64</option>
            <option value="65-74">65-74</option>
            <option value="75+">75+</option>
        </select>
        {#if touched.ageRange && errors.ageRange}
            <span class="text-red-500 text-sm mt-1">{errors.ageRange}</span>
        {/if}
    </div>
  
    <div class="flex flex-col">
        <label for="genderIdentity" class="mb-1 font-medium">
            Gender Identity: <span class="text-red-500">*</span>
        </label>
        <select
            id="genderIdentity"
            bind:value={$profile.personal.genderIdentity}
            on:blur={() => handleBlur('genderIdentity')}
            class={getInputClass('genderIdentity', 'p-2 border border-border rounded-md bg-input text-foreground focus:outline-none focus:ring-2 focus:ring-primary')}
            required
        >
            <option value="">Select your gender identity</option>
            <option value="Woman">Woman</option>
            <option value="Man">Man</option>
            <option value="Non-binary">Non-binary</option>
            <option value="Prefer not to say">Prefer not to say</option>
        </select>
        {#if touched.genderIdentity && errors.genderIdentity}
            <span class="text-red-500 text-sm mt-1">{errors.genderIdentity}</span>
        {/if}
    </div>
  
    <!-- Form validation summary -->
    {#if Object.keys(touched).length > 0 && !isFormValid}
        <div class="bg-red-50 border border-red-200 rounded-md p-3 mt-2">
            <p class="text-red-700 text-sm font-medium">Please update all required fields to continue.</p>
        </div>
    {/if}
</form>