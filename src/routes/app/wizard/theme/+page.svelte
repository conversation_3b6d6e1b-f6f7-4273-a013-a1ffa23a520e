<script lang="ts">
    import ThemeSwitcher from '$lib/components/ThemeSwitcher.svelte';
    import DarkModeToggle from '$lib/components/DarkModeToggle.svelte';
</script>

<h2 class="text-2xl font-bold mb-1">Select a color theme</h2>

<p class="py-8 italic mb-4">Embrace messages in your preferred colors. Select a color theme that matches how you want to approach each moment.</p>

<form class="grid grid-cols-[80px_1fr] gap-10">
    <div class="text-lg font-semibold">Color:</div>
    <ThemeSwitcher />

    <div class="text-lg font-semibold">Mode:</div>
    <div class="ma max-w-[50px]">
        <DarkModeToggle />
    </div>
</form>

