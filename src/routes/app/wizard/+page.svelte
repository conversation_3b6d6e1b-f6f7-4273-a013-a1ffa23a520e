<script lang="ts">
    import { goto } from '$app/navigation';
    import Button from '$lib/components/Button.svelte';

    let showIntro = true; // New state for intro page

    function startWizard() {
        showIntro = false;
        goto('/app/wizard/personal');
    }

    function exitWizard() {
        goto('/app'); // Navigate to home page
    }
</script>

{#if showIntro}
    <div class="flex flex-col gap-5 p-5 rounded-lg max-w-xl mx-auto my-5">
        <div class="flex flex-col items-center text-center">
            <h1 class="text-4xl font-bold mb-2 text-foreground">Your Best Days</h1>
            <p class="text-lg text-muted-foreground mb-2">Experience Profile Wizard</p>
            <div class="bg-background rounded-lg p-4 shadow-md mb-8 max-w-md">
                <p class="text-foreground leading-relaxed">
                    This wizard will guide you through updating your focus profile for living life intentionally. We'll use this
                    to help you practice thought re-framing to reclaim your power and move confidently
                    through life's challenges.
                </p>
            </div>
            <Button on:click={startWizard} variant="default" size="lg" class="w-full max-w-xs text-xl">
                Next
            </Button>
        </div>
    </div>
{:else}
    <slot />
{/if}

<div class="fixed bottom-0 left-0 right-0 p-4 bg-background border-t border-border z-50">
    <Button on:click={exitWizard} variant="outline" size="lg" class="w-full">
        Exit Wizard
    </Button>
</div>