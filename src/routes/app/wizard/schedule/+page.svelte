<script lang="ts">
    import { Check, X } from 'lucide-svelte';
    import Button from '$lib/components/Button.svelte';
    import { onMount } from 'svelte';
    import { profile, type TimeRange, type Schedule } from '$lib/stores/profile';

    const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    let selectedDays: string[] = [];
  
    // Calculate days that are already used in other schedules
    $: usedDays = $profile.schedules
        .filter(schedule => schedule.id !== currentScheduleId) // Exclude current schedule
        .flatMap(schedule => schedule.selectedDays);
    let currentScheduleId: number | null = null;
    let nextScheduleId = 0;
    let timeRanges: TimeRange[] = [];
    let isLoading = false; // Flag to prevent auto-save during loading operations
  
    // Reactive statement to keep time ranges sorted by start time
    $: sortedTimeRanges = [...timeRanges].sort((a, b) => a.startHour - b.startHour);
  
    // Auto-save when selectedDays or timeRanges change for an existing schedule
    $: if (currentScheduleId !== null && !isLoading && (selectedDays.length > 0 || timeRanges.length > 0)) {
        autoSaveCurrentSchedule();
    }
    let nextTimeRangeId = 0;
    let overlapError: string | null = null;
    let showTimeDialog = false;
    let showMaxPerHourDialog = false;
    let editingTimeRangeId: number | null = null;
    let editingMaxPerHourId: number | null = null;
    let dialogStartHour = 9;
    let dialogEndHour = 10;
    let dialogMaxPerHour = 2;

    function toggleDay(day: string) {
        // Don't allow toggling days that are used in other schedules
        if (usedDays.includes(day)) {
            return;
        }
    
        if (selectedDays.includes(day)) {
            selectedDays = selectedDays.filter(d => d !== day);
        } else {
            selectedDays = [...selectedDays, day];
        }
    }

    function checkTimeRangeOverlaps(ranges: TimeRange[]): boolean {
        overlapError = null;
        if (ranges.length < 2) return false;

        const sortedRanges = [...ranges].sort((a, b) => a.startHour - b.startHour);

        for (let i = 0; i < sortedRanges.length; i++) {
            const current = sortedRanges[i];
      
            for (let j = i + 1; j < sortedRanges.length; j++) {
                const next = sortedRanges[j];
        
                // Check for overlap (both ranges are same-day only now)
                if (current.endHour > next.startHour && current.startHour < next.endHour) {
                    overlapError = `Time ranges overlap: ${formatHour(current.startHour)} to ${formatHour(current.endHour)} and ${formatHour(next.startHour)} to ${formatHour(next.endHour)}`;
                    return true;
                }
            }
        }
        return false;
    }

    function formatHour(hour: number): string {
        if (hour === 0) return '12AM';
        if (hour < 12) return `${hour}AM`;
        if (hour === 12) return '12PM';
        return `${hour - 12}PM`;
    }

    function openTimeDialog(rangeId: number | null = null) {
        editingTimeRangeId = rangeId;
        overlapError = null; // Clear any previous errors
        if (rangeId !== null) {
            const range = timeRanges.find(r => r.id === rangeId);
            if (range) {
                dialogStartHour = range.startHour;
                dialogEndHour = range.endHour;
            }
        } else {
            dialogStartHour = 9;
            dialogEndHour = 10;
        }
        showTimeDialog = true;
    }

    function closeTimeDialog() {
        showTimeDialog = false;
        editingTimeRangeId = null;
    }

    function saveTimeRange() {
        // Validate that end hour is after start hour (same day only)
        if (dialogEndHour <= dialogStartHour) {
            overlapError = "End time must be after start time and on the same day";
            return;
        }

        if (editingTimeRangeId !== null) {
            // Editing existing range
            const updatedRanges = timeRanges.map(range =>
                range.id === editingTimeRangeId
                    ? { ...range, startHour: dialogStartHour, endHour: dialogEndHour }
                    : range
            );
            if (!checkTimeRangeOverlaps(updatedRanges)) {
                timeRanges = updatedRanges;
                closeTimeDialog();
            }
        } else {
            // Adding new range
            const newRange: TimeRange = {
                id: nextTimeRangeId++,
                startHour: dialogStartHour,
                endHour: dialogEndHour,
                maxPerHour: 2
            };
            const updatedRanges = [...timeRanges, newRange];
            if (!checkTimeRangeOverlaps(updatedRanges)) {
                timeRanges = updatedRanges;
                closeTimeDialog();
            }
        }
    }

    function openMaxPerHourDialog(rangeId: number) {
        editingMaxPerHourId = rangeId;
        const range = timeRanges.find(r => r.id === rangeId);
        if (range) {
            dialogMaxPerHour = range.maxPerHour;
        }
        showMaxPerHourDialog = true;
    }

    function closeMaxPerHourDialog() {
        showMaxPerHourDialog = false;
        editingMaxPerHourId = null;
    }

    function saveMaxPerHour() {
        if (editingMaxPerHourId !== null) {
            timeRanges = timeRanges.map(range =>
                range.id === editingMaxPerHourId
                    ? { ...range, maxPerHour: dialogMaxPerHour }
                    : range
            );
            closeMaxPerHourDialog();
        }
    }

    function selectMaxPerHour(value: number) {
        dialogMaxPerHour = value;
    }

    function addTimeRange() {
        openTimeDialog();
    }

    function removeTimeRange(id: number) {
        timeRanges = timeRanges.filter(range => range.id !== id);
        checkTimeRangeOverlaps(timeRanges); // Re-check after removal
    }



    function autoSaveCurrentSchedule() {
        // Debounced auto-save to avoid excessive updates
        if (currentScheduleId !== null) {
            profile.update(p => {
                p.schedules = p.schedules.map(schedule =>
                    schedule.id === currentScheduleId
                        ? { ...schedule, selectedDays: [...selectedDays], timeRanges: [...timeRanges] }
                        : schedule
                );
                return p;
            });
        }
    }

    function saveCurrentSchedule() {
        if (currentScheduleId !== null) {
            // Update existing schedule
            profile.update(p => {
                p.schedules = p.schedules.map(schedule =>
                    schedule.id === currentScheduleId
                        ? { ...schedule, selectedDays: [...selectedDays], timeRanges: [...timeRanges] }
                        : schedule
                );
                return p;
            });
        } else if (selectedDays.length > 0 || timeRanges.length > 0) {
            // Create new schedule if there's data
            const newSchedule: Schedule = {
                id: nextScheduleId++,
                name: `Schedule`,
                selectedDays: [...selectedDays],
                timeRanges: [...timeRanges]
            };
            profile.update(p => {
                p.schedules = [...p.schedules, newSchedule];
                return p;
            });
            currentScheduleId = newSchedule.id;
        }
    }

    function loadSchedule(scheduleId: number) {
        saveCurrentSchedule(); // Save current before switching
    
        const schedule = $profile.schedules.find(s => s.id === scheduleId);
        if (schedule) {
            isLoading = true; // Prevent auto-save during loading
            currentScheduleId = scheduleId;
            selectedDays = [...schedule.selectedDays];
            timeRanges = [...schedule.timeRanges];
            // Update nextTimeRangeId to avoid conflicts
            nextTimeRangeId = Math.max(...timeRanges.map(r => r.id), 0) + 1;
            isLoading = false; // Re-enable auto-save
        }
    }

    function saveSchedule() {
        saveCurrentSchedule();
    }

    function addAnotherSchedule() {
        // Clear screen for new schedule (don't auto-save)
        isLoading = true; // Prevent auto-save during clearing
        currentScheduleId = null;
        selectedDays = [];
        timeRanges = [];
        nextTimeRangeId = 0;
        isLoading = false; // Re-enable auto-save
    }

    function deleteSchedule(scheduleId: number) {
        profile.update(p => {
            p.schedules = p.schedules.filter(s => s.id !== scheduleId);
            return p;
        });
        if (currentScheduleId === scheduleId) {
            // If we deleted the current schedule, clear the form
            isLoading = true; // Prevent auto-save during clearing
            currentScheduleId = null;
            selectedDays = [];
            timeRanges = [];
            nextTimeRangeId = 0;
            isLoading = false; // Re-enable auto-save
        }
    }


    onMount(() => {
        // Initialize nextScheduleId based on existing schedules
        if ($profile.schedules.length > 0) {
            nextScheduleId = Math.max(...$profile.schedules.map(s => s.id)) + 1;
        }
    // Start with empty time ranges - user will add them via dialog
    });
</script>

<h1 class="text-2xl font-bold mb-2 text-center text-foreground">Notification Schedule</h1>
<p class="text-center mb-4 text-muted-foreground text-sm">
    Select 1 or more days to share the same schedule. If multiple days will have the same time range, select them all here.
</p>

<!-- Days Picker -->
<div class="mb-3 border border-border p-3 rounded-md">
    <h2 class="text-lg font-semibold mb-2 text-foreground">Days</h2>
    <div class="grid grid-cols-7 gap-1 text-center">
        {#each daysOfWeek as day (day)}
            {@const isSelected = selectedDays.includes(day)}
            {@const isUsed = usedDays.includes(day)}
            <button
                class="relative flex items-center justify-center w-10 h-10 rounded-full border-2 text-xs
                    {isSelected ? 'bg-primary border-primary text-primary-foreground' : 'bg-background border-border text-foreground'}
                    {isUsed ? 'opacity-50 cursor-not-allowed' : ''}"
                on:click={() => !isUsed && toggleDay(day)}
                disabled={isUsed}
            >
                {day}
                {#if isSelected}
                    <div class="absolute top-0 right-0 -mt-1 -mr-1">
                        <Check size={16} class="text-primary-foreground bg-primary rounded-full" />
                    </div>
                {/if}
            </button>
        {/each}
    </div>
</div>

<!-- Time Range Section -->
<div class="mb-3 border border-border p-3 rounded-md">
    <h2 class="text-lg font-semibold mb-2 text-foreground">Reminder Time Ranges</h2>
    {#each sortedTimeRanges as range (range.id)}
        <div class="flex flex-wrap items-center mb-2 gap-2">
            <button
                class="flex-1 min-w-0 p-3 border border-border rounded-md text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-left cursor-pointer"
                on:click={() => openTimeDialog(range.id)}
            >
                <div class="flex items-center">
                    <svg class="mr-2 text-gray-500 dark:text-gray-400" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                    <span class="font-medium text-gray-900 dark:text-gray-100">{formatHour(range.startHour)} to {formatHour(range.endHour)}</span>
                </div>
            </button>
            <button
                class="flex items-center ml-2 p-2 border border-border rounded-md text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 cursor-pointer"
                on:click={() => openMaxPerHourDialog(range.id)}
            >
                <span class="mr-1 text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">Max/Hr:</span>
                <span class="font-medium text-gray-900 dark:text-gray-100">{range.maxPerHour}</span>
            </button>
            <button on:click={() => removeTimeRange(range.id)} class="text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 p-1 rounded-full">
                <X size={16} />
            </button>
        </div>
        
        <style lang="postcss">
          /* Target buttons with the 'no-hover' class */
          .no-hover {
            @apply hover:bg-primary !important; /* Override hover background */
            @apply hover:text-white !important; /* Override hover text color */
          }
        </style>
    {/each}
    <Button
        class="w-full py-2 px-4 no-hover"
        variant="outline"
        on:click={addTimeRange}
    >
        + Add Time Range
    </Button>
</div>

<!-- Time Selection Dialog -->
{#if showTimeDialog}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-background border border-border rounded-lg p-6 w-80 max-w-sm mx-4 shadow-lg">
            <h3 class="text-lg font-semibold mb-4 text-foreground">Select Time Range</h3>
          
            <div class="mb-4">
                <label for="start-hour-select" class="block text-sm font-medium mb-2 text-foreground">Start Hour</label>
                <select id="start-hour-select" bind:value={dialogStartHour} class="w-full p-2 border border-border rounded-md bg-background text-foreground">
                    {#each Array.from({length: 24}, (_, i) => i) as hour (hour)}
                        <option value={hour}>{formatHour(hour)}</option>
                    {/each}
                </select>
            </div>
          
            <div class="mb-6">
                <label for="end-hour-select" class="block text-sm font-medium mb-2 text-foreground">End Hour</label>
                <select id="end-hour-select" bind:value={dialogEndHour} class="w-full p-2 border border-border rounded-md bg-background text-foreground">
                    {#each Array.from({length: 24}, (_, i) => i) as hour (hour)}
                        <option value={hour}>{formatHour(hour)}</option>
                    {/each}
                </select>
            </div>
          
            {#if overlapError}
                <p class="text-red-500 text-sm mb-4">{overlapError}</p>
            {/if}
          
            <div class="flex gap-2">
                <Button
                    class="flex-1"
                    variant="outline"
                    on:click={closeTimeDialog}
                >
                    Cancel
                </Button>
                <Button
                    class="flex-1"
                    on:click={saveTimeRange}
                >
                    Done
                </Button>
            </div>
        </div>
    </div>
{/if}

<!-- Max Per Hour Selection Dialog -->
{#if showMaxPerHourDialog}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-background border border-border rounded-lg p-6 w-80 max-w-sm mx-4 shadow-lg">
            <h3 class="text-lg font-semibold mb-4 text-foreground">Max Notifications Per Hour</h3>
          
            <div class="grid grid-cols-5 gap-2 mb-6">
                {#each [1, 2, 3, 4, 5] as value (value)}
                    <button
                        class="w-12 h-12 rounded-lg border-2 font-semibold text-lg transition-colors
                            {dialogMaxPerHour === value
                                ? 'bg-primary border-primary text-primary-foreground'
                                : 'bg-background border-border text-foreground hover:border-primary hover:text-primary'}"
                        on:click={() => selectMaxPerHour(value)}
                    >
                        {value}
                    </button>
                {/each}
            </div>
          
            <div class="flex gap-2">
                <Button
                    class="flex-1"
                    variant="outline"
                    on:click={closeMaxPerHourDialog}
                >
                    Cancel
                </Button>
                <Button
                    class="flex-1"
                    on:click={saveMaxPerHour}
                >
                    Done
                </Button>
            </div>
        </div>
    </div>
{/if}

<!-- Save Schedule Button -->
<div class="mb-3">
    <Button
        class="w-full py-2 px-4 no-hover"
        on:click={saveSchedule}
        disabled={selectedDays.length === 0 || timeRanges.length === 0}
    >
        💾 Save Schedule
    </Button>
</div>

<!-- Saved Schedules -->
{#if $profile.schedules.length > 0}
    <div class="mb-3 border border-border p-3 rounded-md">
        <h2 class="text-lg font-semibold mb-2 text-foreground">Saved Schedules</h2>
        <div class="flex flex-wrap gap-2 mb-2">
            {#each $profile.schedules as schedule (schedule.id)}
                <div class="relative">
                    <button
                        class="flex flex-col items-center p-3 border-2 rounded-lg transition-colors min-w-[80px]
                            {currentScheduleId === schedule.id
                                ? 'border-primary bg-primary/10'
                                : 'border-border hover:border-primary/50'}"
                        on:click={() => loadSchedule(schedule.id)}
                    >
                        <div class="w-8 h-8 rounded-full bg-muted flex items-center justify-center mb-1">
                            <span class="text-sm font-semibold text-foreground">⏰</span>
                        </div>
                        <span class="text-xs text-center text-foreground">Schedule</span>
                        <div class="text-xs text-muted-foreground mt-1">
                            {schedule.selectedDays.length} days, {schedule.timeRanges.length} ranges
                        </div>
                    </button>
                    <button
                        class="absolute -top-1 -right-1 w-5 h-5 bg-destructive text-primary-foreground rounded-full text-xs hover:bg-destructive/80"
                        on:click|stopPropagation={() => deleteSchedule(schedule.id)}
                    >
                        ×
                    </button>
                </div>
            {/each}
        </div>
    </div>
{/if}

<!-- Bottom Buttons -->
<div class="mb-3">
    <Button
        class="w-full py-2 px-4 no-hover"
        variant="outline"
        on:click={addAnotherSchedule}
    >
        ➕ Add Another Schedule
    </Button>
</div>