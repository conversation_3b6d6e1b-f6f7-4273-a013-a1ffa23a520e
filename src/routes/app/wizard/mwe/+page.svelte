<script lang="ts">
    import { profile } from '$lib/stores/profile';
    import { Check } from 'lucide-svelte';
    import { onMount } from 'svelte';
    import { quintOut } from 'svelte/easing';
    import { fly } from 'svelte/transition';

    const experiences = [
        { name: 'Peace', summary: 'Steady inner quiet; centered and unpressured even when life is loud.' },
        { name: 'Joy', summary: 'The energizing feeling of pure happiness that makes you feel more alive and present.' },
        { name: 'Confidence', summary: 'Quiet self-belief that moves you forward despite uncertainty.' },
        { name: 'Connection', summary: 'Feeling seen and valued through honest bonds with others and yourself.' },
        { name: 'Purpose', summary: 'A direction you claim when an idea, value, or goal feels undeniably yours' },
        { name: 'Focus', summary: 'Tuning out noise to zero in on what matters most.' },
        { name: 'Self-worth', summary: 'Knowing you deserve respect and care simply for existing.' },
        { name: 'Calm', summary: 'Remaining centered and thoughtful instead of reactive during challenges.' },
        { name: 'Contentment', summary: 'Comfortable gratitude for what you have right now.' },
        { name: 'Courage', summary: 'Acting even while afraid and stretching your comfort zone.' },
        { name: '<PERSON>', summary: 'Belief that effort today can build a brighter tomorrow.' },
        { name: 'Clarity', summary: 'Seeing situations and yourself clearly enough to decide confidently.' },
        { name: 'Stability', summary: 'Reliable routines and support that keep you grounded.' },
        { name: 'Freedom', summary: 'Living authentically and choosing your own path without restraint.' },
        { name: 'Security', summary: 'Feeling safe, supported, and free from constant worry.' },
        { name: 'Strength', summary: 'Inner resilience to endure, recover, and stand firm.' }
    ].sort((a, b) => a.name.localeCompare(b.name));

    let maxSelections = 3;

    let showMaxSelectionMessage = false;

    function toggleExperience(experience: { name: string; summary: string }) {
        const index = $profile.experiences.findIndex(e => e.name === experience.name);
        if (index === -1) {
            if ($profile.experiences.length < maxSelections) {
                $profile.experiences = [...$profile.experiences, experience];
                showMaxSelectionMessage = false; // Reset message if an item is successfully added
            } else {
                showMaxSelectionMessage = true; // Show message if max is reached
                setTimeout(() => {
                    showMaxSelectionMessage = false;
                }, 5000); // Hide message after 5 seconds
            }
        } else {
            $profile.experiences = $profile.experiences.filter(e => e.name !== experience.name);
            showMaxSelectionMessage = false; // Reset message if an item is removed
        }
    }

    let scrollContainer: HTMLDivElement;
    let activeDotIndex = 0;

    function handleScroll() {
        if (scrollContainer) {
            const scrollLeft = scrollContainer.scrollLeft;
            const itemWidth = 256 + 16; // w-64 (256px) + gap-4 (16px)

            activeDotIndex = Math.round(scrollLeft / itemWidth);
        }
    }

    function scrollToExperience(index: number) {
        if (scrollContainer) {
            const itemWidth = 256 + 16; // w-64 (256px) + gap-4 (16px)
            scrollContainer.scrollTo({
                left: index * itemWidth,
                behavior: 'smooth'
            });
        }
    }

    onMount(() => {
        handleScroll();
    });
</script>

<h2 class="text-2xl font-bold mb-4">What do you most want to experience more of in your life?</h2>
<div class="mb-4 text-muted-foreground text-center">Select up to { maxSelections } that resonate with you.</div>

{#if showMaxSelectionMessage}
    <div
        in:fly={{ y: -20, duration: 300, easing: quintOut }}
        out:fly={{ y: -20, duration: 200, easing: quintOut }}
        class="fixed top-40 left-1/2 -translate-x-1/2 bg-yellow-100 text-yellow-800 px-4 py-2 rounded-md shadow-lg z-50 text-center border border-black"
    >
        You've reached the maximum of { maxSelections } selections.
    </div>
{/if}

<div bind:this={scrollContainer} on:scroll={handleScroll} class="flex flex-row gap-4 mb-8 overflow-x-auto pb-4 snap-x snap-mandatory">
    {#each experiences as experience (experience.name)}
        <button
            class="text-left transition-all duration-200 flex-shrink-0 w-64 h-[150px] flex flex-col bg-background text-foreground snap-center { $profile.experiences.some(e => e.name === experience.name) ? 'border-2 border-primary rounded-xl' : 'border border-border rounded-lg'}"
            on:click={() => toggleExperience(experience)}
        >
            <h3 class="text-xl font-semibold px-4 pt-4 pb-2 flex items-center h-[80px] line-clamp-2 w-full { $profile.experiences.some(e => e.name === experience.name) ? 'bg-primary text-primary-foreground rounded-t-lg' : '' }">
                <span>{experience.name}</span>
                {#if $profile.experiences.some(e => e.name === experience.name)}
                    <Check size={20} class="ml-2" />
                {/if}
            </h3>
            <hr class="border-t w-full" />
            <p class="text-sm text-muted-foreground px-4 pt-4 pb-4 h-[118px] line-clamp-3">
                {experience.summary}
            </p>
        </button>
    {/each}
</div>

<div class="flex justify-center gap-2 mt-4">
    {#each experiences as experience, i (experience.name)}
        <div class="relative">
            {#if $profile.experiences.some(e => e.name === experience.name)}
                <div class="absolute -top-2 left-1/2 -translate-x-1/2 w-4 h-0.5 bg-primary rounded-full"></div>
            {/if}
            <button
                class="w-3 h-3 rounded-full bg-gray-300 transition-colors duration-200"
                class:bg-primary={i === activeDotIndex}
                on:click={() => scrollToExperience(i)}
                aria-label="Go to experience {i + 1}: {experience.name}"
            ></button>
        </div>
    {/each}
</div>

<div class="mt-2">
    <h3 class="text-lg font-semibold mb-4 text-center">Selected Experiences:</h3>
    {#if $profile.experiences.length > 0}
        <div class="h-4 mb-2">
            <div class="flex flex-wrap gap-2 justify-center">
                {#each $profile.experiences as experience (experience.name)}
                    <span class="text-primary font-bold xbg-primary xtext-primary-foreground xpx-3 xpy-1 xrounded-full xtext-sm">
                        {experience.name}
                    </span>
                {/each}
            </div>
        </div>
    {:else}
        <p class="h-4 mb-2 text-muted-foreground">No experiences selected yet.</p>
    {/if}
</div>