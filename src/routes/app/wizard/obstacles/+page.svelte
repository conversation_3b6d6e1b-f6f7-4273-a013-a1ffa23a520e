<script lang="ts">
    import { profile } from '$lib/stores/profile';
    import { Check } from 'lucide-svelte';
    import { onMount } from 'svelte';
    import { quintOut } from 'svelte/easing';
    import { fly } from 'svelte/transition';

    const obstacles = [
        { name: 'Fear', summary: 'Worry about failure, judgment, or the unknown that holds you back from taking action.' },
        { name: 'Anger', summary: 'Frustration and resentment that clouds judgment and drains energy.' },
        { name: '<PERSON><PERSON><PERSON>', summary: 'Questioning your abilities, decisions, or worth that undermines confidence.' },
        { name: 'Overwhelmed', summary: 'Feeling buried under too many demands, choices, or responsibilities.' },
        { name: 'Exhaustion', summary: 'Physical, mental, or emotional depletion that makes everything feel harder.' },
        { name: 'Pressure', summary: 'External or internal demands that create stress and rush decisions.' },
        { name: 'Isolation', summary: 'Feeling disconnected, alone, or misunderstood by others.' },
        { name: 'Uncertainty', summary: 'Not knowing what comes next or how things will turn out.' },
        { name: 'Distractions', summary: 'External noise, notifications, or internal scattered thoughts that pull focus away.' },
        { name: 'Criticism', summary: 'Harsh judgment from others or yourself that creates self-consciousness.' },
        { name: 'Perfectionism', summary: 'Setting impossibly high standards that prevent progress and completion.' },
        { name: 'Anxiety', summary: 'Persistent worry and nervous energy about future events or outcomes.' },
        { name: 'Negative self-talk', summary: 'Internal voice that criticizes, minimizes achievements, or predicts failure.' },
        { name: 'People', summary: 'Difficult relationships, toxic dynamics, or social situations that drain energy.' },
        { name: 'Procrastination', summary: 'Delaying important tasks or decisions despite knowing they need attention.' },
        { name: 'Myself', summary: 'Self-sabotage, limiting beliefs, or personal habits that create your own barriers.' },
        { name: 'Life', summary: 'Circumstances, responsibilities, or situations beyond your control that create challenges.' },
        { name: 'Unknown', summary: 'Unclear obstacles or a general sense that something is holding you back.' }
    ].sort((a, b) => a.name.localeCompare(b.name));

    let showMaxSelectionMessage = false;

    function toggleObstacle(obstacle: { name: string; summary: string }) {
        const index = $profile.obstacles.findIndex(o => o.name === obstacle.name);
        if (index === -1) {
            if ($profile.obstacles.length < 5) {
                $profile.obstacles = [...$profile.obstacles, obstacle];
                showMaxSelectionMessage = false; // Reset message if an item is successfully added
            } else {
                showMaxSelectionMessage = true; // Show message if max is reached
                setTimeout(() => {
                    showMaxSelectionMessage = false;
                }, 5000); // Hide message after 5 seconds
            }
        } else {
            $profile.obstacles = $profile.obstacles.filter(o => o.name !== obstacle.name);
            showMaxSelectionMessage = false; // Reset message if an item is removed
        }
    }

    let scrollContainer: HTMLDivElement;
    let activeDotIndex = 0;

    function handleScroll() {
        if (scrollContainer) {
            const scrollLeft = scrollContainer.scrollLeft;
            const itemWidth = 256 + 16; // w-64 (256px) + gap-4 (16px)

            activeDotIndex = Math.round(scrollLeft / itemWidth);
        }
    }

    function scrollToObstacle(index: number) {
        if (scrollContainer) {
            const itemWidth = 256 + 16; // w-64 (256px) + gap-4 (16px)
            scrollContainer.scrollTo({
                left: index * itemWidth,
                behavior: 'smooth'
            });
        }
    }

    onMount(() => {
        handleScroll();
    });
</script>

<h2 class="text-2xl font-bold mb-4">What do you find standing in your way most often?</h2>
<div class="mb-4 text-muted-foreground text-center">Select up to 5 that closest match what you feel.</div>

{#if showMaxSelectionMessage}
    <div
        in:fly={{ y: -20, duration: 300, easing: quintOut }}
        out:fly={{ y: -20, duration: 200, easing: quintOut }}
        class="fixed top-40 left-1/2 -translate-x-1/2 bg-yellow-100 text-yellow-800 px-4 py-2 rounded-md shadow-lg z-50 text-center border border-black"
    >
        You've reached the maximum of 5 selections.
    </div>
{/if}

<div bind:this={scrollContainer} on:scroll={handleScroll} class="flex flex-row gap-4 mb-8 overflow-x-auto pb-4 snap-x snap-mandatory">
    {#each obstacles as obstacle (obstacle.name)}
        <button
            class="text-left transition-all duration-200 flex-shrink-0 w-64 h-[150px] flex flex-col bg-background text-foreground snap-center { $profile.obstacles.some(o => o.name === obstacle.name) ? 'border-2 border-primary rounded-xl' : 'border border-border rounded-lg'}"
            on:click={() => toggleObstacle(obstacle)}
        >
            <h3 class="text-xl font-semibold px-4 pt-4 pb-2 flex items-center h-[80px] line-clamp-2 w-full { $profile.obstacles.some(o => o.name === obstacle.name) ? 'bg-primary text-primary-foreground rounded-t-lg' : '' }">
                <span>{obstacle.name}</span>
                {#if $profile.obstacles.some(o => o.name === obstacle.name)}
                    <Check size={20} class="ml-2" />
                {/if}
            </h3>
            <hr class="border-t w-full" />
            <p class="text-sm text-muted-foreground px-4 pt-4 pb-4 h-[118px] line-clamp-3">
                {obstacle.summary}
            </p>
        </button>
    {/each}
</div>

<div class="flex justify-center gap-2 mt-4">
    {#each obstacles as obstacle, i (obstacle.name)}
        <div class="relative">
            {#if $profile.obstacles.some(o => o.name === obstacle.name)}
                <div class="absolute -top-2 left-1/2 -translate-x-1/2 w-4 h-0.5 bg-primary rounded-full"></div>
            {/if}
            <button
                class="w-3 h-3 rounded-full bg-gray-300 transition-colors duration-200"
                class:bg-primary={i === activeDotIndex}
                on:click={() => scrollToObstacle(i)}
                aria-label="Go to obstacle {i + 1}: {obstacle.name}"
            ></button>
        </div>
    {/each}
</div>

<div class="mt-2">
    <h3 class="text-lg font-semibold mb-4 text-center">Selected Obstacles:</h3>
    {#if $profile.obstacles.length > 0}
        <div class="h-4 mb-2">
            <div class="flex flex-wrap gap-2 justify-center">
                {#each $profile.obstacles as obstacle (obstacle.name)}
                    <span class="text-primary font-bold xbg-primary xtext-primary-foreground xpx-3 xpy-1 xrounded-full xtext-sm">
                        {obstacle.name}
                    </span>
                {/each}
            </div>
        </div>
    {:else}
        <p class="h-4 mb-2 text-muted-foreground">No obstacles selected yet.</p>
    {/if}
</div>