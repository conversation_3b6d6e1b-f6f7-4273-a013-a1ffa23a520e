import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { waitForAuthInit } from '$lib/stores/auth';
import { getSubscriptionDetails } from '$lib/services/subscriptionService';

export const ssr = false; // Disable SSR for client-side auth

export async function load({ url }) {
    if (browser) {
        // Wait for auth to initialize and check if user is authenticated
        const authState = await waitForAuthInit();
		
        if (!authState.session) {
            // User is not authenticated, redirect to signin
            goto('/auth/signin');
            return {};
        }
		
        // Get subscription details
        const subscriptionDetails = await getSubscriptionDetails(authState.user!.id);
        
        // Check if user has active subscription
        if (!subscriptionDetails.isActive && url.pathname !== '/app/free') {
            // No active subscription - redirect to free tier page
            goto('/app/free');
            return {};
        }
		
        // User is authenticated and has proper access
        return {
            user: authState.user,
            session: authState.session,
            subscription: subscriptionDetails
        };
    }
	
    return {};
}