<script lang="ts">
    import FirstTimeExperience from '$lib/components/FirstTimeExperience.svelte';
    import Dashboard from '$lib/components/Dashboard.svelte';
    import { profile } from '$lib/stores/profile';
    import { authStore } from '$lib/stores/auth';
    
    // Authentication is handled by the layout, so we can access the user directly
    $: user = $authStore.user;
    $: loading = $authStore.loading;
</script>

<svelte:head>
    <title>YourBestDays - Your Journey</title>
</svelte:head>

{#if loading}
    <!-- Loading state while checking authentication -->
    <div class="flex items-center justify-center min-h-[50vh]">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading...</p>
        </div>
    </div>
{:else if user}
    {#if $profile.wizardCompleted}
        <Dashboard />
    {:else}
        <FirstTimeExperience />
    {/if}
{/if}