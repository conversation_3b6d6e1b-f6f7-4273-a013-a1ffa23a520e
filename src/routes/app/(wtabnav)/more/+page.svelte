<script lang="ts">
    import BtnMain from '$lib/components/Form/BtnMain.svelte';
    import Button from '$lib/components/Button.svelte';
    import { signOut } from '$lib/auth';

    async function handleLogout() {
        try {
            await signOut();
        } catch (error) {
            console.error('Logout failed:', error);
        // You could add a toast notification here if you have one
        }
    }
</script>

<div class="flex flex-col space-y-4">
    <BtnMain text="Settings" to="/app/settings">
        <svelte:fragment slot="icon">
            <!-- Settings icon -->
            <svg class="text-2xl w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
        </svelte:fragment>
    </BtnMain>

    <BtnMain text="About" to="/app/about">
        <svelte:fragment slot="icon">
            <!-- User icon -->
            <svg class="text-2xl w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
        </svelte:fragment>
    </BtnMain>

    <BtnMain text="Contact" to="/contact">
        <svelte:fragment slot="icon">
            <!-- Envelope icon -->
            <svg class="text-2xl w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
        </svelte:fragment>
    </BtnMain>

    <Button
        variant="outline"
        size="lg"
        class="flex items-center p-4 rounded-lg bg-background border border-border shadow-md hover:bg-muted transition-colors w-full justify-start"
        on:click={handleLogout}
    >
        <div class="w-6 h-6 text-primary mr-3">
            <!-- Logout icon -->
            <svg class="text-2xl w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
            </svg>
        </div>
        <span class="text-foreground text-lg">Logout</span>
    </Button>
</div>