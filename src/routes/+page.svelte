<script lang="ts">
    import { goto } from '$app/navigation';
    import { onMount } from 'svelte';
    import YbdIcon from '$lib/components/YbdIcon2.svelte';
    import Button from '../lib/components/Button.svelte';
    import { authStore } from '$lib/stores/auth';
    import { browser } from '$app/environment';

    onMount(() => {
        if (browser) {
            const unsubscribe = authStore.subscribe((auth) => {
                if (auth.initialized && auth.session) {
                    goto('/app');
                }
            });
            
            // Return cleanup function
            return () => {
                unsubscribe();
            };
        }
    });

    function handleGetStarted() {
        goto('/auth/signup');
    }
	
    function handleSignIn() {
        goto('/auth/signin');
    }
</script>

<svelte:head>
    <title>YourBestDays - Transform Your Life</title>
    <meta name="description" content="Start your journey to better days with personalized guidance and proven strategies." />
</svelte:head>

<!-- Landing Page - Mobile-First Design -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Hero Section -->
    <div class="px-4 py-12 text-center">
        <div class="max-w-sm mx-auto">
            <!-- Logo/Brand -->
            <div class="mb-6">
                <h1 class="text-gray-900 mb-2">Your Best Days</h1>
            </div>
			
            <!-- Hero Image/Icon -->
            <div class="mb-8">
                <div class="w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-8">
                    <YbdIcon size="96" className="text-primary opacity-60" />
                </div>

                <p class="text-gray-600 leading-relaxed max-w-72 mx-auto">
                    Reconnect with what truly matters to you. Remember the thoughts and take the actions that free your mind.
                </p>

                <div class="text-gray-600 leading-relaxed max-w-72 mx-auto">
                    <p>
                        Transform your life...
                        <br>
                        one moment at a time
                    </p>
                </div>
            </div>

            <!-- Call-to-Action Buttons -->
            <div class="space-y-4">
                <Button
                    on:click={handleGetStarted}
                    size="xxl"
                    class="w-full"
                    
                >
                    Start Your Journey
                </Button>

                <Button
                    on:click={handleSignIn}
                    size="xxl"
                    class="w-full"
                    variant="outline"
                >
                    Sign In
                </Button>
            </div>
        </div>
    </div>
</div>