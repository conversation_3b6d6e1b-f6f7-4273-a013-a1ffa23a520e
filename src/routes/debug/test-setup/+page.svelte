<script lang="ts">
    import { browser } from '$app/environment';
  
    let output = '';
  
    function log(message: string) {
        output += message + '\n';
        console.log(message);
    }
  
    function setupProfile() {
        if (!browser) return;
    
        output = '';
    
        const testProfile = {
            personal: { 
                preferredName: 'Test User', 
                ageRange: '25-34', 
                genderIdentity: 'prefer-not-to-say' 
            },
            preferences: { 
                theme: { name: 'blue', colors: {} } 
            },
            experiences: [
                { name: 'meditation', summary: 'I practice meditation for clarity' },
                { name: 'exercise', summary: 'Regular exercise helps me stay focused' }
            ],
            obstacles: [
                { name: 'anxiety', summary: 'I struggle with anxiety sometimes' },
                { name: 'stress', summary: 'Work stress affects my wellbeing' }
            ],
            goals: [],
            schedules: [
                {
                    id: 1,
                    name: 'Friday Schedule',
                    selectedDays: ['Fri'],
                    timeRanges: [
                        {
                            id: 1,
                            startHour: 9,
                            endHour: 10,
                            maxPerHour: 2
                        }
                    ]
                }
            ],
            wizardCompleted: true
        };

        log('Setting up test profile with proper schedule format...');
        localStorage.setItem('profile', JSON.stringify(testProfile));
        log('Profile saved to localStorage');
    
        // Verify it was saved
        const saved = localStorage.getItem('profile');
        if (saved) {
            const parsed = JSON.parse(saved);
            log('Verified saved profile schedules: ' + JSON.stringify(parsed.schedules, null, 2));
            log('Wizard completed: ' + parsed.wizardCompleted);
            log('Experiences: ' + parsed.experiences.length);
            log('Obstacles: ' + parsed.obstacles.length);
        }
    
        // Trigger page reload to pick up new profile
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
  
    function clearProfile() {
        if (!browser) return;
    
        output = '';
        localStorage.removeItem('profile');
        log('Profile cleared from localStorage');
    
        // Trigger page reload
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
  
    function checkProfile() {
        if (!browser) return;
    
        output = '';
        const saved = localStorage.getItem('profile');
        if (saved) {
            const parsed = JSON.parse(saved);
            log('Current profile data:');
            log(JSON.stringify(parsed, null, 2));
        } else {
            log('No profile data found in localStorage');
        }
    }
</script>

<div class="container">
    <h1>Profile Setup Test</h1>
  
    <div class="actions">
        <button on:click={setupProfile} class="btn btn-primary">Setup Test Profile</button>
        <button on:click={clearProfile} class="btn btn-secondary">Clear Profile</button>
        <button on:click={checkProfile} class="btn btn-info">Check Profile</button>
    </div>
  
    <pre class="output">{output}</pre>
  
    <div class="links">
        <a href="/debug/debug">Go to Debug Page</a>
        <a href="/debug/notifications-test">Go to Notifications Test</a>
    </div>
</div>

<style>
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .actions {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
  }
  
  .btn-primary {
    background-color: #007bff;
    color: white;
  }
  
  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }
  
  .btn-info {
    background-color: #17a2b8;
    color: white;
  }
  
  .output {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    white-space: pre-wrap;
    min-height: 200px;
    font-family: monospace;
  }
  
  .links {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
  }
  
  .links a {
    color: #007bff;
    text-decoration: none;
  }
  
  .links a:hover {
    text-decoration: underline;
  }
</style>