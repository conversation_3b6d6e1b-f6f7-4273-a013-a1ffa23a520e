<script lang="ts">
    import { onMount } from 'svelte';
    import { profile } from '$lib/stores/profile';
    import { notificationService } from '$lib/services/ybdmessage/notificationService';
    import { scheduleProcessor } from '$lib/services/ybdmessage/scheduleProcessor';
    import { messageMatcher } from '$lib/services/ybdmessage/messageMatcher';
    import { dayAbbreviationToIndex } from '$lib/utils/dateUtils';
    import type { Profile } from '$lib/stores/profile';

    interface TimeRange {
        startHour: number;
        endHour: number;
        maxPerHour: number;
    }

    let debugInfo: unknown = null;
    let currentProfile: Profile | null = null;

    onMount(async () => {
        // Subscribe to profile changes
        profile.subscribe(async (p) => {
            currentProfile = p;
            if (p.wizardCompleted) {
                try {
                    debugInfo = await notificationService.getDebugInfo(p);
                    console.log('Debug Info:', debugInfo);
                } catch (error) {
                    console.error('Error getting debug info:', error);
                }
            }
        });
    });

    function getCurrentDayInfo() {
        const now = new Date();
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        return {
            date: now.toISOString(),
            dayOfWeek: now.getDay(),
            dayName: dayNames[now.getDay()],
            fridayIndex: dayAbbreviationToIndex('Fri')
        };
    }

    function testScheduleGeneration() {
        if (!currentProfile?.schedules) return null;
    
        const now = new Date();
        const timeSlots = scheduleProcessor.generateTimeSlots(currentProfile.schedules, now);
        return {
            totalSlots: timeSlots.length,
            slots: timeSlots.map(slot => ({
                time: slot.time.toISOString(),
                priority: slot.priority,
                scheduleId: slot.scheduleId
            }))
        };
    }

    function testMessageMatching() {
        if (!currentProfile) return null;
    
        const matchedMessages = messageMatcher.matchMessages(currentProfile);
        const stats = messageMatcher.getMatchingStats(currentProfile);
    
        return {
            totalMatched: matchedMessages.length,
            stats,
            messages: matchedMessages.slice(0, 3).map(m => ({
                content: m.message.message?.substring(0, 50) + '...',
                priority: m.priority,
                matchType: m.matchType,
                keywords: m.matchedKeywords
            }))
        };
    }

    async function clearCacheAndRegenerate() {
        if (!currentProfile) return;
    
        try {
            // Clear the cache and force regeneration
            await notificationService.forceRegeneration(currentProfile);
      
            // Refresh debug info
            debugInfo = await notificationService.getDebugInfo(currentProfile);
            console.log('Cache cleared and regenerated:', debugInfo);
        } catch (error) {
            console.error('Error clearing cache:', error);
        }
    }

    function getAvailableTags() {
        return messageMatcher.getAvailableTags();
    }
</script>

<div class="debug-container">
    <h1>Notification System Debug</h1>
  
    <div class="section">
        <h2>Current Date/Time Info</h2>
        <pre>{JSON.stringify(getCurrentDayInfo(), null, 2)}</pre>
    </div>

    {#if currentProfile}
        <div class="section">
            <h2>Profile Status</h2>
            <p>Wizard Completed: {currentProfile.wizardCompleted}</p>
            <p>Experiences: {currentProfile.experiences?.length || 0}</p>
            <p>Obstacles: {currentProfile.obstacles?.length || 0}</p>
            <p>Schedules: {currentProfile.schedules?.length || 0}</p>
      
            {#if currentProfile.schedules?.length > 0}
                <h3>Schedule Details:</h3>
                {#each currentProfile.schedules as schedule, i (i)}
                    <div class="schedule">
                        <p>Days: {schedule.selectedDays.join(', ')}</p>
                        <p>Time Ranges: {schedule.timeRanges.map((r: TimeRange) => `${r.startHour}:00-${r.endHour}:00 (${r.maxPerHour}/hr)`).join(', ')}</p>
                    </div>
                {/each}
            {/if}
        </div>

        <div class="section">
            <h2>Schedule Generation Test</h2>
            <pre>{JSON.stringify(testScheduleGeneration(), null, 2)}</pre>
        </div>

        <div class="section">
            <h2>Message Matching Test</h2>
            <button on:click={clearCacheAndRegenerate}>Clear Cache & Regenerate</button>
            <pre>{JSON.stringify(testMessageMatching(), null, 2)}</pre>
        </div>

        <div class="section">
            <h2>Available Message Tags</h2>
            <pre>{JSON.stringify(getAvailableTags(), null, 2)}</pre>
        </div>

        {#if debugInfo}
            <div class="section">
                <h2>Notification Service Debug</h2>
                <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
            </div>
        {/if}
    {:else}
        <p>No profile data available. Complete the wizard first.</p>
    {/if}
</div>

<style>
  .debug-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    font-family: monospace;
  }

  .section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #f9f9f9;
  }

  .section h2 {
    margin-top: 0;
    color: #333;
  }

  pre {
    background: #fff;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    white-space: pre-wrap;
  }

  .schedule {
    background: #fff;
    padding: 0.5rem;
    margin: 0.5rem 0;
    border-radius: 4px;
  }
</style>