<script lang="ts">
    import Button from '$lib/components/Button.svelte';
    import ThemeSwitcher from '$lib/components/ThemeSwitcher.svelte';
    import DarkModeToggle from '$lib/components/DarkModeToggle.svelte';
</script>

<div class="min-h-screen bg-background p-4">
    <!-- Header with theme controls -->
    <header class="mb-8 p-4 bg-card border border-border rounded-lg">
        <div class="flex justify-between items-center">
            <h1 class="text-xl font-semibold text-foreground">Theme Test Page</h1>
            <div class="flex items-center gap-3">
                <DarkModeToggle />
                <ThemeSwitcher />
            </div>
        </div>
    </header>

    <!-- Content to test themes -->
    <div class="space-y-6">
        <div class="p-6 bg-card border border-border rounded-lg">
            <h2 class="text-lg font-semibold text-card-foreground mb-4">Color Theme Test</h2>
            <p class="text-muted-foreground mb-4">
                Use the theme switcher above to change colors, and the dark mode toggle to switch between light and dark modes.
            </p>
			
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="space-y-2">
                    <div class="flex items-center gap-3">
                        <div class="w-4 h-4 rounded bg-primary"></div>
                        <span class="text-sm text-muted-foreground">Primary</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="w-4 h-4 rounded bg-secondary"></div>
                        <span class="text-sm text-muted-foreground">Secondary</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="w-4 h-4 rounded bg-accent"></div>
                        <span class="text-sm text-muted-foreground">Accent</span>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center gap-3">
                        <div class="w-4 h-4 rounded bg-muted"></div>
                        <span class="text-sm text-muted-foreground">Muted</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="w-4 h-4 rounded border border-border"></div>
                        <span class="text-sm text-muted-foreground">Border</span>
                    </div>
                </div>
            </div>

            <div class="flex gap-3 flex-wrap">
                <Button>Primary Button</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
            </div>
        </div>

        <div class="p-6 bg-card border border-border rounded-lg">
            <h3 class="text-lg font-semibold text-card-foreground mb-3">Instructions</h3>
            <ol class="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
                <li>Use the theme switcher (dropdown) to select different color themes</li>
                <li>Use the dark mode toggle (moon/sun icon) to switch between light and dark modes</li>
                <li>Each theme should have both light and dark variants</li>
                <li>Colors should change smoothly when switching themes or modes</li>
            </ol>
        </div>
    </div>
</div>