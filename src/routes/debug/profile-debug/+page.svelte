<script lang="ts">
    import { onMount } from 'svelte';
    import { profile } from '$lib/stores/profile';
  
    let rawProfileData = '';
    let parsedProfile: typeof $profile | null = null;
    let error = '';
  
    onMount(() => {
        // Get raw localStorage data
        try {
            rawProfileData = localStorage.getItem('profile') || 'No profile data found';
            if (rawProfileData !== 'No profile data found') {
                parsedProfile = JSON.parse(rawProfileData);
            }
        } catch (e) {
            error = `Error parsing profile: ${e instanceof Error ? e.message : String(e)}`;
        }
    });
  
    function clearProfile() {
        localStorage.removeItem('profile');
        location.reload();
    }
</script>

<div class="p-6">
    <h1 class="text-2xl font-bold mb-4">Profile Debug</h1>
  
    <div class="mb-6">
        <h2 class="text-lg font-semibold mb-2">Current Profile Store:</h2>
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto">{JSON.stringify($profile, null, 2)}</pre>
    </div>
  
    <div class="mb-6">
        <h2 class="text-lg font-semibold mb-2">Raw localStorage Data:</h2>
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto">{rawProfileData}</pre>
    </div>
  
    {#if parsedProfile}
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-2">Parsed Profile:</h2>
            <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto">{JSON.stringify(parsedProfile, null, 2)}</pre>
        </div>
    {/if}
  
    {#if error}
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-2 text-red-600">Error:</h2>
            <p class="text-red-600">{error}</p>
        </div>
    {/if}
  
    <div class="mb-6">
        <h2 class="text-lg font-semibold mb-2">Quick Stats:</h2>
        <ul class="list-disc list-inside">
            <li>Experiences: {$profile.experiences?.length || 0}</li>
            <li>Obstacles: {$profile.obstacles?.length || 0}</li>
            <li>Schedules: {$profile.schedules?.length || 0}</li>
            <li>Wizard Completed: {$profile.wizardCompleted}</li>
        </ul>
    </div>
  
    <button 
        on:click={clearProfile}
        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
    >
        Clear Profile & Reload
    </button>
</div>