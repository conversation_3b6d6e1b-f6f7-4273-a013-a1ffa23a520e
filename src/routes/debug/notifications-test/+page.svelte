<script lang="ts">
    import { onMount } from 'svelte';
    import { profile } from '$lib/stores/profile';
    import {
        notificationQueue,
        queueStatus,
        queueStats,
        upcomingNotifications,
        todaysNotifications,
        initializeNotificationQueue,
        regenerateNotificationQueue,
        getNotificationDebugInfo,
        markNotificationRead,
        markNotificationSent
    } from '$lib/stores/notificationQueue';
    import NotificationAPI from '$lib/services/ybdmessage/notificationAPI';
    import Button from '$lib/components/Button.svelte';

    let debugInfo: {
        profileHash: string;
        scheduleStats: Record<string, unknown>;
        matchingStats: Record<string, unknown>;
        cacheInfo: Record<string, unknown>;
    } | null = null;
    let systemHealth: {
        status: string;
        issues: string[];
    } | null = null;
    let isLoading = false;

    onMount(async () => {
        await initializeNotificationQueue();
        await updateDebugInfo();
        await updateSystemHealth();
    });

    async function updateDebugInfo() {
        debugInfo = await getNotificationDebugInfo();
    }

    async function updateSystemHealth() {
        systemHealth = await NotificationAPI.getSystemHealth();
    }

    async function handleRegenerate() {
        isLoading = true;
        try {
            await regenerateNotificationQueue();
            await updateDebugInfo();
            await updateSystemHealth();
        } finally {
            isLoading = false;
        }
    }


    function handleMarkSent(notificationId: string) {
        markNotificationSent(notificationId);
    }

    function handleMarkRead(notificationId: string) {
        markNotificationRead(notificationId);
    }

    function formatTime(date: Date): string {
        return date.toLocaleString();
    }

    function getStatusColor(status: string): string {
        switch (status) {
            case 'ready': return 'text-green-600';
            case 'loading': return 'text-yellow-600';
            case 'error': return 'text-red-600';
            case 'empty': return 'text-gray-600';
            default: return 'text-gray-600';
        }
    }

    function getHealthColor(status: string): string {
        switch (status) {
            case 'healthy': return 'text-green-600';
            case 'warning': return 'text-yellow-600';
            case 'error': return 'text-red-600';
            default: return 'text-gray-600';
        }
    }
</script>

<div class="container mx-auto p-6 max-w-6xl">
    <h1 class="text-3xl font-bold mb-6">Notification System Test</h1>

    <!-- System Status -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Queue Status -->
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Queue Status</h2>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span>Status:</span>
                    <span class={getStatusColor($queueStatus)}>{$queueStatus}</span>
                </div>
                <div class="flex justify-between">
                    <span>Total Notifications:</span>
                    <span>{$notificationQueue.length}</span>
                </div>
                <div class="flex justify-between">
                    <span>Upcoming (6h):</span>
                    <span>{$upcomingNotifications.length}</span>
                </div>
                <div class="flex justify-between">
                    <span>Today:</span>
                    <span>{$todaysNotifications.length}</span>
                </div>
            </div>
        </div>

        <!-- System Health -->
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">System Health</h2>
            {#if systemHealth}
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span>Health:</span>
                        <span class={getHealthColor(systemHealth.status)}>{systemHealth.status}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Issues:</span>
                        <span>{systemHealth.issues.length}</span>
                    </div>
                    {#if systemHealth.issues.length > 0}
                        <div class="mt-2">
                            <p class="text-sm font-medium">Issues:</p>
                            <ul class="text-sm text-red-600 list-disc list-inside">
                                {#each systemHealth.issues as issue, i (i)}
                                    <li>{issue}</li>
                                {/each}
                            </ul>
                        </div>
                    {/if}
                </div>
            {:else}
                <p class="text-gray-500">Loading health status...</p>
            {/if}
        </div>
    </div>


    <!-- Actions -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold mb-4">Actions</h2>
        <div class="flex flex-wrap gap-4">
            <Button
                on:click={handleRegenerate}
                disabled={isLoading}
                class="bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-400"
            >
                {isLoading ? 'Regenerating...' : 'Regenerate Notifications'}
            </Button>
      
            <Button
                on:click={updateDebugInfo}
                variant="outline"
            >
                Refresh Debug Info
            </Button>
      
            <Button
                on:click={updateSystemHealth}
                variant="outline"
            >
                Check Health
            </Button>
        </div>
    </div>

    <!-- Profile Summary -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold mb-4">Profile Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <h3 class="font-medium mb-2">Experiences ({$profile.experiences.length})</h3>
                <ul class="text-sm space-y-1">
                    {#each $profile.experiences as exp, i (i)}
                        <li class="text-blue-600">{exp.name}</li>
                    {/each}
                </ul>
            </div>
      
            <div>
                <h3 class="font-medium mb-2">Obstacles ({$profile.obstacles.length})</h3>
                <ul class="text-sm space-y-1">
                    {#each $profile.obstacles as obs, i (i)}
                        <li class="text-red-600">{obs.name}</li>
                    {/each}
                </ul>
            </div>
      
            <div>
                <h3 class="font-medium mb-2">Schedules ({$profile.schedules.length})</h3>
                <ul class="text-sm space-y-1">
                    {#each $profile.schedules as schedule, i (i)}
                        <li>{schedule.selectedDays.join(', ')} - {schedule.timeRanges.length} ranges</li>
                    {/each}
                </ul>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    {#if $queueStats}
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">Statistics</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{$queueStats.totalGenerated}</div>
                    <div class="text-sm text-gray-600">Generated</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{$queueStats.totalSent}</div>
                    <div class="text-sm text-gray-600">Sent</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">{$queueStats.totalRead}</div>
                    <div class="text-sm text-gray-600">Read</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">{$queueStats.totalGenerated - $queueStats.totalRead}</div>
                    <div class="text-sm text-gray-600">Unread</div>
                </div>
            </div>
        </div>
    {/if}

    <!-- Upcoming Notifications -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold mb-4">Upcoming Notifications (Next 6 Hours)</h2>
        {#if $upcomingNotifications.length > 0}
            <div class="space-y-4">
                {#each $upcomingNotifications.slice(0, 5) as notification (notification.id)}
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-medium">{notification.message.content?.substring(0, 50)}...</h3>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs px-2 py-1 rounded-full bg-{notification.priority === 'high' ? 'red' : notification.priority === 'medium' ? 'yellow' : 'gray'}-100 text-{notification.priority === 'high' ? 'red' : notification.priority === 'medium' ? 'yellow' : 'gray'}-800">
                                    {notification.priority}
                                </span>
                                <span class="text-xs text-gray-500">
                                    {formatTime(notification.scheduledTime)}
                                </span>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">{notification.message.content}</p>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">
                                Keywords: {notification.matchedKeywords.join(', ') || 'None'}
                            </div>
                            <div class="space-x-2">
                                <button 
                                    on:click={() => handleMarkSent(notification.id)}
                                    class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded"
                                    disabled={notification.sent}
                                >
                                    {notification.sent ? 'Sent' : 'Mark Sent'}
                                </button>
                                <button 
                                    on:click={() => handleMarkRead(notification.id)}
                                    class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded"
                                    disabled={notification.read}
                                >
                                    {notification.read ? 'Read' : 'Mark Read'}
                                </button>
                            </div>
                        </div>
                    </div>
                {/each}
                {#if $upcomingNotifications.length > 5}
                    <p class="text-sm text-gray-500 text-center">
                        ... and {$upcomingNotifications.length - 5} more
                    </p>
                {/if}
            </div>
        {:else}
            <p class="text-gray-500">No upcoming notifications in the next 6 hours.</p>
        {/if}
    </div>

    <!-- All Scheduled Notifications -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold mb-4">All Scheduled Notifications</h2>
        {#if $notificationQueue.length > 0}
            <div class="space-y-4">
                {#each $notificationQueue.slice(0, 10) as notification (notification.id)}
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-medium">{notification.message.content?.substring(0, 50)}...</h3>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs px-2 py-1 rounded-full bg-{notification.priority === 'high' ? 'red' : notification.priority === 'medium' ? 'yellow' : 'gray'}-100 text-{notification.priority === 'high' ? 'red' : notification.priority === 'medium' ? 'yellow' : 'gray'}-800">
                                    {notification.priority}
                                </span>
                                <span class="text-xs text-gray-500">
                                    {formatTime(notification.scheduledTime)}
                                </span>
                                {#if notification.sent}
                                    <span class="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800">Sent</span>
                                {/if}
                                {#if notification.read}
                                    <span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">Read</span>
                                {/if}
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">{notification.message.content}</p>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">
                                Keywords: {notification.matchedKeywords.join(', ') || 'None'}
                            </div>
                            <div class="space-x-2">
                                <button
                                    on:click={() => handleMarkSent(notification.id)}
                                    class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded disabled:opacity-50"
                                    disabled={notification.sent}
                                >
                                    {notification.sent ? 'Sent' : 'Mark Sent'}
                                </button>
                                <button
                                    on:click={() => handleMarkRead(notification.id)}
                                    class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded disabled:opacity-50"
                                    disabled={notification.read}
                                >
                                    {notification.read ? 'Read' : 'Mark Read'}
                                </button>
                            </div>
                        </div>
                    </div>
                {/each}
                {#if $notificationQueue.length > 10}
                    <p class="text-sm text-gray-500 text-center">
                        ... and {$notificationQueue.length - 10} more notifications
                    </p>
                {/if}
            </div>
        {:else}
            <div class="text-center py-8">
                <p class="text-gray-500 mb-2">No notifications in queue.</p>
                <p class="text-sm text-gray-400">
                    {#if $profile.schedules.length === 0}
                        Add schedules to your profile to generate notifications.
                    {:else if $profile.experiences.length === 0 && $profile.obstacles.length === 0}
                        Add experiences or obstacles to your profile to generate notifications.
                    {:else}
                        Click "Regenerate Notifications" to create new notifications.
                    {/if}
                </p>
            </div>
        {/if}
    </div>

    <!-- Debug Information -->
    {#if debugInfo}
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium mb-2">Profile Hash</h3>
                    <p class="text-xs font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded">{debugInfo.profileHash}</p>
          
                    <h3 class="font-medium mb-2 mt-4">Schedule Stats</h3>
                    <pre class="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto">{JSON.stringify(debugInfo.scheduleStats, null, 2)}</pre>
                </div>
        
                <div>
                    <h3 class="font-medium mb-2">Matching Stats</h3>
                    <pre class="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto">{JSON.stringify(debugInfo.matchingStats, null, 2)}</pre>
          
                    <h3 class="font-medium mb-2 mt-4">Cache Info</h3>
                    <pre class="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto">{JSON.stringify(debugInfo.cacheInfo, null, 2)}</pre>
                </div>
            </div>
        </div>
    {/if}
</div>

<style>
  .container {
    min-height: 100vh;
  }
</style>