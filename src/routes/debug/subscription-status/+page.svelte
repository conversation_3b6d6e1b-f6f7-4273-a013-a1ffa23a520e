<script lang="ts">
    import { onMount } from 'svelte';
    import { authStore } from '$lib/stores/auth';
    import { getSubscriptionDetails } from '$lib/services/subscriptionService';
    import type { SubscriptionStatus } from '$lib/services/subscriptionService';
    import { goto } from '$app/navigation';
    import type { AuthState } from '$lib/stores/auth';
    
    let authState: AuthState | null = null;
    let subscriptionStatus: SubscriptionStatus | null = null;
    let loading = true;
    let error = '';
    
    onMount(() => {
        const unsubscribe = authStore.subscribe(async (state) => {
            authState = state;
            
            if (state.initialized) {
                if (state.user) {
                    try {
                        subscriptionStatus = await getSubscriptionDetails(state.user.id);
                    } catch (err) {
                        error = 'Failed to check subscription status';
                        console.error(err);
                    }
                }
                loading = false;
            }
        });
        
        return unsubscribe;
    });
</script>

<svelte:head>
    <title>Subscription Status Debug - YourBestDays</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8 px-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">Subscription Status Debug</h1>
            
            {#if loading}
                <div class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading...</p>
                </div>
            {:else}
                <div class="space-y-6">
                    <!-- Authentication Status -->
                    <div class="border rounded-lg p-4">
                        <h2 class="text-lg font-semibold text-gray-800 mb-3">Authentication Status</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <span class="font-medium">Initialized:</span>
                                <span class="ml-2 px-2 py-1 rounded text-sm {authState?.initialized ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    {authState?.initialized ? 'Yes' : 'No'}
                                </span>
                            </div>
                            <div>
                                <span class="font-medium">User Authenticated:</span>
                                <span class="ml-2 px-2 py-1 rounded text-sm {authState?.user ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    {authState?.user ? 'Yes' : 'No'}
                                </span>
                            </div>
                            {#if authState?.user}
                                <div class="md:col-span-2">
                                    <span class="font-medium">User ID:</span>
                                    <span class="ml-2 font-mono text-sm">{authState.user.id}</span>
                                </div>
                                <div class="md:col-span-2">
                                    <span class="font-medium">Email:</span>
                                    <span class="ml-2">{authState.user.email}</span>
                                </div>
                            {/if}
                        </div>
                    </div>
                    
                    <!-- Subscription Status -->
                    <div class="border rounded-lg p-4">
                        <h2 class="text-lg font-semibold text-gray-800 mb-3">Subscription Status</h2>
                        {#if authState?.user}
                            {#if subscriptionStatus}
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <span class="font-medium">Has Active Subscription:</span>
                                        <span class="ml-2 px-2 py-1 rounded text-sm {subscriptionStatus.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                            {subscriptionStatus.isActive ? 'Yes' : 'No'}
                                        </span>
                                    </div>
                                    <div>
                                        <span class="font-medium">Status:</span>
                                        <span class="ml-2 px-2 py-1 rounded text-sm bg-gray-100 text-gray-800">
                                            {subscriptionStatus.status || 'No subscription'}
                                        </span>
                                    </div>
                                    {#if subscriptionStatus.isInGracePeriod}
                                        <div>
                                            <span class="font-medium">Grace Period:</span>
                                            <span class="ml-2 px-2 py-1 rounded text-sm bg-yellow-100 text-yellow-800">
                                                Active
                                            </span>
                                        </div>
                                    {/if}
                                    {#if subscriptionStatus.expiresAt}
                                        <div>
                                            <span class="font-medium">Expires:</span>
                                            <span class="ml-2">{new Date(subscriptionStatus.expiresAt).toLocaleDateString()}</span>
                                        </div>
                                    {/if}
                                    {#if subscriptionStatus.plan}
                                        <div class="md:col-span-2">
                                            <span class="font-medium">Plan ID:</span>
                                            <span class="ml-2 font-mono text-sm">{subscriptionStatus.plan}</span>
                                        </div>
                                    {/if}
                                </div>
                            {:else if error}
                                <div class="bg-red-50 border border-red-200 rounded-md p-3">
                                    <p class="text-red-800">{error}</p>
                                </div>
                            {:else}
                                <p class="text-gray-600">No subscription data available</p>
                            {/if}
                        {:else}
                            <p class="text-gray-600">User must be authenticated to check subscription status</p>
                        {/if}
                    </div>
                    
                    <!-- Actions -->
                    <div class="border rounded-lg p-4">
                        <h2 class="text-lg font-semibold text-gray-800 mb-3">Actions</h2>
                        <div class="flex flex-wrap gap-3">
                            <button
                                on:click={() => goto('/app')}
                                class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                            >
                                Try to Access App
                            </button>
                            <button
                                on:click={() => goto('/pricing')}
                                class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                            >
                                Go to Pricing
                            </button>
                            <button
                                on:click={() => goto('/subscription/verify')}
                                class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
                            >
                                Verify Subscription
                            </button>
                            {#if !authState?.user}
                                <button
                                    on:click={() => goto('/auth/signin')}
                                    class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
                                >
                                    Sign In
                                </button>
                            {/if}
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    </div>
</div>