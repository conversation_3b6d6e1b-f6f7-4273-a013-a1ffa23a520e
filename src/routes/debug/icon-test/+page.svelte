<script>
    import { <PERSON>, <PERSON>, Settings } from 'lucide-svelte';
</script>

<div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Icon Test Page</h1>
  
    <div class="space-y-4">
        <div class="flex items-center gap-3">
            <Rocket width="24" height="24" />
            <span>Rocket Icon</span>
        </div>
    
        <div class="flex items-center gap-3">
            <Star width="24" height="24" />
            <span>Star Icon</span>
        </div>
    
        <div class="flex items-center gap-3">
            <Settings width="24" height="24" />
            <span>Settings Icon</span>
        </div>
    </div>
  
    <p class="mt-6 text-sm text-muted-foreground">
        If you can see the icons above, then Lucide Svelte is working correctly in the main Vite environment.
    </p>
</div>