<script lang="ts">
	import Card from '$lib/components/CardBase.svelte';
	import NavTabBar from '$lib/components/NavTabBar.svelte';
	// Icon components for slot
	import { Leaf, AlertOctagon, Zap, Circle } from 'lucide-svelte';
	
	let recommendations_unread = 4;
</script>

<div class="flex flex-col h-screen">
	<!-- Scrollable page content -->
	<div class="flex-1 overflow-y-auto bg-background">
		<div class="p-4">
			<div class="flex flex-col items-center min-h-screen">
				<div class="text-left px-4">
					<p class="text-lg text-muted-foreground mb-1 text-center">Get Back To...</p>
				
					<h1 class="text-4xl font-bold text-foreground mb-4 text-center">Your Best Days</h1>
					
					<div class="w-full max-w-md h-px bg-border"></div>

					<p class="text-lg text-foreground mt-4 text-center font-bold">Choose your path...</p>
				</div>

				<div class="space-y-8">
					{#if recommendations_unread === 0}
						<div class="text-left p-4">
							<p class="text-foreground mb-6 max-w-md">
								Great job finishing your recommendations. Now live your life. Do what's in your power to make the best of this moment regardless of the last.
							</p>
							
							<p class="text-foreground mb-6 max-w-md">
								Embrace alternative thoughts and actions to empower your mind. Do what's in your power to relieve mental suffering and achieve your goals.
							</p>
							
							<p class="text-foreground mb-6 max-w-md text-center">
								Focus on this moment, to make today your best day.
							</p>
						</div>
					{:else}
						<div>
							<Card
								class="mt-8"
								iconName="Leaf"
								title="Recommendations"
								content="See unread YBD messages and activities. These can support a mindset for your goals based on your profile."
								count={recommendations_unread}
								destination="/ybd"
							/>
						</div>
					{/if}

					<Card
						iconName="AlertOctagon"
					    title="Experience Wizard"
					    content="Your profile for living life intentionally. Revisit this often to ensure goal, focus and action alignment."
					    destination="/wizard"
					/>

					<Card
						iconName="AlertOctagon"
					    title="Beliefs Explorer"
					    content="Explore discomfort and paths for relief through guided beliefs exploration."
					    destination="/beliefs-explore"
					/>

					<Card
						iconName="AlertOctagon"
					    title="Overcome"
					    content="Relief right now. Re-discover perspectives and actions that lead to inner balance."
					    destination="/ybd"
					/>

					<Card
						iconName="Zap"
					    title="Actions"
					    content="Jump right into actions list to find something to do right now that helps (has filters, etc)."
					    destination="/ybd"
					/>
				</div>
			</div>
		</div>
	</div>

	<!-- Sticky bottom -->
	<div class="sticky bottom-0 z-10">
		<NavTabBar />
	</div>
</div>