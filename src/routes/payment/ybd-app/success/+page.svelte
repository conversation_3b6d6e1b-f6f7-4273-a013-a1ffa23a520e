<script lang="ts">
    import { goto } from '$app/navigation';
    import { onMount } from 'svelte';
    import { page } from '$app/stores';
    import { authStore } from '$lib/stores/auth';
    import { checkSubscriptionStatus } from '$lib/services/subscriptionService';

    let loading = true;
    let success = false;
    let error = '';

    onMount(() => {
        const sessionId = $page.url.searchParams.get('session_id');
        
        if (!sessionId) {
            error = 'No session ID provided';
            loading = false;
            return;
        }

        // Wait for auth to be ready
        const unsubscribe = authStore.subscribe(async (authState) => {
            if (!authState.initialized) return;
            
            if (!authState.session || !authState.user) {
                goto('/auth/signin');
                return;
            }

            try {
                // Check if subscription is now active
                const isSubscriptionActive = await checkSubscriptionStatus(authState.user.id);
                
                if (isSubscriptionActive) {
                    success = true;
                    // Redirect to app after a short delay
                    setTimeout(() => {
                        goto('/app?payment=success&welcome=true');
                    }, 2000);
                } else {
                    error = 'Payment processed but subscription not found. Please contact support.';
                }
            } catch (err) {
                console.error('Error checking subscription:', err);
                error = 'Error verifying payment. Please contact support.';
            } finally {
                loading = false;
            }
        });

        return unsubscribe;
    });
</script>

<svelte:head>
    <title>Payment Success - Your Best Days</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center px-4">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {#if loading}
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h1 class="text-xl font-semibold text-gray-900 mb-2">Processing Payment...</h1>
            <p class="text-gray-600">Please wait while we verify your subscription.</p>
        {:else if success}
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
            <p class="text-gray-600 mb-4">Your subscription has been activated. Redirecting you to the app...</p>
            <div class="animate-pulse text-blue-600">Taking you to your dashboard...</div>
        {:else if error}
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Payment Issue</h1>
            <p class="text-gray-600 mb-4">{error}</p>
            <div class="space-y-2">
                <button
                    on:click={() => goto('/pricing')}
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                    Try Again
                </button>
                <button
                    on:click={() => goto('/app')}
                    class="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors"
                >
                    Go to App
                </button>
            </div>
        {/if}

        <div class="bg-gray-50 rounded-b-xl py-3 mt-8">
            <p class="text-xs text-gray-600">
                Your Best Days - brought to you by UpliftingActions LLC
            </p>
        </div>
    </div>
</div>