<script>
    import { page } from '$app/state';
</script>

<div class="min-h-screen bg-background flex items-center justify-center p-6">
    <div class="text-center max-w-2xl mx-auto">
        <div class="mb-8">
            {#if page.status === 404}
                <h1 class="font-primary text-primary mb-4">404</h1>
                <h2 class="font-primary text-foreground mb-6">Page Not Found</h2>
                <p class="text-muted-foreground mb-8 max-w-md mx-auto">
                    Oops! The page you're looking for doesn't exist or has been moved.
                </p>
            {:else}
                <h1 class="font-primary text-primary mb-4">{page.status || 'Error'}</h1>
                <h2 class="font-primary text-foreground mb-6">Something went wrong</h2>
                <p class="text-muted-foreground mb-8 max-w-md mx-auto">
                    {page.error?.message || 'An unexpected error occurred'}
                </p>
            {/if}
        </div>
    
        <a
            href="/"
            class="inline-flex items-center justify-center rounded-lg bg-primary px-8 py-3 text-primary-foreground font-medium transition-all duration-200 hover:bg-primary-600 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background"
        >
            Return to Homepage
        </a>
    </div>
</div>
