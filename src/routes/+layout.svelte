<script lang="ts">
    import '../app.css';
    import { currentTheme, darkMode } from '$lib/stores/theme';
    import { authStore }               from '$lib/stores/auth';
    import { goto }                    from '$app/navigation';
    import { browser }                 from '$app/environment';
    import { onMount }                 from 'svelte';
    import { App as CapApp }           from '@capacitor/app';

    // trigger theme & auth side-effects
    void $currentTheme;
    void $darkMode;
    void $authStore;

    function handleDeepLink(url: string) {
        const parsed = new URL(url);

        switch (parsed.pathname) {
            case '/payment/ybd-app/success': {
                const sessionId = parsed.searchParams.get('session_id');
                goto(`/payment/ybd-app/confirm?session_id=${sessionId}`);
                break;
            }
            case '/payment/ybd-app/cancel':
                goto('/pricing');
                break;
            case '/auth/callback':
                // preserve whatever query Rx Params your OAuth flow needs
                goto(`/auth/callback${parsed.search}`);
                break;
        }
    }

    onMount(() => {
        if (!browser) return;

        // 1) Cold-start deep-link (app not already running)
        CapApp.getLaunchUrl().then(info => {
            if (info?.url) {
                handleDeepLink(info.url);
            }
        });

        // 2) Runtime deep-links (app already in foreground/background)
        CapApp.addListener('appUrlOpen', ({ url }) => {
            handleDeepLink(url);
        });

        // 3) Your existing authStore redirect logic
        const unsubscribe = authStore.subscribe((auth) => {
            if (auth.initialized && auth.session) {
                const path = window.location.pathname;
                if (path.startsWith('/auth/') || path === '/') {
                    goto('/app');
                    unsubscribe();
                }
            }
        });

        return () => {
            unsubscribe();
        };
    });
</script>

<div class="min-h-screen bg-background">
    <header class="sticky top-0 z-50 border-b border-border bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
        <!-- your header -->
    </header>

    <main>
        <slot />
    </main>
</div>
