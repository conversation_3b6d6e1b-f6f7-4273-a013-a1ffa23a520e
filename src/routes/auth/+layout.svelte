<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    
    // Simple layout for auth pages - no navigation, clean design
    onMount(() => {
    // Add any auth-specific initialization here
    });
</script>

<!-- Clean full-page auth layout matching the welcome page style -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
    <div class="px-4 py-12 text-center">
        <div class="max-w-sm mx-auto">
            <div class="mb-8">
                <h1 class="text-gray-900 mb-2">Your Best Days</h1>
                <p class="text-gray-600 mt-2">Your journey to better days starts here</p>
            </div>
			
            <slot />
        </div>
    </div>
    <button
        on:click={() => goto('/')}
        class="mt-auto mb-8 mx-auto px-6 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all text-blue-600 font-semibold"
    >
        ← Back to Home
    </button>
</div>