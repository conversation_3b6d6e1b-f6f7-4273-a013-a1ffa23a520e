<script lang="ts">
    import { goto } from '$app/navigation';
    import Button from '$lib/components/Button.svelte';
    import { sendSigninOtp, verifyOtp } from '$lib/auth';
	
    let email = '';
    let otpToken = '';
    let loading = false;
    let error = '';
    let success = false;
    let showOtpInput = false;
    let resendLoading = false;
    let resendCooldown = 0;
    let resendTimer: NodeJS.Timeout | null = null;
	
    async function handleSignIn() {
        if (!email) {
            error = 'Please enter your email address';
            return;
        }
		
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            error = 'Please enter a valid email address';
            return;
        }
		
        loading = true;
        error = '';
		
        try {
            await sendSigninOtp(email);
            success = true;
            showOtpInput = true;
        } catch (err: unknown) {
            console.error('Sign in error:', err);
            error = (err instanceof Error) ? err.message : 'Failed to send sign-in code. Please try again.';
        } finally {
            loading = false;
        }
    }
	
    async function handleOtpVerification() {
        if (!otpToken || otpToken.length !== 6) {
            error = 'Please enter the 6-digit code';
            return;
        }
		
        loading = true;
        error = '';
		
        try {
            await verifyOtp(email, otpToken);
            await goto('/app');
        } catch (err: unknown) {
            console.error('OTP verification error:', err);
            error = (err instanceof Error) ? err.message : 'Invalid code. Please try again.';
        } finally {
            loading = false;
        }
    }
	
    async function handleResendOtp() {
        if (resendCooldown > 0 || resendLoading) return;
		
        resendLoading = true;
        error = '';
        // Clear the existing OTP input since the old code is no longer valid
        otpToken = '';
		
        try {
            await sendSigninOtp(email);
            // Start cooldown timer (60 seconds)
            resendCooldown = 60;
            resendTimer = setInterval(() => {
                resendCooldown--;
                if (resendCooldown <= 0) {
                    if (resendTimer) {
                        clearInterval(resendTimer);
                        resendTimer = null;
                    }
                }
            }, 1000);
			
            // Show success message briefly
            error = '';
        // You could add a success state here if needed
        } catch (err: unknown) {
            console.error('Resend OTP error:', err);
            error = (err instanceof Error) ? err.message : 'Failed to resend verification code. Please try again.';
        } finally {
            resendLoading = false;
        }
    }

    function resetOtpInput() {
        otpToken = '';
        error = '';
    }
 
    function formatOtpInput(event: Event) {
        const target = event.target as HTMLInputElement;
        const value = target.value.replace(/\D/g, '').slice(0, 6);
        otpToken = value;
    }
	
    // Cleanup timer on component destroy
    import { onDestroy } from 'svelte';
    onDestroy(() => {
        if (resendTimer) {
            clearInterval(resendTimer);
        }
    });
</script>

<svelte:head>
    <title>Sign In - YourBestDays</title>
</svelte:head>

{#if success && !showOtpInput}
    <div class="text-center space-y-4">
        <div class="text-green-500 mb-4">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <h2 class="text-gray-900">Check your email</h2>
        <p class="text-gray-600">
            We've sent a 6-digit verification code to <strong>{email}</strong>
        </p>
        <p class="text-sm text-gray-500">
            Enter the code below to sign in.
        </p>
        <div class="pt-4 space-y-3">
            <Button
                on:click={() => showOtpInput = true}
                variant="default"
                size="xxl"
                class="w-full"
            >
                Enter verification code
            </Button>
            <Button
                on:click={() => { success = false; email = ''; showOtpInput = false; }}
                variant="link"
                class="text-blue-600 hover:text-blue-500"
            >
                Use a different email
            </Button>
        </div>
    </div>
{:else if showOtpInput}
    <form on:submit|preventDefault={handleOtpVerification} class="space-y-6">
        <div class="text-center mb-6">
            <h2 class="text-gray-900 mb-2">Enter verification code</h2>
            <p class="text-gray-600">
                Enter the 6-digit code sent to <strong>{email}</strong>
            </p>
        </div>
		
        {#if error}
            <div class="bg-amber-50 border border-amber-200 text-amber-800 px-4 py-3 rounded-md">
                {error}
            </div>
        {/if}
		
        <div>
            <label for="otpToken" class="block text-sm text-gray-700 mb-2">
                6-digit code
            </label>
            <input
                id="otpToken"
                type="text"
                bind:value={otpToken}
                on:input={formatOtpInput}
                required
                maxlength="6"
                inputmode="numeric"
                class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-center tracking-widest"
                placeholder="000000"
            />
            <p class="text-xs text-gray-500 mt-2">
                Check your email for the 6-digit verification code
            </p>
            <div class="flex space-x-2 mt-3">
                <Button
                    type="button"
                    on:click={resetOtpInput}
                    disabled={!otpToken}
                    variant="outline"
                    size="md"
                    class="flex-1"
                >
                    Clear code
                </Button>
            </div>
        </div>
  
        <Button
            type="submit"
            disabled={loading || otpToken.length !== 6}
            variant="default"
            size="xxl"
            class="w-full"
        >
            {loading ? 'Verifying...' : 'Verify code'}
        </Button>
		
        <div class="text-center space-y-3">
            <div class="flex flex-col items-center space-y-2">
                <p class="text-sm text-gray-600">
                    Didn't receive the code?
                </p>
                {#if resendCooldown > 0}
                    <div class="bg-gray-50 border border-gray-200 rounded-md px-4 py-2">
                        <p class="text-sm text-gray-700 font-medium">
                            Code sent! You can request another in {resendCooldown} seconds
                        </p>
                    </div>
                {:else}
                    <Button
                        type="button"
                        on:click={handleResendOtp}
                        disabled={resendLoading}
                        variant="link"
                        class="text-blue-600 hover:text-blue-500 disabled:text-gray-400"
                    >
                        {#if resendLoading}
                            Sending...
                        {:else}
                            Resend verification code
                        {/if}
                    </Button>
                {/if}
            </div>
			
            <div class="border-t border-gray-200 pt-3 space-y-2">
                <Button
                    type="button"
                    on:click={() => { showOtpInput = false; success = true; }}
                    variant="link"
                    class="text-blue-600 hover:text-blue-500"
                >
                    ← Back to email options
                </Button>
                <br>
                <Button
                    type="button"
                    on:click={() => { success = false; email = ''; showOtpInput = false; otpToken = ''; }}
                    variant="link"
                    class="text-gray-600 hover:text-gray-500"
                >
                    Use a different email
                </Button>
            </div>
        </div>
    </form>
{:else}
    <form on:submit|preventDefault={handleSignIn} class="space-y-6">
        <div>
            <h3 class="text-gray-900 mb-2">Welcome back</h3>
            <h5 class="text-gray-900 mb-2">Please login</h5>
        </div>
		
        {#if error}
            <div class="bg-amber-50 border border-amber-200 text-amber-800 px-4 py-3 rounded-md">
                {error}
            </div>
        {/if}
		
        <div>
            <label for="email" class="block text-sm text-gray-700 mb-2">
                Email address
            </label>
            <input
                id="email"
                type="email"
                bind:value={email}
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your email address"
            />
            <p class="text-xs text-gray-500 mt-2">
                We'll send you a 6-digit verification code - no password needed
            </p>
        </div>
		
        <Button
            type="submit"
            disabled={loading}
            variant="default"
            size="xxl"
            class="w-full"
        >
            {loading ? 'Sending code...' : 'Send verification code'}
        </Button>
		
        <div class="text-center">
            <p class="text-sm text-gray-600">
                Don't have an account?
                <a href="/auth/signup" class="text-blue-600 hover:text-blue-500">
                    Sign up
                </a>
            </p>
        </div>
    </form>
{/if}