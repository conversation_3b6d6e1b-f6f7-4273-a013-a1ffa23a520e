<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
	
    let loading = true;
    let error = '';
	
    onMount(async () => {
        // Since we're using OTP-only authentication (no magic links),
		// this callback page shouldn't be used. Redirect to signin.
        try {
            await goto('/auth/signin', { replaceState: true });
        } catch (err: unknown) {
            console.error('Redirect error:', err);
            error = 'Unable to redirect. Please try again.';
            loading = false;
        }
    });
</script>

<svelte:head>
    <title>Authenticating... - YourBestDays</title>
</svelte:head>

{#if loading}
    <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Signing you in...</h2>
        <p class="text-gray-600">Please wait while we complete your authentication.</p>
    </div>
{:else if error}
    <div class="text-center">
        <div class="text-red-500 mb-4">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
        </div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Authentication Error</h2>
        <p class="text-gray-600 mb-4">{error}</p>
        <button 
            on:click={() => goto('/auth/signin')}
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
            Try Again
        </button>
    </div>
{/if}