import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { waitForAuthInit } from '$lib/stores/auth';

export const ssr = false; // Disable SSR for client-side auth

export async function load() {
    if (browser) {
        // Wait for auth to initialize and check if user is already authenticated
        const authState = await waitForAuthInit();
		
        if (authState.session) {
            // User is already authenticated, redirect to home
            goto('/');
            return {};
        }
    }
	
    return {};
}