// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
import type { Session, User } from '@supabase/supabase-js'

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			session: Session | null
			user: User | null
		}
		interface PageData {
			session: Session | null
		}
		// interface PageState {}
		// interface Platform {}
	}
	
	// Declare the global variable defined by Vite
	declare const __APP_VERSION__: string;
}

// Database types for Supabase (client-side only)
export interface Database {
	public: {
		Tables: {
			profiles: {
				Row: {
					id: string
					stripe_customer_id: string | null
					email: string | null
					first_name: string | null
					last_name: string | null
					early_adopter: boolean
					wizard_completed: boolean
					created_at: string
					updated_at: string
				}
				Insert: {
					id: string
					stripe_customer_id?: string | null
					email?: string | null
					first_name?: string | null
					last_name?: string | null
					early_adopter?: boolean
					wizard_completed?: boolean
					created_at?: string
					updated_at?: string
				}
				Update: {
					id?: string
					stripe_customer_id?: string | null
					email?: string | null
					first_name?: string | null
					last_name?: string | null
					early_adopter?: boolean
					wizard_completed?: boolean
					created_at?: string
					updated_at?: string
				}
			}
			user_subscriptions: {
				Row: {
					id: string
					user_id: string | null
					subscription_id: string | null
					status: string | null
					current_period_end: string | null
					created_at: string
					updated_at: string
				}
				Insert: {
					id?: string
					user_id?: string | null
					subscription_id?: string | null
					status?: string | null
					current_period_end?: string | null
					created_at?: string
					updated_at?: string
				}
				Update: {
					id?: string
					user_id?: string | null
					subscription_id?: string | null
					status?: string | null
					current_period_end?: string | null
					created_at?: string
					updated_at?: string
				}
			}
		}
	}
}

export {};
