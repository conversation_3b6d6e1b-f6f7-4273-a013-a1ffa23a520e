// Test script to manually set up profile data with proper schedule format
const testProfile = {
    personal: { 
        preferredName: 'Test User', 
        ageRange: '25-34', 
        genderIdentity: 'prefer-not-to-say' 
    },
    preferences: { 
        theme: { name: 'blue', colors: {} } 
    },
    experiences: [
        { name: 'meditation', summary: 'I practice meditation for clarity' },
        { name: 'exercise', summary: 'Regular exercise helps me stay focused' }
    ],
    obstacles: [
        { name: 'anxiety', summary: 'I struggle with anxiety sometimes' },
        { name: 'stress', summary: 'Work stress affects my wellbeing' }
    ],
    goals: [],
    schedules: [
        {
            id: 1,
            name: 'Friday Schedule',
            selectedDays: ['Fri'],
            timeRanges: [
                {
                    id: 1,
                    startHour: 9,
                    endHour: 10,
                    maxPerHour: 2
                }
            ]
        }
    ],
    wizardCompleted: true
};

console.log('Setting up test profile with proper schedule format...');
console.log('Profile data:', JSON.stringify(testProfile, null, 2));

if (typeof localStorage !== 'undefined') {
    localStorage.setItem('profile', JSON.stringify(testProfile));
    console.log('Profile saved to localStorage');
  
    // Verify it was saved
    const saved = localStorage.getItem('profile');
    if (saved) {
        const parsed = JSON.parse(saved);
        console.log('Verified saved profile schedules:', parsed.schedules);
    }
} else {
    console.log('localStorage not available');
}