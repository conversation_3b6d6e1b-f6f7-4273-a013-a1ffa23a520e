# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Development
```bash
# Start development server
npm run dev

# Start with browser auto-open
npm run dev -- --open
```

### Building
```bash
# Build for web production
npm run build

# Build and sync for Capacitor (mobile)
npm run build-cap

# Preview production build
npm run preview
```

### Code Quality
```bash
# Run ESLint with auto-fix
npm run lint

# Type checking with svelte-check
npm run check

# Type checking in watch mode
npm run check:watch
```

### Testing
```bash
# Run unit tests with Vitest
npm run test:unit

# Run e2e tests with <PERSON><PERSON> (mobile viewport)
npm run test:e2e

# Run all tests
npm run test

# Run Storybook for component development
npm run storybook
```

## High-Level Architecture

### Application Overview
YourBestDays is a personal development and mental wellness app built with SvelteKit and Capacitor. It helps users transform their lives through mindful practices, cognitive behavioral therapy techniques, and personalized guidance.

### Technology Stack
- **Framework**: SvelteKit with TypeScript
- **Styling**: Tailwind CSS v4
- **Mobile**: Capacitor for iOS/Android
- **Backend**: Supabase (auth & data)
- **State Management**: Svelte stores
- **Build**: Vite
- **Testing**: Vitest (unit) + Playwright (e2e)

### Key Architectural Components

#### 1. **Routing Structure**
The app uses nested layouts for different UI patterns:
- `/auth/*` - Authentication flows (signin, signup, OAuth callback)
- `/app/*` - Main authenticated app
  - `(wtabnav)` - Routes with bottom tab navigation
  - `(wbackbtn)` - Routes with back button header
- `/debug/*` - Development tools (only in dev mode)

#### 2. **State Management**
Core stores in `src/lib/stores/`:
- **authStore**: Manages Supabase authentication and sessions
- **profile**: User preferences, goals, and schedules (persisted to localStorage)
- **theme**: Dark/light mode with system preference detection
- **beliefsExplorer**: Complex multi-step wizard state
- **notificationQueue**: Scheduled notifications management

#### 3. **Service Layer**
Business logic services in `src/lib/services/`:
- **notificationService**: Handles YBD message scheduling and delivery
- **messageMatcher**: Matches messages to user preferences
- **scheduleProcessor**: Generates notification time slots
- **subscriptionService**: Stripe integration for premium features

#### 4. **Mobile Integration**
Capacitor plugins used:
- **Preferences**: Native key-value storage
- **Network**: Connection status monitoring
- **Browser**: In-app browser for external links
- **SplashScreen**: Native splash screen control

#### 5. **Data Flow**
- **Local Storage**: Profile data, notification cache, theme preferences
- **Supabase**: User authentication, sessions
- **Static JSON**: YBD messages and belief explorer data
- **Audio Cache**: Offline playback support

### Development Guidelines

#### Component Structure
- Components live in `src/lib/components/`
- Use `.svelte` files with TypeScript in `<script lang="ts">`
- Follow existing patterns for props and event handling
- Prefer composition over inheritance

#### Styling
- Use Tailwind CSS classes
- Dark mode support via `dark:` prefix
- 4-space indentation (enforced by ESLint)
- Mobile-first responsive design

#### State Management
- Use Svelte stores for cross-component state
- Persist user data to localStorage for offline support
- Handle loading and error states consistently
- Clean up subscriptions in `onDestroy`

#### Mobile Considerations
- Test on actual devices via Capacitor
- Handle offline scenarios gracefully
- Use native features where appropriate
- Optimize for touch interactions

### Build Targets
- **Web**: Static site with adapter-static
- **iOS**: Capacitor iOS app (Xcode required)
- **Android**: Capacitor Android app (Android Studio required)

### Development Approach
**CRITICAL**: For any issue with SvelteKit, Capacitor, or Tailwind:
1. Search framework documentation FIRST
2. Never guess or try generic solutions
3. Ask user to clarify framework version if needed or check package.json

### Command Execution Preference
**IMPORTANT**: Never run development/build commands directly. Instead:
- Tell the user to run commands like `npm run dev`, `npm run build`, `npx cap run ios`, etc.
- Ask the user to report back with results
- This includes all npm scripts, build commands, and development server commands

### Important Files
- `capacitor.config.ts` - Mobile app configuration
- `src/lib/config/env.ts` - Environment variables
- `src/routes/+layout.ts` - Root layout and auth logic
- `src/lib/stores/profile.ts` - Core user data structure