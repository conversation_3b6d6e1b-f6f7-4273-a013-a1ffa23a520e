# sv

Everything you need to build a Svelte project, powered by [`sv`](https://github.com/sveltejs/cli).

## Creating a project

If you're seeing this, you've probably already done this step. Congrats!

```bash
# create a new project in the current directory
npx sv create

# create a new project in my-app
npx sv create my-app
```

## Developing

Once you've created a project and installed dependencies with `npm install` (or `pnpm install` or `yarn`), start a development server:

```bash
npm run dev

# or start the server and open the app in a new browser tab
npm run dev -- --open
```

## Building

To create a production version of your app:

```bash
npm run build
```

You can preview the production build with `npm run preview`.

> To deploy your app, you may need to install an [adapter](https://svelte.dev/docs/kit/adapters) for your target environment.



## Setup Plan Notes

1. Can a remote-hosted SvelteKit app still pre-render and cache pages for offline use (for iOS WebView)?

Yes — absolutely.

Here’s how:

Use SvelteKit with adapter-static to pre-render all known routes at build time.
Enable Service Worker + Workbox/PWA plugin to:
<PERSON><PERSON> visited pages
Allow offline navigation of previously loaded pages
Gracefully fail or show custom offline screen when navigating to new (uncached) routes
🔧 With proper config, the user:

Visits /page-a → it gets cached
Loses internet
Reopens app → still sees /page-a, and any other pages they previously visited
⚠️ Just be sure to handle dynamic API content separately — e.g. cache fallback JSON or mark trpc.getX.query() as stale on reconnection.


TODO

- Need to handle what happens if user uses same email after they canceled previous subscription?
- How to allow users to change email? (or how to handle manually until that feature is automatic) supabase has this feature but need to know how to tie it to stripe subscription