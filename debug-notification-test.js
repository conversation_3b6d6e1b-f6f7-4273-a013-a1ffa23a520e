// Quick debug test to check notification system
import { notificationService } from './src/lib/services/notificationService.js';

// Simulate the user's profile data
const testProfile = {
    personal: {
        preferredName: "dfgg",
        ageRange: "35-44", 
        genderIdentity: "Prefer not to say"
    },
    experiences: [
        {
            name: "Clarity",
            summary: "test summary"
        }
    ],
    obstacles: [
        {
            name: "Anxiety", 
            summary: "test summary"
        }
    ],
    schedules: [
        {
            id: 1,
            selectedDays: ["Fri"],
            timeRanges: [
                {
                    id: 1,
                    startHour: 9,
                    endHour: 10,
                    maxPerHour: 2
                }
            ]
        }
    ],
    wizardCompleted: true
};

// Get debug info
console.log('=== NOTIFICATION DEBUG INFO ===');
notificationService.getDebugInfo(testProfile).then(debugInfo => {
    console.log('Profile Hash:', debugInfo.profileHash);
    console.log('Schedule Stats:', debugInfo.scheduleStats);
    console.log('Matching Stats:', debugInfo.matchingStats);
    console.log('Cache Info:', debugInfo.cacheInfo);
    console.log('Upcoming Slots:', debugInfo.upcomingSlots);
  
    console.log('\n=== CURRENT DATE INFO ===');
    const now = new Date();
    console.log('Current time:', now.toISOString());
    console.log('Current day of week:', now.getDay()); // 0=Sunday, 1=Monday, etc.
    console.log('Friday is day:', 5);
  
    console.log('\n=== EXPECTED ISSUE ===');
    console.log('If today is Monday (1) and tomorrow is Tuesday (2),');
    console.log('then Friday (5) is not within the next 24 hours,');
    console.log('so no time slots would be generated.');
});